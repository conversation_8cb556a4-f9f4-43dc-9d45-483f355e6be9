{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/node_modules/react-dom/index.js"], "sourcesContent": ["'use strict';\n\nfunction checkDCE() {\n  /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\n  if (\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ === 'undefined' ||\n    typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE !== 'function'\n  ) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // This branch is unreachable because this function is only called\n    // in production, but the condition is true only in development.\n    // Therefore if the branch is still here, dead code elimination wasn't\n    // properly applied.\n    // Don't change the message. React DevTools relies on it. Also make sure\n    // this message doesn't occur elsewhere in this function, or it will cause\n    // a false positive.\n    throw new Error('^_^');\n  }\n  try {\n    // Verify that the code above has been dead code eliminated (DCE'd).\n    __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(checkDCE);\n  } catch (err) {\n    // DevTools shouldn't crash React, no matter what.\n    // We should still report in case we break this code.\n    console.error(err);\n  }\n}\n\nif (process.env.NODE_ENV === 'production') {\n  // DCE check should happen before ReactDOM bundle executes so that\n  // DevTools can report bad minification during injection.\n  checkDCE();\n  module.exports = require('./cjs/react-dom.production.min.js');\n} else {\n  module.exports = require('./cjs/react-dom.development.js');\n}\n"], "names": [], "mappings": "AA8BI;AA9BJ;AAEA,SAAS;IACP,yCAAyC,GACzC,IACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,QAAQ,KAAK,YACnD;QACA;IACF;IACA,wCAA2C;QACzC,kEAAkE;QAClE,gEAAgE;QAChE,sEAAsE;QACtE,oBAAoB;QACpB,wEAAwE;QACxE,0EAA0E;QAC1E,oBAAoB;QACpB,MAAM,IAAI,MAAM;IAClB;IACA,IAAI;QACF,oEAAoE;QACpE,+BAA+B,QAAQ,CAAC;IAC1C,EAAE,OAAO,KAAK;QACZ,kDAAkD;QAClD,qDAAqD;QACrD,QAAQ,KAAK,CAAC;IAChB;AACF;AAEA,uCAA2C;;AAK3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/node_modules/react-dom/client.js"], "sourcesContent": ["'use strict';\n\nvar m = require('react-dom');\nif (process.env.NODE_ENV === 'production') {\n  exports.createRoot = m.createRoot;\n  exports.hydrateRoot = m.hydrateRoot;\n} else {\n  var i = m.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n  exports.createRoot = function(c, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.createRoot(c, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n  exports.hydrateRoot = function(c, h, o) {\n    i.usingClientEntryPoint = true;\n    try {\n      return m.hydrateRoot(c, h, o);\n    } finally {\n      i.usingClientEntryPoint = false;\n    }\n  };\n}\n"], "names": [], "mappings": "AAGI;AAHJ;AAEA,IAAI;AACJ,uCAA2C;;AAG3C,OAAO;IACL,IAAI,IAAI,EAAE,kDAAkD;IAC5D,QAAQ,UAAU,GAAG,SAAS,CAAC,EAAE,CAAC;QAChC,EAAE,qBAAqB,GAAG;QAC1B,IAAI;YACF,OAAO,EAAE,UAAU,CAAC,GAAG;QACzB,SAAU;YACR,EAAE,qBAAqB,GAAG;QAC5B;IACF;IACA,QAAQ,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QACpC,EAAE,qBAAqB,GAAG;QAC1B,IAAI;YACF,OAAO,EAAE,WAAW,CAAC,GAAG,GAAG;QAC7B,SAAU;YACR,EAAE,qBAAqB,GAAG;QAC5B;IACF;AACF", "ignoreList": [0], "debugId": null}}]}