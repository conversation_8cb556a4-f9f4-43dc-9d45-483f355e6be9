{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/app/api/ollama/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { Ollama } from 'ollama';\n\nconst ollama = new Ollama({ host: 'http://localhost:11434' });\n\nexport async function GET(request: NextRequest) {\n  const { searchParams } = new URL(request.url);\n  const action = searchParams.get('action');\n\n  try {\n    switch (action) {\n      case 'list':\n        const models = await ollama.list();\n        return NextResponse.json({ models: models.models });\n\n      case 'ps':\n        const runningModels = await ollama.ps();\n        return NextResponse.json(runningModels);\n\n      case 'check':\n        await ollama.list();\n        return NextResponse.json({ connected: true });\n\n      default:\n        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Ollama API error:', error);\n    return NextResponse.json({ \n      error: 'Failed to connect to Ollama. Make sure Ollama is running on localhost:11434',\n      connected: false \n    }, { status: 500 });\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { action, model, prompt, options } = body;\n\n    switch (action) {\n      case 'generate':\n        const response = await ollama.generate({\n          model: model || 'llama3.2:3b',\n          prompt,\n          stream: false,\n          options: options || {\n            temperature: 0.7,\n            top_p: 0.9,\n            top_k: 40,\n          }\n        });\n        \n        return NextResponse.json({ content: response.response });\n\n      case 'pull':\n        // For pulling models - this would be a streaming operation\n        return NextResponse.json({ message: 'Model pull initiated' });\n\n      default:\n        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });\n    }\n  } catch (error) {\n    console.error('Ollama API error:', error);\n    return NextResponse.json({ \n      error: 'Failed to process request',\n      details: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,SAAS,IAAI,0IAAA,CAAA,SAAM,CAAC;IAAE,MAAM;AAAyB;AAEpD,eAAe,IAAI,OAAoB;IAC5C,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;IAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;IAEhC,IAAI;QACF,OAAQ;YACN,KAAK;gBACH,MAAM,SAAS,MAAM,OAAO,IAAI;gBAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,QAAQ,OAAO,MAAM;gBAAC;YAEnD,KAAK;gBACH,MAAM,gBAAgB,MAAM,OAAO,EAAE;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAE3B,KAAK;gBACH,MAAM,OAAO,IAAI;gBACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,WAAW;gBAAK;YAE7C;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;QACxE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,WAAW;QACb,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;QAE3C,OAAQ;YACN,KAAK;gBACH,MAAM,WAAW,MAAM,OAAO,QAAQ,CAAC;oBACrC,OAAO,SAAS;oBAChB;oBACA,QAAQ;oBACR,SAAS,WAAW;wBAClB,aAAa;wBACb,OAAO;wBACP,OAAO;oBACT;gBACF;gBAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,SAAS,SAAS,QAAQ;gBAAC;YAExD,KAAK;gBACH,2DAA2D;gBAC3D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,SAAS;gBAAuB;YAE7D;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAiB,GAAG;oBAAE,QAAQ;gBAAI;QACxE;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACpD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}