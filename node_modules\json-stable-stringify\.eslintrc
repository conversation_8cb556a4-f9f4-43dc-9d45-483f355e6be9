{
	"root": true,

	"extends": "@ljharb",

	"rules": {
		"consistent-return": 1,
		"max-len": 0,
		"max-lines-per-function": 0,
		"max-params": 1,
		"max-statements-per-line": [2, { "max": 2 }],
		"no-continue": 1,
		"no-negated-condition": 1,
		"no-param-reassign": 1,
		"no-redeclare": 1,
		"no-restricted-syntax": 1,
		"object-curly-newline": 0,
		"max-statements": 1,
		"operator-linebreak": 0,
		"sort-keys": 1,
	},

	"overrides": [
		{
			"files": "example/**",
			"rules": {
				"no-console": 0,
			},
		},
	],
}
