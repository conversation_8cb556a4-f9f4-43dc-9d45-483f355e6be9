{"name": "ps-tree", "version": "1.2.0", "description": "Get all children of a pid", "license": "MIT", "homepage": "http://github.com/indexzero/ps-tree#readme", "repository": "github:indexzero/ps-tree", "bugs": {"url": "https://github.com/indexzero/ps-tree/issues", "email": "<EMAIL>"}, "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/zixia)", "<PERSON> <<EMAIL>> (https://github.com/simonepri)"], "keyboards": ["ps-tree", "ps", "tree", "ppid", "pid"], "main": "index.js", "bin": {"ps-tree": "./bin/ps-tree.js"}, "files": ["bin", "index.js"], "engines": {"node": ">= 0.10"}, "scripts": {"test": "istanbul cover node_modules/tape/bin/tape test/test.js", "coverage": "cross-env CODECLIMATE_REPO_TOKEN=84436b4f13c70ace9c62e7f04928bf23c234eb212c0232d39d7fb1535beb2da5 node_modules/.bin/codeclimate-test-reporter < coverage/lcov.info"}, "dependencies": {"event-stream": "=3.3.4"}, "devDependencies": {"codeclimate-test-reporter": "^0.5.0", "cross-env": "^2.0.1", "istanbul": "^0.4.5", "tape": "^4.9.0", "tree-kill": "^1.1.0"}}