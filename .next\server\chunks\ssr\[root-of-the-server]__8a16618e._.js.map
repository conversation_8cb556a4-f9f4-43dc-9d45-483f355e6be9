{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sXACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {cn} from '@/lib/utils';\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, React.ComponentProps<'textarea'>>(\n  ({className, ...props}, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport {Textarea};\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAC,SAAS,EAAE,GAAG,OAAM,EAAE;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qTACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 465, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/ai/ollama-service.ts"], "sourcesContent": ["// Ollama service now uses API routes instead of direct imports to avoid browser compatibility issues\n\nexport interface OllamaModel {\n  name: string;\n  model: string;\n  size: number;\n  digest: string;\n  details: {\n    parent_model: string;\n    format: string;\n    family: string;\n    families: string[];\n    parameter_size: string;\n    quantization_level: string;\n  };\n  expires_at: string;\n  size_vram: number;\n}\n\nexport interface ModelUsage {\n  name: string;\n  size: string;\n  ramUsage: string;\n  status: 'running' | 'idle' | 'loading';\n  lastUsed?: Date;\n}\n\nclass OllamaService {\n  private currentModel: string = 'llama3.2:3b'; // Default model\n  private apiBase: string = '/api/ollama';\n\n  constructor() {\n    // No direct Ollama instance - using API routes\n  }\n\n  async listModels(): Promise<any[]> {\n    try {\n      const response = await fetch(`${this.apiBase}?action=list`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch models');\n      }\n      const data = await response.json();\n      return data.models || [];\n    } catch (error) {\n      console.error('Error listing models:', error);\n      throw new Error('Failed to connect to Ollama. Make sure <PERSON>lla<PERSON> is running.');\n    }\n  }\n\n  async getModelUsage(): Promise<ModelUsage[]> {\n    try {\n      const [models, runningResponse] = await Promise.all([\n        this.listModels(),\n        fetch(`${this.apiBase}?action=ps`)\n      ]);\n\n      const runningModels = runningResponse.ok ? await runningResponse.json() : { models: [] };\n\n      return models.map((model: any) => {\n        const running = runningModels.models?.find((rm: any) => rm.name === model.name);\n        return {\n          name: model.name,\n          size: this.formatBytes(model.size || 0),\n          ramUsage: running ? this.formatBytes(running.size_vram || 0) : '0 MB',\n          status: running ? 'running' as const : 'idle' as const,\n          lastUsed: running ? new Date() : undefined\n        };\n      });\n    } catch (error) {\n      console.error('Error getting model usage:', error);\n      return [];\n    }\n  }\n\n  async generateText(prompt: string, model?: string): Promise<string> {\n    const modelToUse = model || this.currentModel;\n\n    try {\n      const response = await fetch(this.apiBase, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'generate',\n          model: modelToUse,\n          prompt: prompt,\n          options: {\n            temperature: 0.7,\n            top_p: 0.9,\n            top_k: 40,\n          }\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to generate text');\n      }\n\n      const data = await response.json();\n      return data.content;\n    } catch (error) {\n      console.error('Error generating text:', error);\n      throw new Error(`Failed to generate text with model ${modelToUse}`);\n    }\n  }\n\n  async generateTextStream(\n    prompt: string,\n    onChunk: (chunk: string) => void,\n    model?: string\n  ): Promise<void> {\n    // For now, we'll use the non-streaming version and call onChunk once\n    // Streaming would require Server-Sent Events or WebSockets\n    const modelToUse = model || this.currentModel;\n\n    try {\n      const content = await this.generateText(prompt, modelToUse);\n      onChunk(content);\n    } catch (error) {\n      console.error('Error generating text stream:', error);\n      throw new Error(`Failed to generate text stream with model ${modelToUse}`);\n    }\n  }\n\n  setCurrentModel(model: string) {\n    this.currentModel = model;\n  }\n\n  getCurrentModel(): string {\n    return this.currentModel;\n  }\n\n  async isModelAvailable(model: string): Promise<boolean> {\n    try {\n      const models = await this.listModels();\n      return models.some((m: any) => m.name === model);\n    } catch (error) {\n      return false;\n    }\n  }\n\n  async pullModel(model: string, onProgress?: (progress: any) => void): Promise<void> {\n    try {\n      const response = await fetch(this.apiBase, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          action: 'pull',\n          model: model\n        })\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to pull model');\n      }\n\n      if (onProgress) {\n        onProgress({ status: 'completed' });\n      }\n    } catch (error) {\n      console.error('Error pulling model:', error);\n      throw new Error(`Failed to pull model ${model}`);\n    }\n  }\n\n  private formatBytes(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    \n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  async checkConnection(): Promise<boolean> {\n    try {\n      const response = await fetch(`${this.apiBase}?action=check`);\n      const data = await response.json();\n      return data.connected === true;\n    } catch (error) {\n      return false;\n    }\n  }\n}\n\nexport const ollamaService = new OllamaService();\n"], "names": [], "mappings": "AAAA,qGAAqG;;;;AA2BrG,MAAM;IACI,eAAuB,cAAc;IACrC,UAAkB,cAAc;IAExC,aAAc;IACZ,+CAA+C;IACjD;IAEA,MAAM,aAA6B;QACjC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAC1D,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YACA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,MAAM,IAAI,EAAE;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,MAAM,gBAAuC;QAC3C,IAAI;YACF,MAAM,CAAC,QAAQ,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAClD,IAAI,CAAC,UAAU;gBACf,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;aAClC;YAED,MAAM,gBAAgB,gBAAgB,EAAE,GAAG,MAAM,gBAAgB,IAAI,KAAK;gBAAE,QAAQ,EAAE;YAAC;YAEvF,OAAO,OAAO,GAAG,CAAC,CAAC;gBACjB,MAAM,UAAU,cAAc,MAAM,EAAE,KAAK,CAAC,KAAY,GAAG,IAAI,KAAK,MAAM,IAAI;gBAC9E,OAAO;oBACL,MAAM,MAAM,IAAI;oBAChB,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,IAAI;oBACrC,UAAU,UAAU,IAAI,CAAC,WAAW,CAAC,QAAQ,SAAS,IAAI,KAAK;oBAC/D,QAAQ,UAAU,YAAqB;oBACvC,UAAU,UAAU,IAAI,SAAS;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;IAEA,MAAM,aAAa,MAAc,EAAE,KAAc,EAAmB;QAClE,MAAM,aAAa,SAAS,IAAI,CAAC,YAAY;QAE7C,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,OAAO;oBACP,QAAQ;oBACR,SAAS;wBACP,aAAa;wBACb,OAAO;wBACP,OAAO;oBACT;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,OAAO;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,YAAY;QACpE;IACF;IAEA,MAAM,mBACJ,MAAc,EACd,OAAgC,EAChC,KAAc,EACC;QACf,qEAAqE;QACrE,2DAA2D;QAC3D,MAAM,aAAa,SAAS,IAAI,CAAC,YAAY;QAE7C,IAAI;YACF,MAAM,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ;YAChD,QAAQ;QACV,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,YAAY;QAC3E;IACF;IAEA,gBAAgB,KAAa,EAAE;QAC7B,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,kBAA0B;QACxB,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,MAAM,iBAAiB,KAAa,EAAoB;QACtD,IAAI;YACF,MAAM,SAAS,MAAM,IAAI,CAAC,UAAU;YACpC,OAAO,OAAO,IAAI,CAAC,CAAC,IAAW,EAAE,IAAI,KAAK;QAC5C,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,UAAU,KAAa,EAAE,UAAoC,EAAiB;QAClF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,IAAI,CAAC,OAAO,EAAE;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ;oBACR,OAAO;gBACT;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,YAAY;gBACd,WAAW;oBAAE,QAAQ;gBAAY;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,OAAO;QACjD;IACF;IAEQ,YAAY,KAAa,EAAU;QACzC,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;YAAM;SAAK;QAC/C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,kBAAoC;QACxC,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;YAC3D,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO,KAAK,SAAS,KAAK;QAC5B,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;AACF;AAEO,MAAM,gBAAgB,IAAI", "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/ai/flows/ollama-compose-document.ts"], "sourcesContent": ["import { ollamaService } from '../ollama-service';\n\nexport interface ComposeDocumentInput {\n  topic: string;\n  writingStyle: string;\n  culturalInflections: {\n    american: number;\n    russian: number;\n    german: number;\n    japanese: number;\n    french: number;\n  };\n}\n\nexport interface ComposeDocumentOutput {\n  content: string;\n}\n\nconst masterPrompt = `Master System Prompt: V2 with Modular Cultural Inflection\n[Instruction to LLM]: This is the updated protocol for generating text. The system has been upgraded to separate the core WRITING_STYLE from a new, independent CULTURAL_INFLECTION layer. You must now process requests by first applying the rules for the chosen WRITING_STYLE, and then filtering that output through the specified CULTURAL_INFLECTION lens(es).\n\nPart 1: Writing Style Definitions (The \"What\")\nThis section defines the fundamental structure and purpose of the text. Adhere to the selected style's CORE_OBJECTIVE, TONE, STRUCTURE, and FOCUS.\n\n[1] Analytical\nCORE_OBJECTIVE: To dissect a topic and explain its internal logic.\nTONE: Formal, objective, intellectual, detached.\nSTRUCTURE: Introduction → Analysis → Conclusion. Heavy use of subheadings and logical divisions.\nFOCUS: Breaking down complex ideas into component parts and examining relationships.\n\n[2] Instructional\nCORE_OBJECTIVE: To teach the reader how to do something or understand a process.\nTONE: Clear, direct, helpful, authoritative but approachable.\nSTRUCTURE: Step-by-step progression. Use of numbered lists, bullet points, and clear transitions.\nFOCUS: Practical application and comprehension. Anticipating reader questions and obstacles.\n\n[3] Reporting\nCORE_OBJECTIVE: To present factual information in an organized, neutral manner.\nTONE: Objective, professional, informative, unbiased.\nSTRUCTURE: Who/What/When/Where/Why/How format. Chronological or thematic organization.\nFOCUS: Accuracy, completeness, and clarity of information presentation.\n\n[4] Argumentative / Persuasive\nCORE_OBJECTIVE: To convince the reader of a particular viewpoint or course of action.\nTONE: Confident, logical, passionate but controlled, evidence-based.\nSTRUCTURE: Thesis → Evidence → Counter-argument acknowledgment → Refutation → Conclusion.\nFOCUS: Building a compelling case through logic, evidence, and emotional appeal.\n\n[5] Narrative\nCORE_OBJECTIVE: To tell a story or recount events in an engaging manner.\nTONE: Engaging, descriptive, personal, emotionally resonant.\nSTRUCTURE: Beginning → Middle → End. Character development and plot progression.\nFOCUS: Creating vivid scenes, developing characters, and maintaining reader engagement.\n\n[6] Exploratory / Reflective\nCORE_OBJECTIVE: To examine ideas, experiences, or concepts through personal reflection.\nTONE: Thoughtful, introspective, questioning, open-minded.\nSTRUCTURE: Question → Exploration → Insight. Non-linear, associative thinking patterns.\nFOCUS: Personal growth, understanding, and meaning-making through reflection.\n\n[7] Comparative\nCORE_OBJECTIVE: To examine similarities and differences between two or more subjects.\nTONE: Balanced, analytical, fair, systematic.\nSTRUCTURE: Point-by-point or subject-by-subject comparison. Clear criteria for comparison.\nFOCUS: Highlighting relationships, contrasts, and relative merits of subjects.\n\n[8] Schematic / Referential\nCORE_OBJECTIVE: To organize and present information in a systematic, reference-friendly format.\nTONE: Precise, organized, comprehensive, authoritative.\nSTRUCTURE: Hierarchical organization. Heavy use of headings, subheadings, and cross-references.\nFOCUS: Creating a comprehensive resource that can be easily navigated and referenced.\n\nPart 2: Cultural Inflection Definitions (The \"How\")\nThis section defines the communication style and cultural lens through which the writing style is expressed.\n\n[A] American\nCOMMUNICATION_STYLE: Direct, optimistic, pragmatic, results-oriented.\nCULTURAL_LENS: Emphasis on individual achievement, innovation, and forward progress.\nLINGUISTIC_MARKERS: Active voice, confident assertions, solution-focused language.\n\n[B] Russian\nCOMMUNICATION_STYLE: Philosophical, thorough, historically-conscious, intellectually rigorous.\nCULTURAL_LENS: Deep consideration of context, precedent, and long-term implications.\nLINGUISTIC_MARKERS: Complex sentence structures, historical references, theoretical frameworks.\n\n[C] German\nCOMMUNICATION_STYLE: Systematic, precise, methodical, technically detailed.\nCULTURAL_LENS: Process-oriented, risk-aware, quality-focused, engineering mindset.\nLINGUISTIC_MARKERS: Detailed explanations, systematic organization, technical precision.\n\n[D] Japanese\nCOMMUNICATION_STYLE: Respectful, consensus-building, harmony-seeking, indirect.\nCULTURAL_LENS: Group consideration, long-term thinking, respect for tradition and hierarchy.\nLINGUISTIC_MARKERS: Polite language, consideration of multiple perspectives, gradual revelation.\n\n[E] French\nCOMMUNICATION_STYLE: Elegant, intellectually sophisticated, artistically aware, debate-oriented.\nCULTURAL_LENS: Appreciation for nuance, style, and intellectual discourse.\nLINGUISTIC_MARKERS: Sophisticated vocabulary, rhetorical flourishes, cultural references.\n\nPart 3: Integration Protocol\nWhen a user makes a selection in the app, you will receive a prompt structured like this. Your task is to synthesize all parts into a single, coherent output.\n\n--- USER REQUEST ---\nWRITING_STYLE: [4] Argumentative / Persuasive\nCULTURAL_INFLECTION_1: [A] American, 50%\nCULTURAL_INFLECTION_2: [C] German, 50%\nCULTURAL_INFLECTION_3: [B] Russian, Off\nCULTURAL_INFLECTION_4: [D] Japanese, Off\nCULTURAL_INFLECTION_5: [E] French, Off\nTOPIC: The necessity of adopting nuclear fusion for energy independence.\n\n--- YOUR REQUIRED BEHAVIOR ---\nAdopt the Core Style: Argumentative. Start with a thesis, use evidence, refute counter-arguments.\nBlend the Cultures:\nUse the German focus on technical precision, risk analysis, and process to build the core evidence.\nUse the American tone of optimism, pragmatism, and a strong, confident call-to-action to frame the argument and conclusion.\nProduce the Text: The resulting text should be a logically rigorous and exhaustive argument (German) presented in a confident, forward-looking, and persuasive manner (American).`;\n\nexport async function composeDocument(input: ComposeDocumentInput): Promise<ComposeDocumentOutput> {\n  const styleMapping: {[key: string]: string} = {\n    \"Argumentative\": \"Argumentative / Persuasive\",\n    \"Exploratory\": \"Exploratory / Reflective\",\n    \"Schematic\": \"Schematic / Referential\"\n  };\n  \n  const mappedStyle = styleMapping[input.writingStyle] || input.writingStyle;\n  \n  // Build cultural inflections string\n  const culturalInflections = Object.entries(input.culturalInflections)\n    .filter(([_, value]) => value > 0)\n    .map(([key, value]) => {\n      const culturalMap: {[key: string]: string} = {\n        american: '[A] American',\n        russian: '[B] Russian', \n        german: '[C] German',\n        japanese: '[D] Japanese',\n        french: '[E] French'\n      };\n      return `${culturalMap[key]}, ${value}%`;\n    })\n    .join('\\n');\n\n  const prompt = `${masterPrompt}\n\n---\n\nNow, generate the first page of a document based on the following request.\n\nEXECUTION INSTRUCTIONS\n* Generate content for 1 page, with the page being approximately 240 words.\n* Use four spaces for the first-line indentation of each new paragraph.\n* DO NOT include a title, author information, or page numbers in your output.\n* Your response must be only the body of the paper.\n\n--- USER REQUEST ---\nWRITING_STYLE: ${mappedStyle}\n${culturalInflections}\nTOPIC: ${input.topic}\n\nGenerate the content now:`;\n\n  try {\n    const content = await ollamaService.generateText(prompt);\n    return { content };\n  } catch (error) {\n    console.error('Error in composeDocument:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAkBA,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iLAmG2J,CAAC;AAE3K,eAAe,gBAAgB,KAA2B;IAC/D,MAAM,eAAwC;QAC5C,iBAAiB;QACjB,eAAe;QACf,aAAa;IACf;IAEA,MAAM,cAAc,YAAY,CAAC,MAAM,YAAY,CAAC,IAAI,MAAM,YAAY;IAE1E,oCAAoC;IACpC,MAAM,sBAAsB,OAAO,OAAO,CAAC,MAAM,mBAAmB,EACjE,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,QAAQ,GAC/B,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAChB,MAAM,cAAuC;YAC3C,UAAU;YACV,SAAS;YACT,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;QACA,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACzC,GACC,IAAI,CAAC;IAER,MAAM,SAAS,GAAG,aAAa;;;;;;;;;;;;;eAalB,EAAE,YAAY;AAC7B,EAAE,oBAAoB;OACf,EAAE,MAAM,KAAK,CAAC;;yBAEI,CAAC;IAExB,IAAI;QACF,MAAM,UAAU,MAAM,8HAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QACjD,OAAO;YAAE;QAAQ;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/ai/flows/ollama-continue-document.ts"], "sourcesContent": ["import { ollamaService } from '../ollama-service';\n\nexport interface ContinueDocumentInput {\n  topic: string;\n  writingStyle: string;\n  context: string;\n  pagesToGenerate: number;\n  culturalInflections: {\n    american: number;\n    russian: number;\n    german: number;\n    japanese: number;\n    french: number;\n  };\n}\n\nexport interface ContinueDocumentOutput {\n  content: string;\n}\n\nconst masterPrompt = `Master System Prompt: V2 with Modular Cultural Inflection\n[Instruction to LLM]: This is the updated protocol for generating text. The system has been upgraded to separate the core WRITING_STYLE from a new, independent CULTURAL_INFLECTION layer. You must now process requests by first applying the rules for the chosen WRITING_STYLE, and then filtering that output through the specified CULTURAL_INFLECTION lens(es).\n\nPart 1: Writing Style Definitions (The \"What\")\nThis section defines the fundamental structure and purpose of the text. Adhere to the selected style's CORE_OBJECTIVE, TONE, STRUCTURE, and FOCUS.\n\n[1] Analytical\nCORE_OBJECTIVE: To dissect a topic and explain its internal logic.\nTONE: Formal, objective, intellectual, detached.\nSTRUCTURE: Introduction → Analysis → Conclusion. Heavy use of subheadings and logical divisions.\nFOCUS: Breaking down complex ideas into component parts and examining relationships.\n\n[2] Instructional\nCORE_OBJECTIVE: To teach the reader how to do something or understand a process.\nTONE: Clear, direct, helpful, authoritative but approachable.\nSTRUCTURE: Step-by-step progression. Use of numbered lists, bullet points, and clear transitions.\nFOCUS: Practical application and comprehension. Anticipating reader questions and obstacles.\n\n[3] Reporting\nCORE_OBJECTIVE: To present factual information in an organized, neutral manner.\nTONE: Objective, professional, informative, unbiased.\nSTRUCTURE: Who/What/When/Where/Why/How format. Chronological or thematic organization.\nFOCUS: Accuracy, completeness, and clarity of information presentation.\n\n[4] Argumentative / Persuasive\nCORE_OBJECTIVE: To convince the reader of a particular viewpoint or course of action.\nTONE: Confident, logical, passionate but controlled, evidence-based.\nSTRUCTURE: Thesis → Evidence → Counter-argument acknowledgment → Refutation → Conclusion.\nFOCUS: Building a compelling case through logic, evidence, and emotional appeal.\n\n[5] Narrative\nCORE_OBJECTIVE: To tell a story or recount events in an engaging manner.\nTONE: Engaging, descriptive, personal, emotionally resonant.\nSTRUCTURE: Beginning → Middle → End. Character development and plot progression.\nFOCUS: Creating vivid scenes, developing characters, and maintaining reader engagement.\n\n[6] Exploratory / Reflective\nCORE_OBJECTIVE: To examine ideas, experiences, or concepts through personal reflection.\nTONE: Thoughtful, introspective, questioning, open-minded.\nSTRUCTURE: Question → Exploration → Insight. Non-linear, associative thinking patterns.\nFOCUS: Personal growth, understanding, and meaning-making through reflection.\n\n[7] Comparative\nCORE_OBJECTIVE: To examine similarities and differences between two or more subjects.\nTONE: Balanced, analytical, fair, systematic.\nSTRUCTURE: Point-by-point or subject-by-subject comparison. Clear criteria for comparison.\nFOCUS: Highlighting relationships, contrasts, and relative merits of subjects.\n\n[8] Schematic / Referential\nCORE_OBJECTIVE: To organize and present information in a systematic, reference-friendly format.\nTONE: Precise, organized, comprehensive, authoritative.\nSTRUCTURE: Hierarchical organization. Heavy use of headings, subheadings, and cross-references.\nFOCUS: Creating a comprehensive resource that can be easily navigated and referenced.\n\nPart 2: Cultural Inflection Definitions (The \"How\")\nThis section defines the communication style and cultural lens through which the writing style is expressed.\n\n[A] American\nCOMMUNICATION_STYLE: Direct, optimistic, pragmatic, results-oriented.\nCULTURAL_LENS: Emphasis on individual achievement, innovation, and forward progress.\nLINGUISTIC_MARKERS: Active voice, confident assertions, solution-focused language.\n\n[B] Russian\nCOMMUNICATION_STYLE: Philosophical, thorough, historically-conscious, intellectually rigorous.\nCULTURAL_LENS: Deep consideration of context, precedent, and long-term implications.\nLINGUISTIC_MARKERS: Complex sentence structures, historical references, theoretical frameworks.\n\n[C] German\nCOMMUNICATION_STYLE: Systematic, precise, methodical, technically detailed.\nCULTURAL_LENS: Process-oriented, risk-aware, quality-focused, engineering mindset.\nLINGUISTIC_MARKERS: Detailed explanations, systematic organization, technical precision.\n\n[D] Japanese\nCOMMUNICATION_STYLE: Respectful, consensus-building, harmony-seeking, indirect.\nCULTURAL_LENS: Group consideration, long-term thinking, respect for tradition and hierarchy.\nLINGUISTIC_MARKERS: Polite language, consideration of multiple perspectives, gradual revelation.\n\n[E] French\nCOMMUNICATION_STYLE: Elegant, intellectually sophisticated, artistically aware, debate-oriented.\nCULTURAL_LENS: Appreciation for nuance, style, and intellectual discourse.\nLINGUISTIC_MARKERS: Sophisticated vocabulary, rhetorical flourishes, cultural references.\n\nPart 3: Integration Protocol\nWhen a user makes a selection in the app, you will receive a prompt structured like this. Your task is to synthesize all parts into a single, coherent output.\n\n--- USER REQUEST ---\nWRITING_STYLE: [4] Argumentative / Persuasive\nCULTURAL_INFLECTION_1: [A] American, 50%\nCULTURAL_INFLECTION_2: [C] German, 50%\nCULTURAL_INFLECTION_3: [B] Russian, Off\nCULTURAL_INFLECTION_4: [D] Japanese, Off\nCULTURAL_INFLECTION_5: [E] French, Off\nTOPIC: The necessity of adopting nuclear fusion for energy independence.\n\n--- YOUR REQUIRED BEHAVIOR ---\nAdopt the Core Style: Argumentative. Start with a thesis, use evidence, refute counter-arguments.\nBlend the Cultures:\nUse the German focus on technical precision, risk analysis, and process to build the core evidence.\nUse the American tone of optimism, pragmatism, and a strong, confident call-to-action to frame the argument and conclusion.\nProduce the Text: The resulting text should be a logically rigorous and exhaustive argument (German) presented in a confident, forward-looking, and persuasive manner (American).`;\n\nexport async function continueDocument(input: ContinueDocumentInput): Promise<ContinueDocumentOutput> {\n  const styleMapping: {[key: string]: string} = {\n    \"Argumentative\": \"Argumentative / Persuasive\",\n    \"Exploratory\": \"Exploratory / Reflective\",\n    \"Schematic\": \"Schematic / Referential\"\n  };\n  \n  const mappedStyle = styleMapping[input.writingStyle] || input.writingStyle;\n  \n  // Build cultural inflections string\n  const culturalInflections = Object.entries(input.culturalInflections)\n    .filter(([_, value]) => value > 0)\n    .map(([key, value]) => {\n      const culturalMap: {[key: string]: string} = {\n        american: '[A] American',\n        russian: '[B] Russian', \n        german: '[C] German',\n        japanese: '[D] Japanese',\n        french: '[E] French'\n      };\n      return `${culturalMap[key]}, ${value}%`;\n    })\n    .join('\\n');\n\n  const prompt = `${masterPrompt}\n\n---\n\nNow, continue generating the document based on the following request.\n\nEXECUTION INSTRUCTIONS\n* You are continuing a document that is already in progress. Use the following text as the context from the previous page to ensure a seamless transition: ${input.context}\n* Generate content for ${input.pagesToGenerate} additional pages, with each page being approximately 240 words.\n* Separate each new page of content with the marker \"[PAGEBREAK]\".\n* Use four spaces for the first-line indentation of each new paragraph.\n* DO NOT include a title, author information, or page numbers in your output.\n* Your response must be only the body of the paper.\n* Maintain the established tone, style, and cultural inflections.\n\n--- USER REQUEST ---\nWRITING_STYLE: ${mappedStyle}\n${culturalInflections}\nTOPIC: ${input.topic}\n\nGenerate the content now:`;\n\n  try {\n    const content = await ollamaService.generateText(prompt);\n    return { content };\n  } catch (error) {\n    console.error('Error in continueDocument:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAoBA,MAAM,eAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iLAmG2J,CAAC;AAE3K,eAAe,iBAAiB,KAA4B;IACjE,MAAM,eAAwC;QAC5C,iBAAiB;QACjB,eAAe;QACf,aAAa;IACf;IAEA,MAAM,cAAc,YAAY,CAAC,MAAM,YAAY,CAAC,IAAI,MAAM,YAAY;IAE1E,oCAAoC;IACpC,MAAM,sBAAsB,OAAO,OAAO,CAAC,MAAM,mBAAmB,EACjE,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,GAAK,QAAQ,GAC/B,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAChB,MAAM,cAAuC;YAC3C,UAAU;YACV,SAAS;YACT,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;QACA,OAAO,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;IACzC,GACC,IAAI,CAAC;IAER,MAAM,SAAS,GAAG,aAAa;;;;;;;2JAO0H,EAAE,MAAM,OAAO,CAAC;uBACpJ,EAAE,MAAM,eAAe,CAAC;;;;;;;;eAQhC,EAAE,YAAY;AAC7B,EAAE,oBAAoB;OACf,EAAE,MAAM,KAAK,CAAC;;yBAEI,CAAC;IAExB,IAAI;QACF,MAAM,UAAU,MAAM,8HAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QACjD,OAAO;YAAE;QAAQ;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1084, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1115, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1157, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/collapsible.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;AAIA,MAAM,cAAc,uKAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,uKAAA,CAAA,qBAAuC;AAElE,MAAM,qBAAqB,uKAAA,CAAA,qBAAuC", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/enhanced-sidebar.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect } from 'react';\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\";\nimport { \n  MessageSquare, \n  FileText, \n  Upload, \n  Download, \n  Settings, \n  Bot, \n  ChevronDown, \n  ChevronRight,\n  Feather,\n  FilePlus2,\n  Clock,\n  HardDrive,\n  Cpu,\n  Activity\n} from 'lucide-react';\nimport { format as formatDate } from 'date-fns';\nimport { ollamaService, type ModelUsage } from '@/ai/ollama-service';\n\nexport interface DocumentRecord {\n  id: string;\n  topic: string;\n  content: string;\n  createdAt: Date;\n  settings: any;\n}\n\nexport interface UploadedDocument {\n  id: string;\n  name: string;\n  type: 'pdf' | 'word' | 'txt';\n  size: number;\n  uploadedAt: Date;\n  content?: string;\n}\n\ninterface EnhancedSidebarProps {\n  documentHistory: DocumentRecord[];\n  uploadedDocuments: UploadedDocument[];\n  onSelectDocument: (doc: DocumentRecord) => void;\n  onNewDocument: () => void;\n  onNewChat: () => void;\n  onUploadDocument: (file: File) => void;\n  onExportDocument: (doc: DocumentRecord, format: 'pdf' | 'rtf' | 'latex' | 'txt') => void;\n  currentModel: string;\n  onModelChange: (model: string) => void;\n}\n\nexport function EnhancedSidebar({\n  documentHistory,\n  uploadedDocuments,\n  onSelectDocument,\n  onNewDocument,\n  onNewChat,\n  onUploadDocument,\n  onExportDocument,\n  currentModel,\n  onModelChange\n}: EnhancedSidebarProps) {\n  const [activeSection, setActiveSection] = useState<string>('chat');\n  const [modelsOpen, setModelsOpen] = useState(false);\n  const [documentsOpen, setDocumentsOpen] = useState(true);\n  const [uploadedDocsOpen, setUploadedDocsOpen] = useState(false);\n  const [savedDocsOpen, setSavedDocsOpen] = useState(true);\n  const [modelUsage, setModelUsage] = useState<ModelUsage[]>([]);\n  const [isLoadingModels, setIsLoadingModels] = useState(false);\n\n  useEffect(() => {\n    loadModelUsage();\n  }, []);\n\n  const loadModelUsage = async () => {\n    setIsLoadingModels(true);\n    try {\n      const usage = await ollamaService.getModelUsage();\n      setModelUsage(usage);\n    } catch (error) {\n      console.error('Failed to load model usage:', error);\n    } finally {\n      setIsLoadingModels(false);\n    }\n  };\n\n  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {\n    const file = event.target.files?.[0];\n    if (file) {\n      onUploadDocument(file);\n    }\n  };\n\n  const formatFileSize = (bytes: number): string => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  const getFileIcon = (type: string) => {\n    switch (type) {\n      case 'pdf': return '📄';\n      case 'word': return '📝';\n      case 'txt': return '📃';\n      default: return '📄';\n    }\n  };\n\n  return (\n    <aside className=\"bg-card text-card-foreground p-4 flex flex-col gap-4 border-r w-80 h-full\">\n      {/* Header */}\n      <div className=\"flex items-center gap-3\">\n        <Feather className=\"text-primary\" size={28} />\n        <h1 className=\"text-xl font-headline font-semibold\">ASCAES</h1>\n      </div>\n\n      <ScrollArea className=\"flex-1\">\n        <div className=\"space-y-4\">\n          {/* New Chat Button */}\n          <Button onClick={onNewChat} className=\"w-full justify-start\">\n            <MessageSquare className=\"mr-2 h-4 w-4\" />\n            New Chat\n          </Button>\n\n          {/* Chat Section */}\n          <div className=\"space-y-2\">\n            <Button \n              variant={activeSection === 'chat' ? 'secondary' : 'ghost'} \n              className=\"w-full justify-start\"\n              onClick={() => setActiveSection('chat')}\n            >\n              <MessageSquare className=\"mr-2 h-4 w-4\" />\n              Chat\n            </Button>\n          </div>\n\n          <Separator />\n\n          {/* Documents Section */}\n          <Collapsible open={documentsOpen} onOpenChange={setDocumentsOpen}>\n            <CollapsibleTrigger asChild>\n              <Button variant=\"ghost\" className=\"w-full justify-between\">\n                <div className=\"flex items-center\">\n                  <FileText className=\"mr-2 h-4 w-4\" />\n                  Documents\n                </div>\n                {documentsOpen ? <ChevronDown className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              </Button>\n            </CollapsibleTrigger>\n            <CollapsibleContent className=\"space-y-2 ml-4\">\n              {/* Uploaded Documents */}\n              <Collapsible open={uploadedDocsOpen} onOpenChange={setUploadedDocsOpen}>\n                <CollapsibleTrigger asChild>\n                  <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-between\">\n                    <div className=\"flex items-center\">\n                      <Upload className=\"mr-2 h-3 w-3\" />\n                      Uploaded ({uploadedDocuments.length})\n                    </div>\n                    {uploadedDocsOpen ? <ChevronDown className=\"h-3 w-3\" /> : <ChevronRight className=\"h-3 w-3\" />}\n                  </Button>\n                </CollapsibleTrigger>\n                <CollapsibleContent className=\"space-y-1 ml-4\">\n                  <div className=\"mb-2\">\n                    <input\n                      type=\"file\"\n                      accept=\".pdf,.doc,.docx,.txt\"\n                      onChange={handleFileUpload}\n                      className=\"hidden\"\n                      id=\"file-upload\"\n                    />\n                    <label htmlFor=\"file-upload\">\n                      <Button variant=\"outline\" size=\"sm\" className=\"w-full cursor-pointer\">\n                        <Upload className=\"mr-2 h-3 w-3\" />\n                        Upload File\n                      </Button>\n                    </label>\n                  </div>\n                  {uploadedDocuments.map((doc) => (\n                    <div key={doc.id} className=\"p-2 rounded border text-xs\">\n                      <div className=\"flex items-center justify-between\">\n                        <span className=\"flex items-center\">\n                          <span className=\"mr-1\">{getFileIcon(doc.type)}</span>\n                          {doc.name}\n                        </span>\n                      </div>\n                      <div className=\"text-muted-foreground mt-1\">\n                        {formatFileSize(doc.size)} • {formatDate(doc.uploadedAt, 'MMM dd, yyyy')}\n                      </div>\n                    </div>\n                  ))}\n                </CollapsibleContent>\n              </Collapsible>\n\n              {/* Saved Documents */}\n              <Collapsible open={savedDocsOpen} onOpenChange={setSavedDocsOpen}>\n                <CollapsibleTrigger asChild>\n                  <Button variant=\"ghost\" size=\"sm\" className=\"w-full justify-between\">\n                    <div className=\"flex items-center\">\n                      <FileText className=\"mr-2 h-3 w-3\" />\n                      Saved ({documentHistory.length})\n                    </div>\n                    {savedDocsOpen ? <ChevronDown className=\"h-3 w-3\" /> : <ChevronRight className=\"h-3 w-3\" />}\n                  </Button>\n                </CollapsibleTrigger>\n                <CollapsibleContent className=\"space-y-1 ml-4\">\n                  <Button onClick={onNewDocument} size=\"sm\" className=\"w-full mb-2\">\n                    <FilePlus2 className=\"mr-2 h-3 w-3\" />\n                    New Document\n                  </Button>\n                  {documentHistory.slice(0, 10).map((doc) => (\n                    <div key={doc.id} className=\"group\">\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"w-full justify-start text-left p-2 h-auto\"\n                        onClick={() => onSelectDocument(doc)}\n                      >\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"text-xs font-medium truncate\">\n                            {doc.topic}\n                          </div>\n                          <div className=\"text-xs text-muted-foreground flex items-center mt-1\">\n                            <Clock className=\"mr-1 h-3 w-3\" />\n                            {formatDate(doc.createdAt, 'MMM dd, HH:mm')}\n                          </div>\n                        </div>\n                      </Button>\n                      <div className=\"flex gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                        {['pdf', 'rtf', 'latex', 'txt'].map((format) => (\n                          <Button\n                            key={format}\n                            variant=\"outline\"\n                            size=\"sm\"\n                            className=\"h-6 px-2 text-xs\"\n                            onClick={(e) => {\n                              e.stopPropagation();\n                              onExportDocument(doc, format as any);\n                            }}\n                          >\n                            {format.toUpperCase()}\n                          </Button>\n                        ))}\n                      </div>\n                    </div>\n                  ))}\n                </CollapsibleContent>\n              </Collapsible>\n            </CollapsibleContent>\n          </Collapsible>\n\n          <Separator />\n\n          {/* Models Section */}\n          <Collapsible open={modelsOpen} onOpenChange={setModelsOpen}>\n            <CollapsibleTrigger asChild>\n              <Button variant=\"ghost\" className=\"w-full justify-between\">\n                <div className=\"flex items-center\">\n                  <Bot className=\"mr-2 h-4 w-4\" />\n                  Models\n                </div>\n                {modelsOpen ? <ChevronDown className=\"h-4 w-4\" /> : <ChevronRight className=\"h-4 w-4\" />}\n              </Button>\n            </CollapsibleTrigger>\n            <CollapsibleContent className=\"space-y-2 ml-4\">\n              <Button onClick={loadModelUsage} size=\"sm\" variant=\"outline\" className=\"w-full\">\n                <Activity className=\"mr-2 h-3 w-3\" />\n                Refresh\n              </Button>\n              {isLoadingModels ? (\n                <div className=\"text-xs text-muted-foreground\">Loading models...</div>\n              ) : (\n                modelUsage.map((model) => (\n                  <div key={model.name} className=\"p-2 rounded border text-xs space-y-2\">\n                    <div className=\"flex items-center justify-between\">\n                      <span className=\"font-medium\">{model.name}</span>\n                      <Badge variant={model.status === 'running' ? 'default' : 'secondary'}>\n                        {model.status}\n                      </Badge>\n                    </div>\n                    <div className=\"space-y-1\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"flex items-center\">\n                          <HardDrive className=\"mr-1 h-3 w-3\" />\n                          Size:\n                        </span>\n                        <span>{model.size}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"flex items-center\">\n                          <Cpu className=\"mr-1 h-3 w-3\" />\n                          RAM:\n                        </span>\n                        <span>{model.ramUsage}</span>\n                      </div>\n                      {model.lastUsed && (\n                        <div className=\"flex justify-between\">\n                          <span className=\"flex items-center\">\n                            <Clock className=\"mr-1 h-3 w-3\" />\n                            Last used:\n                          </span>\n                          <span>{formatDate(model.lastUsed, 'HH:mm')}</span>\n                        </div>\n                      )}\n                    </div>\n                    <Button\n                      size=\"sm\"\n                      variant={currentModel === model.name ? 'default' : 'outline'}\n                      className=\"w-full\"\n                      onClick={() => onModelChange(model.name)}\n                    >\n                      {currentModel === model.name ? 'Current' : 'Select'}\n                    </Button>\n                  </div>\n                ))\n              )}\n            </CollapsibleContent>\n          </Collapsible>\n\n          <Separator />\n\n          {/* Settings Section */}\n          <Button \n            variant={activeSection === 'settings' ? 'secondary' : 'ghost'} \n            className=\"w-full justify-start\"\n            onClick={() => setActiveSection('settings')}\n          >\n            <Settings className=\"mr-2 h-4 w-4\" />\n            Settings\n          </Button>\n        </div>\n      </ScrollArea>\n    </aside>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAgBA;AACA;AA1BA;;;;;;;;;;;AAyDO,SAAS,gBAAgB,EAC9B,eAAe,EACf,iBAAiB,EACjB,gBAAgB,EAChB,aAAa,EACb,SAAS,EACT,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,aAAa,EACQ;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,mBAAmB;QACnB,IAAI;YACF,MAAM,QAAQ,MAAM,8HAAA,CAAA,gBAAa,CAAC,aAAa;YAC/C,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;QACpC,IAAI,MAAM;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,8OAAC;QAAM,WAAU;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wMAAA,CAAA,UAAO;wBAAC,WAAU;wBAAe,MAAM;;;;;;kCACxC,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;;;;;;;0BAGtD,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAW,WAAU;;8CACpC,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAK5C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS,kBAAkB,SAAS,cAAc;gCAClD,WAAU;gCACV,SAAS,IAAM,iBAAiB;;kDAEhC,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;sCAK9C,8OAAC,qIAAA,CAAA,YAAS;;;;;sCAGV,8OAAC,uIAAA,CAAA,cAAW;4BAAC,MAAM;4BAAe,cAAc;;8CAC9C,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,OAAO;8CACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGtC,8BAAgB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAAe,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGnF,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAE5B,8OAAC,uIAAA,CAAA,cAAW;4CAAC,MAAM;4CAAkB,cAAc;;8DACjD,8OAAC,uIAAA,CAAA,qBAAkB;oDAAC,OAAO;8DACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;;0EAC1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,sMAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;oEACxB,kBAAkB,MAAM;oEAAC;;;;;;;4DAErC,iCAAmB,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAAe,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGtF,8OAAC,uIAAA,CAAA,qBAAkB;oDAAC,WAAU;;sEAC5B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,MAAK;oEACL,QAAO;oEACP,UAAU;oEACV,WAAU;oEACV,IAAG;;;;;;8EAEL,8OAAC;oEAAM,SAAQ;8EACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAU,MAAK;wEAAK,WAAU;;0FAC5C,8OAAC,sMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;;;;;;;;;;;;wDAKxC,kBAAkB,GAAG,CAAC,CAAC,oBACtB,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAK,WAAU;;8FACd,8OAAC;oFAAK,WAAU;8FAAQ,YAAY,IAAI,IAAI;;;;;;gFAC3C,IAAI,IAAI;;;;;;;;;;;;kFAGb,8OAAC;wEAAI,WAAU;;4EACZ,eAAe,IAAI,IAAI;4EAAE;4EAAI,CAAA,GAAA,sJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,UAAU,EAAE;;;;;;;;+DARnD,IAAI,EAAE;;;;;;;;;;;;;;;;;sDAgBtB,8OAAC,uIAAA,CAAA,cAAW;4CAAC,MAAM;4CAAe,cAAc;;8DAC9C,8OAAC,uIAAA,CAAA,qBAAkB;oDAAC,OAAO;8DACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAQ,MAAK;wDAAK,WAAU;;0EAC1C,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;oEAC7B,gBAAgB,MAAM;oEAAC;;;;;;;4DAEhC,8BAAgB,8OAAC,oNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;qFAAe,8OAAC,sNAAA,CAAA,eAAY;gEAAC,WAAU;;;;;;;;;;;;;;;;;8DAGnF,8OAAC,uIAAA,CAAA,qBAAkB;oDAAC,WAAU;;sEAC5B,8OAAC,kIAAA,CAAA,SAAM;4DAAC,SAAS;4DAAe,MAAK;4DAAK,WAAU;;8EAClD,8OAAC,oNAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;wDAGvC,gBAAgB,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,oBACjC,8OAAC;gEAAiB,WAAU;;kFAC1B,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,iBAAiB;kFAEhC,cAAA,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;8FACZ,IAAI,KAAK;;;;;;8FAEZ,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,oMAAA,CAAA,QAAK;4FAAC,WAAU;;;;;;wFAChB,CAAA,GAAA,sJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,SAAS,EAAE;;;;;;;;;;;;;;;;;;kFAIjC,8OAAC;wEAAI,WAAU;kFACZ;4EAAC;4EAAO;4EAAO;4EAAS;yEAAM,CAAC,GAAG,CAAC,CAAC,uBACnC,8OAAC,kIAAA,CAAA,SAAM;gFAEL,SAAQ;gFACR,MAAK;gFACL,WAAU;gFACV,SAAS,CAAC;oFACR,EAAE,eAAe;oFACjB,iBAAiB,KAAK;gFACxB;0FAEC,OAAO,WAAW;+EATd;;;;;;;;;;;+DApBH,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAwC1B,8OAAC,qIAAA,CAAA,YAAS;;;;;sCAGV,8OAAC,uIAAA,CAAA,cAAW;4BAAC,MAAM;4BAAY,cAAc;;8CAC3C,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,OAAO;8CACzB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;;0DAChC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGjC,2BAAa,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAAe,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGhF,8OAAC,uIAAA,CAAA,qBAAkB;oCAAC,WAAU;;sDAC5B,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAgB,MAAK;4CAAK,SAAQ;4CAAU,WAAU;;8DACrE,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;wCAGtC,gCACC,8OAAC;4CAAI,WAAU;sDAAgC;;;;;mDAE/C,WAAW,GAAG,CAAC,CAAC,sBACd,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAe,MAAM,IAAI;;;;;;0EACzC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAS,MAAM,MAAM,KAAK,YAAY,YAAY;0EACtD,MAAM,MAAM;;;;;;;;;;;;kEAGjB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,gNAAA,CAAA,YAAS;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGxC,8OAAC;kFAAM,MAAM,IAAI;;;;;;;;;;;;0EAEnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGlC,8OAAC;kFAAM,MAAM,QAAQ;;;;;;;;;;;;4DAEtB,MAAM,QAAQ,kBACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,oMAAA,CAAA,QAAK;gFAAC,WAAU;;;;;;4EAAiB;;;;;;;kFAGpC,8OAAC;kFAAM,CAAA,GAAA,sJAAA,CAAA,SAAU,AAAD,EAAE,MAAM,QAAQ,EAAE;;;;;;;;;;;;;;;;;;kEAIxC,8OAAC,kIAAA,CAAA,SAAM;wDACL,MAAK;wDACL,SAAS,iBAAiB,MAAM,IAAI,GAAG,YAAY;wDACnD,WAAU;wDACV,SAAS,IAAM,cAAc,MAAM,IAAI;kEAEtC,iBAAiB,MAAM,IAAI,GAAG,YAAY;;;;;;;+CAtCrC,MAAM,IAAI;;;;;;;;;;;;;;;;;sCA8C5B,8OAAC,qIAAA,CAAA,YAAS;;;;;sCAGV,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,kBAAkB,aAAa,cAAc;4BACtD,WAAU;4BACV,SAAS,IAAM,iBAAiB;;8CAEhC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/lib/document-upload.ts"], "sourcesContent": ["export interface UploadedDocument {\n  id: string;\n  name: string;\n  type: 'pdf' | 'word' | 'txt';\n  size: number;\n  uploadedAt: Date;\n  content?: string;\n}\n\nexport class DocumentUploadService {\n  private static instance: DocumentUploadService;\n  private uploadedDocuments: UploadedDocument[] = [];\n\n  static getInstance(): DocumentUploadService {\n    if (!DocumentUploadService.instance) {\n      DocumentUploadService.instance = new DocumentUploadService();\n    }\n    return DocumentUploadService.instance;\n  }\n\n  async uploadDocument(file: File): Promise<UploadedDocument> {\n    const id = this.generateId();\n    const type = this.getFileType(file.name);\n    \n    let content: string | undefined;\n    \n    try {\n      if (type === 'txt') {\n        content = await this.readTextFile(file);\n      } else if (type === 'pdf') {\n        // For PDF files, we'll need a PDF parser library\n        // For now, we'll just store the file info without content\n        content = undefined;\n      } else if (type === 'word') {\n        // For Word files, we'll need a Word parser library\n        // For now, we'll just store the file info without content\n        content = undefined;\n      }\n    } catch (error) {\n      console.error('Error reading file content:', error);\n    }\n\n    const uploadedDoc: UploadedDocument = {\n      id,\n      name: file.name,\n      type,\n      size: file.size,\n      uploadedAt: new Date(),\n      content\n    };\n\n    this.uploadedDocuments.unshift(uploadedDoc); // Add to beginning for most recent first\n    this.saveToLocalStorage();\n    \n    return uploadedDoc;\n  }\n\n  getUploadedDocuments(): UploadedDocument[] {\n    return [...this.uploadedDocuments];\n  }\n\n  deleteDocument(id: string): boolean {\n    const index = this.uploadedDocuments.findIndex(doc => doc.id === id);\n    if (index !== -1) {\n      this.uploadedDocuments.splice(index, 1);\n      this.saveToLocalStorage();\n      return true;\n    }\n    return false;\n  }\n\n  getDocument(id: string): UploadedDocument | undefined {\n    return this.uploadedDocuments.find(doc => doc.id === id);\n  }\n\n  private getFileType(filename: string): 'pdf' | 'word' | 'txt' {\n    const extension = filename.toLowerCase().split('.').pop();\n    \n    switch (extension) {\n      case 'pdf':\n        return 'pdf';\n      case 'doc':\n      case 'docx':\n        return 'word';\n      case 'txt':\n        return 'txt';\n      default:\n        return 'txt'; // Default to txt for unknown types\n    }\n  }\n\n  private async readTextFile(file: File): Promise<string> {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader();\n      \n      reader.onload = (event) => {\n        const content = event.target?.result as string;\n        resolve(content);\n      };\n      \n      reader.onerror = () => {\n        reject(new Error('Failed to read file'));\n      };\n      \n      reader.readAsText(file);\n    });\n  }\n\n  private generateId(): string {\n    return Date.now().toString(36) + Math.random().toString(36).substr(2);\n  }\n\n  private saveToLocalStorage(): void {\n    try {\n      localStorage.setItem('uploadedDocuments', JSON.stringify(this.uploadedDocuments));\n    } catch (error) {\n      console.error('Failed to save uploaded documents to localStorage:', error);\n    }\n  }\n\n  loadFromLocalStorage(): void {\n    try {\n      const stored = localStorage.getItem('uploadedDocuments');\n      if (stored) {\n        const parsed = JSON.parse(stored);\n        this.uploadedDocuments = parsed.map((doc: any) => ({\n          ...doc,\n          uploadedAt: new Date(doc.uploadedAt)\n        }));\n      }\n    } catch (error) {\n      console.error('Failed to load uploaded documents from localStorage:', error);\n      this.uploadedDocuments = [];\n    }\n  }\n\n  // Initialize the service\n  init(): void {\n    this.loadFromLocalStorage();\n  }\n}\n\n// Export singleton instance\nexport const documentUploadService = DocumentUploadService.getInstance();\n"], "names": [], "mappings": ";;;;AASO,MAAM;IACX,OAAe,SAAgC;IACvC,oBAAwC,EAAE,CAAC;IAEnD,OAAO,cAAqC;QAC1C,IAAI,CAAC,sBAAsB,QAAQ,EAAE;YACnC,sBAAsB,QAAQ,GAAG,IAAI;QACvC;QACA,OAAO,sBAAsB,QAAQ;IACvC;IAEA,MAAM,eAAe,IAAU,EAA6B;QAC1D,MAAM,KAAK,IAAI,CAAC,UAAU;QAC1B,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI;QAEvC,IAAI;QAEJ,IAAI;YACF,IAAI,SAAS,OAAO;gBAClB,UAAU,MAAM,IAAI,CAAC,YAAY,CAAC;YACpC,OAAO,IAAI,SAAS,OAAO;gBACzB,iDAAiD;gBACjD,0DAA0D;gBAC1D,UAAU;YACZ,OAAO,IAAI,SAAS,QAAQ;gBAC1B,mDAAmD;gBACnD,0DAA0D;gBAC1D,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;QAEA,MAAM,cAAgC;YACpC;YACA,MAAM,KAAK,IAAI;YACf;YACA,MAAM,KAAK,IAAI;YACf,YAAY,IAAI;YAChB;QACF;QAEA,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,cAAc,yCAAyC;QACtF,IAAI,CAAC,kBAAkB;QAEvB,OAAO;IACT;IAEA,uBAA2C;QACzC,OAAO;eAAI,IAAI,CAAC,iBAAiB;SAAC;IACpC;IAEA,eAAe,EAAU,EAAW;QAClC,MAAM,QAAQ,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG;YAChB,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,OAAO;YACrC,IAAI,CAAC,kBAAkB;YACvB,OAAO;QACT;QACA,OAAO;IACT;IAEA,YAAY,EAAU,EAAgC;QACpD,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACvD;IAEQ,YAAY,QAAgB,EAA0B;QAC5D,MAAM,YAAY,SAAS,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG;QAEvD,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO,OAAO,mCAAmC;QACrD;IACF;IAEA,MAAc,aAAa,IAAU,EAAmB;QACtD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,MAAM,SAAS,IAAI;YAEnB,OAAO,MAAM,GAAG,CAAC;gBACf,MAAM,UAAU,MAAM,MAAM,EAAE;gBAC9B,QAAQ;YACV;YAEA,OAAO,OAAO,GAAG;gBACf,OAAO,IAAI,MAAM;YACnB;YAEA,OAAO,UAAU,CAAC;QACpB;IACF;IAEQ,aAAqB;QAC3B,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;IACrE;IAEQ,qBAA2B;QACjC,IAAI;YACF,aAAa,OAAO,CAAC,qBAAqB,KAAK,SAAS,CAAC,IAAI,CAAC,iBAAiB;QACjF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;QACtE;IACF;IAEA,uBAA6B;QAC3B,IAAI;YACF,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,IAAI,CAAC,iBAAiB,GAAG,OAAO,GAAG,CAAC,CAAC,MAAa,CAAC;wBACjD,GAAG,GAAG;wBACN,YAAY,IAAI,KAAK,IAAI,UAAU;oBACrC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wDAAwD;YACtE,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC7B;IACF;IAEA,yBAAyB;IACzB,OAAa;QACX,IAAI,CAAC,oBAAoB;IAC3B;AACF;AAGO,MAAM,wBAAwB,sBAAsB,WAAW", "debugId": null}}, {"offset": {"line": 2171, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/lib/document-export.ts"], "sourcesContent": ["import jsPDF from 'jspdf';\nimport html2canvas from 'html2canvas';\n\nexport interface DocumentRecord {\n  id: string;\n  topic: string;\n  content: string;\n  createdAt: Date;\n  settings: any;\n}\n\nexport class DocumentExportService {\n  static async exportAsPDF(doc: DocumentRecord): Promise<void> {\n    try {\n      // Create a temporary div with the document content\n      const tempDiv = document.createElement('div');\n      tempDiv.style.position = 'absolute';\n      tempDiv.style.left = '-9999px';\n      tempDiv.style.width = '8.5in';\n      tempDiv.style.padding = '1in';\n      tempDiv.style.fontFamily = 'Times New Roman, serif';\n      tempDiv.style.fontSize = '12pt';\n      tempDiv.style.lineHeight = '1.6';\n      tempDiv.style.backgroundColor = 'white';\n      tempDiv.style.color = 'black';\n      \n      // Format content for PDF\n      const formattedContent = this.formatContentForPDF(doc);\n      tempDiv.innerHTML = formattedContent;\n      \n      document.body.appendChild(tempDiv);\n      \n      // Convert to canvas and then PDF\n      const canvas = await html2canvas(tempDiv, {\n        scale: 2,\n        useCORS: true,\n        backgroundColor: '#ffffff'\n      });\n      \n      document.body.removeChild(tempDiv);\n      \n      const imgData = canvas.toDataURL('image/png');\n      const pdf = new jsPDF('p', 'mm', 'a4');\n      const imgWidth = 210;\n      const pageHeight = 295;\n      const imgHeight = (canvas.height * imgWidth) / canvas.width;\n      let heightLeft = imgHeight;\n      \n      let position = 0;\n      \n      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n      heightLeft -= pageHeight;\n      \n      while (heightLeft >= 0) {\n        position = heightLeft - imgHeight;\n        pdf.addPage();\n        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);\n        heightLeft -= pageHeight;\n      }\n      \n      pdf.save(`${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`);\n    } catch (error) {\n      console.error('Error exporting PDF:', error);\n      throw new Error('Failed to export as PDF');\n    }\n  }\n\n  static exportAsRTF(doc: DocumentRecord): void {\n    try {\n      const rtfContent = this.convertToRTF(doc);\n      const blob = new Blob([rtfContent], { type: 'application/rtf' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.rtf`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error exporting RTF:', error);\n      throw new Error('Failed to export as RTF');\n    }\n  }\n\n  static exportAsLaTeX(doc: DocumentRecord): void {\n    try {\n      const latexContent = this.convertToLaTeX(doc);\n      const blob = new Blob([latexContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.tex`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error exporting LaTeX:', error);\n      throw new Error('Failed to export as LaTeX');\n    }\n  }\n\n  static exportAsText(doc: DocumentRecord): void {\n    try {\n      const textContent = this.convertToPlainText(doc);\n      const blob = new Blob([textContent], { type: 'text/plain' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n    } catch (error) {\n      console.error('Error exporting text:', error);\n      throw new Error('Failed to export as text');\n    }\n  }\n\n  private static formatContentForPDF(doc: DocumentRecord): string {\n    const header = `\n      <div style=\"text-align: center; margin-bottom: 30px;\">\n        <h1 style=\"font-size: 16pt; margin: 0;\">${doc.topic}</h1>\n        <p style=\"margin: 10px 0; font-size: 12pt;\">\n          ${doc.settings?.studentName || 'Student Name'}<br>\n          ${doc.settings?.professorName || 'Professor Name'}<br>\n          ${doc.settings?.courseDetails || 'Course Details'}<br>\n          ${new Date(doc.createdAt).toLocaleDateString()}\n        </p>\n      </div>\n    `;\n    \n    const content = doc.content\n      .replace(/\\[PAGEBREAK\\]/g, '<div style=\"page-break-before: always;\"></div>')\n      .replace(/\\n\\n/g, '</p><p style=\"text-indent: 0.5in; margin: 0; margin-bottom: 12pt;\">')\n      .replace(/\\n/g, '<br>');\n    \n    return `${header}<p style=\"text-indent: 0.5in; margin: 0; margin-bottom: 12pt;\">${content}</p>`;\n  }\n\n  private static convertToRTF(doc: DocumentRecord): string {\n    const header = `{\\\\rtf1\\\\ansi\\\\deff0 {\\\\fonttbl {\\\\f0 Times New Roman;}}\n\\\\f0\\\\fs24 \\\\qc ${doc.topic}\\\\par\n\\\\ql ${doc.settings?.studentName || 'Student Name'}\\\\par\n${doc.settings?.professorName || 'Professor Name'}\\\\par\n${doc.settings?.courseDetails || 'Course Details'}\\\\par\n${new Date(doc.createdAt).toLocaleDateString()}\\\\par\\\\par\n`;\n    \n    const content = doc.content\n      .replace(/\\[PAGEBREAK\\]/g, '\\\\page ')\n      .replace(/\\n\\n/g, '\\\\par ')\n      .replace(/\\n/g, '\\\\line ')\n      .replace(/[{}\\\\]/g, '\\\\$&');\n    \n    return `${header}${content}\\\\par}`;\n  }\n\n  private static convertToLaTeX(doc: DocumentRecord): string {\n    const header = `\\\\documentclass[12pt]{article}\n\\\\usepackage[margin=1in]{geometry}\n\\\\usepackage{setspace}\n\\\\doublespacing\n\\\\title{${doc.topic.replace(/[&%$#_{}~^\\\\]/g, '\\\\$&')}}\n\\\\author{${(doc.settings?.studentName || 'Student Name').replace(/[&%$#_{}~^\\\\]/g, '\\\\$&')}}\n\\\\date{${new Date(doc.createdAt).toLocaleDateString()}}\n\\\\begin{document}\n\\\\maketitle\n`;\n    \n    const content = doc.content\n      .replace(/\\[PAGEBREAK\\]/g, '\\\\newpage\\n')\n      .replace(/\\n\\n/g, '\\n\\n\\\\par ')\n      .replace(/[&%$#_{}~^\\\\]/g, '\\\\$&');\n    \n    return `${header}\\n${content}\\n\\n\\\\end{document}`;\n  }\n\n  private static convertToPlainText(doc: DocumentRecord): string {\n    const header = `${doc.topic}\n\n${doc.settings?.studentName || 'Student Name'}\n${doc.settings?.professorName || 'Professor Name'}\n${doc.settings?.courseDetails || 'Course Details'}\n${new Date(doc.createdAt).toLocaleDateString()}\n\n`;\n    \n    const content = doc.content\n      .replace(/\\[PAGEBREAK\\]/g, '\\n\\n--- Page Break ---\\n\\n')\n      .replace(/\\n\\n/g, '\\n\\n    '); // Add indentation for paragraphs\n    \n    return `${header}    ${content}`;\n  }\n}\n\nexport const documentExportService = DocumentExportService;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAUO,MAAM;IACX,aAAa,YAAY,GAAmB,EAAiB;QAC3D,IAAI;YACF,mDAAmD;YACnD,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,KAAK,CAAC,QAAQ,GAAG;YACzB,QAAQ,KAAK,CAAC,IAAI,GAAG;YACrB,QAAQ,KAAK,CAAC,KAAK,GAAG;YACtB,QAAQ,KAAK,CAAC,OAAO,GAAG;YACxB,QAAQ,KAAK,CAAC,UAAU,GAAG;YAC3B,QAAQ,KAAK,CAAC,QAAQ,GAAG;YACzB,QAAQ,KAAK,CAAC,UAAU,GAAG;YAC3B,QAAQ,KAAK,CAAC,eAAe,GAAG;YAChC,QAAQ,KAAK,CAAC,KAAK,GAAG;YAEtB,yBAAyB;YACzB,MAAM,mBAAmB,IAAI,CAAC,mBAAmB,CAAC;YAClD,QAAQ,SAAS,GAAG;YAEpB,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,iCAAiC;YACjC,MAAM,SAAS,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,SAAS;gBACxC,OAAO;gBACP,SAAS;gBACT,iBAAiB;YACnB;YAEA,SAAS,IAAI,CAAC,WAAW,CAAC;YAE1B,MAAM,UAAU,OAAO,SAAS,CAAC;YACjC,MAAM,MAAM,IAAI,mJAAA,CAAA,UAAK,CAAC,KAAK,MAAM;YACjC,MAAM,WAAW;YACjB,MAAM,aAAa;YACnB,MAAM,YAAY,AAAC,OAAO,MAAM,GAAG,WAAY,OAAO,KAAK;YAC3D,IAAI,aAAa;YAEjB,IAAI,WAAW;YAEf,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;YACpD,cAAc;YAEd,MAAO,cAAc,EAAG;gBACtB,WAAW,aAAa;gBACxB,IAAI,OAAO;gBACX,IAAI,QAAQ,CAAC,SAAS,OAAO,GAAG,UAAU,UAAU;gBACpD,cAAc;YAChB;YAEA,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;QACvE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,YAAY,GAAmB,EAAQ;QAC5C,IAAI;YACF,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC;YACrC,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAkB;YAC9D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;YACzE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,cAAc,GAAmB,EAAQ;QAC9C,IAAI;YACF,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC;YACzC,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAa,EAAE;gBAAE,MAAM;YAAa;YAC3D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;YACzE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAO,aAAa,GAAmB,EAAQ;QAC7C,IAAI;YACF,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC;YAC5C,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAY,EAAE;gBAAE,MAAM;YAAa;YAC1D,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,KAAK,WAAW,GAAG,IAAI,CAAC;YACzE,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM,IAAI,MAAM;QAClB;IACF;IAEA,OAAe,oBAAoB,GAAmB,EAAU;QAC9D,MAAM,SAAS,CAAC;;gDAE4B,EAAE,IAAI,KAAK,CAAC;;UAElD,EAAE,IAAI,QAAQ,EAAE,eAAe,eAAe;UAC9C,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;UAClD,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;UAClD,EAAE,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB,GAAG;;;IAGrD,CAAC;QAED,MAAM,UAAU,IAAI,OAAO,CACxB,OAAO,CAAC,kBAAkB,kDAC1B,OAAO,CAAC,SAAS,uEACjB,OAAO,CAAC,OAAO;QAElB,OAAO,GAAG,OAAO,+DAA+D,EAAE,QAAQ,IAAI,CAAC;IACjG;IAEA,OAAe,aAAa,GAAmB,EAAU;QACvD,MAAM,SAAS,CAAC;gBACJ,EAAE,IAAI,KAAK,CAAC;KACvB,EAAE,IAAI,QAAQ,EAAE,eAAe,eAAe;AACnD,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;AAClD,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;AAClD,EAAE,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB,GAAG;AAC/C,CAAC;QAEG,MAAM,UAAU,IAAI,OAAO,CACxB,OAAO,CAAC,kBAAkB,WAC1B,OAAO,CAAC,SAAS,UACjB,OAAO,CAAC,OAAO,WACf,OAAO,CAAC,WAAW;QAEtB,OAAO,GAAG,SAAS,QAAQ,MAAM,CAAC;IACpC;IAEA,OAAe,eAAe,GAAmB,EAAU;QACzD,MAAM,SAAS,CAAC;;;;QAIZ,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,kBAAkB,QAAQ;SAC7C,EAAE,CAAC,IAAI,QAAQ,EAAE,eAAe,cAAc,EAAE,OAAO,CAAC,kBAAkB,QAAQ;OACpF,EAAE,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB,GAAG;;;AAGtD,CAAC;QAEG,MAAM,UAAU,IAAI,OAAO,CACxB,OAAO,CAAC,kBAAkB,eAC1B,OAAO,CAAC,SAAS,cACjB,OAAO,CAAC,kBAAkB;QAE7B,OAAO,GAAG,OAAO,EAAE,EAAE,QAAQ,mBAAmB,CAAC;IACnD;IAEA,OAAe,mBAAmB,GAAmB,EAAU;QAC7D,MAAM,SAAS,GAAG,IAAI,KAAK,CAAC;;AAEhC,EAAE,IAAI,QAAQ,EAAE,eAAe,eAAe;AAC9C,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;AAClD,EAAE,IAAI,QAAQ,EAAE,iBAAiB,iBAAiB;AAClD,EAAE,IAAI,KAAK,IAAI,SAAS,EAAE,kBAAkB,GAAG;;AAE/C,CAAC;QAEG,MAAM,UAAU,IAAI,OAAO,CACxB,OAAO,CAAC,kBAAkB,8BAC1B,OAAO,CAAC,SAAS,aAAa,iCAAiC;QAElE,OAAO,GAAG,OAAO,IAAI,EAAE,SAAS;IAClC;AACF;AAEO,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/components/ui/slider.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;0BAET,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;0BAC/B,cAAA,8OAAC,kKAAA,CAAA,QAAqB;oBAAC,WAAU;;;;;;;;;;;0BAEnC,8OAAC,kKAAA,CAAA,QAAqB;gBAAC,WAAU;;;;;;;;;;;;AAGrC,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/app/page.tsx"], "sourcesContent": ["\n\"use client\";\n\nimport React, { useState, useTransition, useRef, useEffect, useCallback } from \"react\";\nimport {\n  Settings2,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>aderCircle,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Feather,\n} from \"lucide-react\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\n// DropdownMenu imports removed - no longer needed\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { Switch } from \"@/components/ui/switch\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from \"@/components/ui/tabs\";\nimport { useToast } from \"@/hooks/use-toast\";\nimport { Skeleton } from \"@/components/ui/skeleton\";\nimport type { ComposeDocumentInput } from \"@/ai/flows/ollama-compose-document\";\nimport { composeDocument } from \"@/ai/flows/ollama-compose-document\";\nimport type { ContinueDocumentInput } from \"@/ai/flows/ollama-continue-document\";\nimport { continueDocument } from \"@/ai/flows/ollama-continue-document\";\nimport { ollamaService } from \"@/ai/ollama-service\";\nimport { EnhancedSidebar, type UploadedDocument } from \"@/components/enhanced-sidebar\";\nimport { documentUploadService } from \"@/lib/document-upload\";\nimport { documentExportService } from \"@/lib/document-export\";\nimport { format as formatDate } from 'date-fns';\nimport { Slider } from \"@/components/ui/slider\";\n\ntype Message = {\n  role: \"user\" | \"assistant\";\n  content: string;\n};\n\ntype Settings = {\n  writingStyle: string;\n  pageCount: number;\n  humanization: boolean;\n  studentName: string;\n  professorName: string;\n  courseDetails: string;\n  citationStyle: \"MLA\" | \"APA\";\n  culturalInflections: {\n    american: number;\n    russian: number;\n    german: number;\n    japanese: number;\n    french: number;\n  };\n};\n\ntype DocumentRecord = {\n  id: string;\n  topic: string;\n  content: string;\n  settings: Settings;\n  createdAt: Date;\n};\n\nconst writingStyles = [\n  \"Analytical\", \"Instructional\", \"Reporting\", \"Argumentative\", \"Exploratory\", \"Descriptive\", \"Narrative\", \"Schematic\",\n];\n\nconst cultures: Array<keyof Settings['culturalInflections']> = [\n  'american',\n  'russian',\n  'german',\n  'japanese',\n  'french',\n];\n\n\n// Old LeftSidebar component removed - replaced with EnhancedSidebar\n\n// ExportDropdown removed - export functionality moved to EnhancedSidebar\n\nconst SettingsPanel = ({ settings, setSettings, disabled, onSave }: { settings: Settings, setSettings: React.Dispatch<React.SetStateAction<Settings>>, disabled: boolean, onSave: () => void }) => (\n  <Card className=\"h-full\">\n    <CardHeader>\n      <CardTitle className=\"flex items-center gap-2 font-headline\">\n        <Settings2 /> Settings\n      </CardTitle>\n    </CardHeader>\n    <CardContent className=\"grid grid-cols-2 gap-x-4 gap-y-6\">\n      <div className=\"space-y-2 col-span-2 sm:col-span-1\">\n        <Label htmlFor=\"writing-style\">Writing Style</Label>\n        <Select value={settings.writingStyle} onValueChange={(value) => setSettings(s => ({ ...s, writingStyle: value }))} disabled={disabled}>\n          <SelectTrigger id=\"writing-style\"><SelectValue placeholder=\"Select style\" /></SelectTrigger>\n          <SelectContent>{writingStyles.map(style => <SelectItem key={style} value={style}>{style}</SelectItem>)}</SelectContent>\n        </Select>\n      </div>\n      <div className=\"space-y-2 col-span-2 sm:col-span-1\">\n        <Label htmlFor=\"pages\">Pages</Label>\n        <Input id=\"pages\" type=\"number\" min=\"1\" max=\"1500\" value={settings.pageCount} onChange={(e) => setSettings(s => ({ ...s, pageCount: Math.min(1500, Math.max(1, parseInt(e.target.value, 10) || 1)) }))} disabled={disabled}/>\n        <p className=\"text-sm text-muted-foreground\">Approx. {settings.pageCount * 250} words</p>\n      </div>\n      <div className=\"space-y-2 col-span-2\">\n        <Label htmlFor=\"humanization\" className=\"flex items-center gap-2\">\n          Humanization <Sparkles className=\"text-primary\" />\n        </Label>\n        <div className=\"flex items-center space-x-2 h-10\">\n          <Switch id=\"humanization\" checked={settings.humanization} onCheckedChange={(checked) => setSettings(s => ({ ...s, humanization: checked }))} disabled={disabled} />\n          <Label htmlFor=\"humanization\" className=\"text-sm text-muted-foreground\">Improve realism</Label>\n        </div>\n      </div>\n\n      <div className=\"col-span-2 border-t pt-4 mt-2\">\n        <h3 className=\"font-semibold text-lg mb-2 font-headline\">Cultural Inflection</h3>\n        <p className=\"text-sm text-muted-foreground mb-4\">\n          Blend cultural communication styles. Set to 0% to turn off.\n        </p>\n      </div>\n      \n      {cultures.map((key) => (\n        <div key={key} className=\"col-span-2 sm:col-span-1 space-y-2\">\n          <div className=\"flex justify-between items-center\">\n              <Label htmlFor={`inflection-${key}`} className=\"capitalize\">{key}</Label>\n              <span className=\"text-sm text-muted-foreground\">{settings.culturalInflections[key]}%</span>\n          </div>\n          <Slider\n              id={`inflection-${key}`}\n              min={0}\n              max={100}\n              step={10}\n              value={[settings.culturalInflections[key]]}\n              onValueChange={([value]) => setSettings(s => ({\n                  ...s,\n                  culturalInflections: { ...s.culturalInflections, [key]: value }\n              }))}\n              disabled={disabled}\n          />\n        </div>\n      ))}\n      \n      <div className=\"col-span-2 border-t pt-4 mt-2\">\n        <h3 className=\"font-semibold text-lg mb-2 font-headline\">Academic Formatting</h3>\n      </div>\n       <div className=\"space-y-2\">\n        <Label htmlFor=\"student-name\">Student Name</Label>\n        <Input id=\"student-name\" value={settings.studentName} onChange={(e) => setSettings(s => ({...s, studentName: e.target.value}))} disabled={disabled} placeholder=\"John Doe\" />\n      </div>\n       <div className=\"space-y-2\">\n        <Label htmlFor=\"professor-name\">Professor Name</Label>\n        <Input id=\"professor-name\" value={settings.professorName} onChange={(e) => setSettings(s => ({...s, professorName: e.target.value}))} disabled={disabled} placeholder=\"Dr. Smith\" />\n      </div>\n       <div className=\"space-y-2\">\n        <Label htmlFor=\"course-details\">Course Details</Label>\n        <Input id=\"course-details\" value={settings.courseDetails} onChange={(e) => setSettings(s => ({...s, courseDetails: e.target.value}))} disabled={disabled} placeholder=\"ENGL 101\" />\n      </div>\n      <div className=\"space-y-2\">\n        <Label htmlFor=\"citation-style\">Citation Style</Label>\n        <Select value={settings.citationStyle} onValueChange={(value) => setSettings(s => ({ ...s, citationStyle: value as Settings['citationStyle'] }))} disabled={disabled}>\n          <SelectTrigger id=\"citation-style\"><SelectValue /></SelectTrigger>\n          <SelectContent>\n            <SelectItem value=\"MLA\">MLA</SelectItem>\n            <SelectItem value=\"APA\">APA</SelectItem>\n          </SelectContent>\n        </Select>\n      </div>\n      <div className=\"col-span-2 flex justify-end\">\n        <Button onClick={onSave} disabled={disabled}><Save className=\"mr-2\" /> Save Settings</Button>\n      </div>\n    </CardContent>\n  </Card>\n);\n\nconst DocumentPreview = ({ content, topic, settings }: { content: string, topic: string, settings: Settings }) => {\n  const pages = content ? content.split('[PAGEBREAK]') : [];\n  \n  if (!content) {\n    return (\n      <div className=\"text-center text-muted-foreground h-full flex flex-col justify-center items-center py-20\">\n        <Feather size={48} className=\"mb-4 text-primary/50\" />\n        <h3 className=\"font-semibold text-lg text-foreground\">No Document Generated</h3>\n        <p>Use the chat to generate your document.</p>\n      </div>\n    );\n  }\n\n  const studentLastName = settings.studentName.split(' ').pop() || '';\n  const shortTitle = topic.split(' ').slice(0, 5).join(' ').toUpperCase();\n  \n  return (\n    <div className=\"p-4 sm:p-8\">\n      {pages.map((page, index) => (\n        <div key={index} className=\"relative bg-white text-black shadow-lg p-12 mb-8 font-serif leading-relaxed text-[12pt]\" style={{width: '8.5in', minHeight: '11in'}}>\n          <div className=\"absolute top-[0.5in] right-[1in] text-[12pt]\">\n            {settings.citationStyle === 'MLA' ? `${studentLastName} ${index + 1}` : `${shortTitle} ${index + 1}`}\n          </div>\n\n          {index === 0 && (\n            <>\n              <pre className=\"whitespace-pre-wrap font-inherit\">{settings.studentName}{\"\\n\"}{settings.professorName}{\"\\n\"}{settings.courseDetails}{\"\\n\"}{formatDate(new Date(), 'dd MMMM yyyy')}</pre>\n              <h1 className=\"text-center font-bold my-6 text-[14pt]\">{topic}</h1>\n            </>\n          )}\n          <pre className=\"whitespace-pre-wrap font-inherit\">{page.trim()}</pre>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nconst ChatView = ({ messages, chatInput, setChatInput, handleSend, isLoading }: { messages: Message[], chatInput: string, setChatInput: (val: string) => void, handleSend: () => void, isLoading: boolean }) => {\n  const scrollAreaRef = useRef<HTMLDivElement>(null);\n\n  useEffect(() => {\n    if (scrollAreaRef.current) {\n      const viewport = scrollAreaRef.current.querySelector('div[data-radix-scroll-area-viewport]');\n      if (viewport) viewport.scrollTop = viewport.scrollHeight;\n    }\n  }, [messages]);\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      if (!isLoading) handleSend();\n    }\n  };\n\n  return (\n    <main className=\"flex flex-col h-full bg-background/80 p-4\">\n      <ScrollArea className=\"flex-1 -mx-4\" ref={scrollAreaRef}>\n        <div className=\"px-4 pb-4\">\n          {messages.length === 0 ? (\n            <div className=\"text-center text-muted-foreground h-full flex flex-col justify-center items-center pt-20\">\n               <h2 className=\"text-3xl font-headline text-foreground\">Welcome to ASCAES</h2>\n               <p className=\"mt-2 max-w-md\">Describe the topic for your document below to get started.</p>\n            </div>\n          ) : (\n            <div className=\"space-y-6\">\n              {messages.map((msg, idx) => (\n                <div key={idx} className={`flex items-start gap-4 ${msg.role === 'user' ? 'justify-end' : ''}`}>\n                  {msg.role === 'assistant' && <div className=\"p-2 rounded-full bg-primary/20 text-primary\"><Bot /></div>}\n                  <div className={`rounded-lg p-4 max-w-xl ${msg.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-card'}`}>\n                    <p className=\"whitespace-pre-wrap\">{msg.content}</p>\n                  </div>\n                  {msg.role === 'user' && <div className=\"p-2 rounded-full bg-accent/20 text-accent-foreground\"><User /></div>}\n                </div>\n              ))}\n              {isLoading && messages.length > 0 && messages[messages.length-1].role === 'user' && (\n                <div className=\"flex items-start gap-4\">\n                  <div className=\"p-2 rounded-full bg-primary/20 text-primary\"><Bot /></div>\n                  <div className=\"rounded-lg p-4 bg-card flex items-center space-x-2\">\n                    <LoaderCircle className=\"animate-spin\" />\n                    <span>Generating...</span>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n      <div className=\"mt-4 relative\">\n        <Textarea value={chatInput} onChange={(e) => setChatInput(e.target.value)} onKeyDown={handleKeyDown} placeholder=\"e.g., 'A research paper on the impact of climate change on marine biodiversity...'\" className=\"pr-20 py-3\" rows={2} disabled={isLoading}/>\n        <Button size=\"icon\" className=\"absolute right-3 top-1/2 -translate-y-1/2\" onClick={handleSend} disabled={isLoading || !chatInput.trim()}><ArrowUp /></Button>\n      </div>\n    </main>\n  );\n};\n\n// useExportHandler removed - export functionality moved to documentExportService\n\n\nexport default function ASCAESPage() {\n  const [settings, setSettings] = useState<Settings>({\n    writingStyle: \"Analytical\",\n    pageCount: 10, // Changed to 10 pages per batch\n    humanization: false,\n    studentName: \"John Doe\",\n    professorName: \"Professor Smith\",\n    courseDetails: \"ENGL 101\",\n    citationStyle: \"MLA\",\n    culturalInflections: {\n      american: 100,\n      russian: 0,\n      german: 0,\n      japanese: 0,\n      french: 0,\n    },\n  });\n  const [chatInput, setChatInput] = useState(\"\");\n  const [chatHistory, setChatHistory] = useState<Message[]>([]);\n  const [generatedContent, setGeneratedContent] = useState(\"\");\n  const [isGenerating, startTransition] = useTransition();\n  const { toast } = useToast();\n  const [currentTopic, setCurrentTopic] = useState(\"\");\n  const [documentHistory, setDocumentHistory] = useState<DocumentRecord[]>([]);\n  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([]);\n  const [currentModel, setCurrentModel] = useState(\"llama3.2:3b\");\n\n  // Initialize document upload service and load uploaded documents\n  useEffect(() => {\n    documentUploadService.init();\n    setUploadedDocuments(documentUploadService.getUploadedDocuments());\n  }, []);\n\n  const handleGenerate = useCallback(() => {\n    if (!chatInput.trim()) return;\n\n    const topic = chatInput.trim().replace(/^paper on /i, '').replace(/ in /i, ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n    setCurrentTopic(topic);\n    const userMessage: Message = { role: \"user\", content: chatInput };\n    setChatHistory([userMessage]);\n    setGeneratedContent(\"\");\n    setChatInput(\"\");\n\n    startTransition(async () => {\n      let fullContent = \"\";\n      try {\n        // Check if Ollama is connected\n        const isConnected = await ollamaService.checkConnection();\n        if (!isConnected) {\n          throw new Error(\"Cannot connect to Ollama. Please make sure Ollama is running on localhost:11434\");\n        }\n\n        // Set the current model\n        ollamaService.setCurrentModel(currentModel);\n\n        setChatHistory(prev => [...prev, { role: 'assistant', content: `Generating page 1 of ${settings.pageCount} using ${currentModel}...` }]);\n        const composeInput: ComposeDocumentInput = { topic, writingStyle: settings.writingStyle, culturalInflections: settings.culturalInflections };\n        const result = await composeDocument(composeInput);\n        if (!result.content || result.content.trim() === '') throw new Error(\"The AI returned an empty response for the first page.\");\n        \n        fullContent = result.content;\n        setGeneratedContent(fullContent);\n\n        let pagesGenerated = 1;\n        while (pagesGenerated < settings.pageCount) {\n          // Wait 6 seconds before the next batch request.\n          await new Promise(resolve => setTimeout(resolve, 6000));\n\n          const remainingPages = settings.pageCount - pagesGenerated;\n          const pagesToGenerateInBatch = Math.min(10, remainingPages);\n          const batchEndPage = pagesGenerated + pagesToGenerateInBatch;\n          \n          setChatHistory(prev => {\n              const newHistory = [...prev];\n              const lastMessage = newHistory[newHistory.length - 1];\n              const progressMessage = `Generating pages ${pagesGenerated + 1} to ${batchEndPage} of ${settings.pageCount}...`;\n              if (lastMessage.role === 'assistant' && lastMessage.content.startsWith('Generating page')) {\n                  newHistory[newHistory.length - 1] = { role: 'assistant', content: progressMessage };\n              } else {\n                  newHistory.push({ role: 'assistant', content: progressMessage });\n              }\n              return newHistory;\n          });\n\n          const pages = fullContent.split('[PAGEBREAK]');\n          const context = pages[pages.length - 1].slice(-1000);\n\n          const continueInput: ContinueDocumentInput = { \n              topic, \n              writingStyle: settings.writingStyle, \n              context,\n              pagesToGenerate: pagesToGenerateInBatch,\n              culturalInflections: settings.culturalInflections,\n          };\n          const continueResult = await continueDocument(continueInput);\n\n          if (!continueResult.content || continueResult.content.trim() === '') {\n              toast({ variant: \"destructive\", title: \"Generation Stopped\", description: `The AI failed to generate pages starting from page ${pagesGenerated + 1}.` });\n              break;\n          }\n\n          fullContent += \"\\n\\n[PAGEBREAK]\\n\\n\" + continueResult.content;\n          setGeneratedContent(fullContent);\n          // This is more robust in case the AI doesn't return exactly what was requested.\n          pagesGenerated = fullContent.split('[PAGEBREAK]').length;\n        }\n        \n        const finalMessage: Message = { role: \"assistant\", content: `I have finished generating your document on \"${topic}\" using ${currentModel}. You can see the preview on the right.` };\n        setChatHistory(prev => [...prev.slice(0, prev.findIndex(m => m.content.startsWith(\"Generating page\"))), finalMessage]);\n\n        const newRecord: DocumentRecord = { id: new Date().toISOString(), topic, content: fullContent, settings, createdAt: new Date() };\n        setDocumentHistory(prev => [newRecord, ...prev]);\n\n      } catch (error) {\n        console.error(error);\n        const errorMessage = error instanceof Error ? error.message : \"An unknown error occurred.\";\n        toast({ variant: \"destructive\", title: \"Generation Failed\", description: errorMessage });\n        setChatHistory(prev => [...prev.slice(0, 1), { role: \"assistant\", content: `I'm sorry, I couldn't generate the document. ${errorMessage}` }]);\n      }\n    });\n  }, [chatInput, settings, toast, currentModel]);\n  \n  const handleSaveSettings = () => toast({ title: \"Settings Saved\", description: \"Your new settings will be applied to the next document generation.\" });\n  const handleSelectDocument = (doc: DocumentRecord) => {\n    setGeneratedContent(doc.content);\n    setCurrentTopic(doc.topic);\n    setSettings(doc.settings);\n    setChatHistory([]);\n  };\n  const handleNewDocument = () => {\n    setGeneratedContent(\"\");\n    setCurrentTopic(\"\");\n    setChatHistory([]);\n    setChatInput(\"\");\n  };\n\n  const handleNewChat = () => {\n    setChatHistory([]);\n    setChatInput(\"\");\n  };\n\n  const handleUploadDocument = async (file: File) => {\n    try {\n      const uploadedDoc = await documentUploadService.uploadDocument(file);\n      setUploadedDocuments(prev => [uploadedDoc, ...prev]);\n      toast({ title: \"File Uploaded\", description: `${file.name} has been uploaded successfully.` });\n    } catch (error) {\n      console.error('Error uploading document:', error);\n      toast({ variant: \"destructive\", title: \"Upload Failed\", description: \"Failed to upload the document.\" });\n    }\n  };\n\n  const handleExportDocument = async (doc: DocumentRecord, format: 'pdf' | 'rtf' | 'latex' | 'txt') => {\n    try {\n      toast({ title: \"Export\", description: `Exporting document as ${format.toUpperCase()}...` });\n\n      switch (format) {\n        case 'pdf':\n          await documentExportService.exportAsPDF(doc);\n          break;\n        case 'rtf':\n          documentExportService.exportAsRTF(doc);\n          break;\n        case 'latex':\n          documentExportService.exportAsLaTeX(doc);\n          break;\n        case 'txt':\n          documentExportService.exportAsText(doc);\n          break;\n      }\n\n      toast({ title: \"Export Complete\", description: `Document exported as ${format.toUpperCase()} successfully.` });\n    } catch (error) {\n      console.error('Export error:', error);\n      toast({ variant: \"destructive\", title: \"Export Failed\", description: `Failed to export document as ${format.toUpperCase()}.` });\n    }\n  };\n\n  const handleModelChange = (model: string) => {\n    setCurrentModel(model);\n    ollamaService.setCurrentModel(model);\n    toast({ title: \"Model Changed\", description: `Switched to ${model}` });\n  };\n\n  const isPreviewLoading = isGenerating && !generatedContent;\n\n  return (\n    <div className=\"grid grid-cols-[320px_1fr] md:grid-cols-[320px_1fr_1.2fr] h-screen font-body text-foreground overflow-hidden\">\n      <EnhancedSidebar\n        documentHistory={documentHistory}\n        uploadedDocuments={uploadedDocuments}\n        onSelectDocument={handleSelectDocument}\n        onNewDocument={handleNewDocument}\n        onNewChat={handleNewChat}\n        onUploadDocument={handleUploadDocument}\n        onExportDocument={handleExportDocument}\n        currentModel={currentModel}\n        onModelChange={handleModelChange}\n      />\n      <ChatView messages={chatHistory} chatInput={chatInput} setChatInput={setChatInput} handleSend={handleGenerate} isLoading={isGenerating} />\n      <aside className=\"hidden md:flex flex-col h-full bg-card p-4 border-l min-w-0\">\n        <Tabs defaultValue=\"document\" className=\"flex flex-col flex-1 min-h-0\">\n            <TabsList className=\"grid w-full grid-cols-2\">\n                <TabsTrigger value=\"document\">Document</TabsTrigger>\n                <TabsTrigger value=\"settings\">Settings</TabsTrigger>\n            </TabsList>\n            <TabsContent value=\"document\" className=\"flex-1 min-h-0 mt-4 overflow-y-auto\">\n                 <Card className=\"flex-1 flex flex-col min-h-0\">\n                    <CardHeader>\n                        <div className=\"flex justify-between items-start\">\n                        <div>\n                            <CardTitle className=\"font-headline\">Document Preview</CardTitle>\n                            <CardDescription>Generated content will appear here.</CardDescription>\n                        </div>\n                        </div>\n                    </CardHeader>\n                    <CardContent className=\"flex-1 min-h-0 bg-muted/30\">\n                        <ScrollArea className=\"h-full w-full\">\n                            {isPreviewLoading ? (\n                                <div className=\"space-y-4 p-8\">\n                                    <Skeleton className=\"h-4 w-full\" />\n                                    <Skeleton className=\"h-4 w-full\" />\n                                    <Skeleton className=\"h-4 w-[80%]\" />\n                                </div>\n                            ) : (\n                                <DocumentPreview content={generatedContent} topic={currentTopic} settings={settings} />\n                            )}\n                        </ScrollArea>\n                    </CardContent>\n                </Card>\n            </TabsContent>\n            <TabsContent value=\"settings\" className=\"flex-1 min-h-0 mt-4 overflow-y-auto\">\n                <SettingsPanel settings={settings} setSettings={setSettings} disabled={isGenerating} onSave={handleSaveSettings} />\n            </TabsContent>\n        </Tabs>\n      </aside>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA,kDAAkD;AAClD;AAOA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAzCA;;;;;;;;;;;;;;;;;;;;;;;AAyEA,MAAM,gBAAgB;IACpB;IAAc;IAAiB;IAAa;IAAiB;IAAe;IAAe;IAAa;CACzG;AAED,MAAM,WAAyD;IAC7D;IACA;IACA;IACA;IACA;CACD;AAGD,oEAAoE;AAEpE,yEAAyE;AAEzE,MAAM,gBAAgB,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,EAA8H,iBAC5L,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,8OAAC,gNAAA,CAAA,YAAS;;;;;wBAAG;;;;;;;;;;;;0BAGjB,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAgB;;;;;;0CAC/B,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,SAAS,YAAY;gCAAE,eAAe,CAAC,QAAU,YAAY,CAAA,IAAK,CAAC;4CAAE,GAAG,CAAC;4CAAE,cAAc;wCAAM,CAAC;gCAAI,UAAU;;kDAC3H,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,IAAG;kDAAgB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAC3D,8OAAC,kIAAA,CAAA,gBAAa;kDAAE,cAAc,GAAG,CAAC,CAAA,sBAAS,8OAAC,kIAAA,CAAA,aAAU;gDAAa,OAAO;0DAAQ;+CAAtB;;;;;;;;;;;;;;;;;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAQ;;;;;;0CACvB,8OAAC,iIAAA,CAAA,QAAK;gCAAC,IAAG;gCAAQ,MAAK;gCAAS,KAAI;gCAAI,KAAI;gCAAO,OAAO,SAAS,SAAS;gCAAE,UAAU,CAAC,IAAM,YAAY,CAAA,IAAK,CAAC;4CAAE,GAAG,CAAC;4CAAE,WAAW,KAAK,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO;wCAAI,CAAC;gCAAI,UAAU;;;;;;0CAClN,8OAAC;gCAAE,WAAU;;oCAAgC;oCAAS,SAAS,SAAS,GAAG;oCAAI;;;;;;;;;;;;;kCAEjF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAe,WAAU;;oCAA0B;kDACnD,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;;0CAEnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,IAAG;wCAAe,SAAS,SAAS,YAAY;wCAAE,iBAAiB,CAAC,UAAY,YAAY,CAAA,IAAK,CAAC;oDAAE,GAAG,CAAC;oDAAE,cAAc;gDAAQ,CAAC;wCAAI,UAAU;;;;;;kDACvJ,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAe,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;kCAI5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2C;;;;;;0CACzD,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;oBAKnD,SAAS,GAAG,CAAC,CAAC,oBACb,8OAAC;4BAAc,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACX,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAS,CAAC,WAAW,EAAE,KAAK;4CAAE,WAAU;sDAAc;;;;;;sDAC7D,8OAAC;4CAAK,WAAU;;gDAAiC,SAAS,mBAAmB,CAAC,IAAI;gDAAC;;;;;;;;;;;;;8CAEvF,8OAAC,kIAAA,CAAA,SAAM;oCACH,IAAI,CAAC,WAAW,EAAE,KAAK;oCACvB,KAAK;oCACL,KAAK;oCACL,MAAM;oCACN,OAAO;wCAAC,SAAS,mBAAmB,CAAC,IAAI;qCAAC;oCAC1C,eAAe,CAAC,CAAC,MAAM,GAAK,YAAY,CAAA,IAAK,CAAC;gDAC1C,GAAG,CAAC;gDACJ,qBAAqB;oDAAE,GAAG,EAAE,mBAAmB;oDAAE,CAAC,IAAI,EAAE;gDAAM;4CAClE,CAAC;oCACD,UAAU;;;;;;;2BAfN;;;;;kCAoBZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;;;;;;kCAE1D,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAe;;;;;;0CAC9B,8OAAC,iIAAA,CAAA,QAAK;gCAAC,IAAG;gCAAe,OAAO,SAAS,WAAW;gCAAE,UAAU,CAAC,IAAM,YAAY,CAAA,IAAK,CAAC;4CAAC,GAAG,CAAC;4CAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wCAAA,CAAC;gCAAI,UAAU;gCAAU,aAAY;;;;;;;;;;;;kCAEjK,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAiB;;;;;;0CAChC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,IAAG;gCAAiB,OAAO,SAAS,aAAa;gCAAE,UAAU,CAAC,IAAM,YAAY,CAAA,IAAK,CAAC;4CAAC,GAAG,CAAC;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAA,CAAC;gCAAI,UAAU;gCAAU,aAAY;;;;;;;;;;;;kCAEvK,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAiB;;;;;;0CAChC,8OAAC,iIAAA,CAAA,QAAK;gCAAC,IAAG;gCAAiB,OAAO,SAAS,aAAa;gCAAE,UAAU,CAAC,IAAM,YAAY,CAAA,IAAK,CAAC;4CAAC,GAAG,CAAC;4CAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wCAAA,CAAC;gCAAI,UAAU;gCAAU,aAAY;;;;;;;;;;;;kCAExK,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAiB;;;;;;0CAChC,8OAAC,kIAAA,CAAA,SAAM;gCAAC,OAAO,SAAS,aAAa;gCAAE,eAAe,CAAC,QAAU,YAAY,CAAA,IAAK,CAAC;4CAAE,GAAG,CAAC;4CAAE,eAAe;wCAAmC,CAAC;gCAAI,UAAU;;kDAC1J,8OAAC,kIAAA,CAAA,gBAAa;wCAAC,IAAG;kDAAiB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kDAC/C,8OAAC,kIAAA,CAAA,gBAAa;;0DACZ,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,8OAAC,kIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;kCAI9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAQ,UAAU;;8CAAU,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAM9E,MAAM,kBAAkB,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAA0D;IAC3G,MAAM,QAAQ,UAAU,QAAQ,KAAK,CAAC,iBAAiB,EAAE;IAEzD,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,wMAAA,CAAA,UAAO;oBAAC,MAAM;oBAAI,WAAU;;;;;;8BAC7B,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;8BAAE;;;;;;;;;;;;IAGT;IAEA,MAAM,kBAAkB,SAAS,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM;IACjE,MAAM,aAAa,MAAM,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,WAAW;IAErE,qBACE,8OAAC;QAAI,WAAU;kBACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gBAAgB,WAAU;gBAA0F,OAAO;oBAAC,OAAO;oBAAS,WAAW;gBAAM;;kCAC5J,8OAAC;wBAAI,WAAU;kCACZ,SAAS,aAAa,KAAK,QAAQ,GAAG,gBAAgB,CAAC,EAAE,QAAQ,GAAG,GAAG,GAAG,WAAW,CAAC,EAAE,QAAQ,GAAG;;;;;;oBAGrG,UAAU,mBACT;;0CACE,8OAAC;gCAAI,WAAU;;oCAAoC,SAAS,WAAW;oCAAE;oCAAM,SAAS,aAAa;oCAAE;oCAAM,SAAS,aAAa;oCAAE;oCAAM,CAAA,GAAA,sJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ;;;;;;;0CAClK,8OAAC;gCAAG,WAAU;0CAA0C;;;;;;;;kCAG5D,8OAAC;wBAAI,WAAU;kCAAoC,KAAK,IAAI;;;;;;;eAXpD;;;;;;;;;;AAgBlB;AAEA,MAAM,WAAW,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAA+H;IACzM,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,EAAE;YACzB,MAAM,WAAW,cAAc,OAAO,CAAC,aAAa,CAAC;YACrD,IAAI,UAAU,SAAS,SAAS,GAAG,SAAS,YAAY;QAC1D;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB,IAAI,CAAC,WAAW;QAClB;IACF;IAEA,qBACE,8OAAC;QAAK,WAAU;;0BACd,8OAAC,0IAAA,CAAA,aAAU;gBAAC,WAAU;gBAAe,KAAK;0BACxC,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;;0CACZ,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;6CAGhC,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,KAAK,oBAClB,8OAAC;oCAAc,WAAW,CAAC,uBAAuB,EAAE,IAAI,IAAI,KAAK,SAAS,gBAAgB,IAAI;;wCAC3F,IAAI,IAAI,KAAK,6BAAe,8OAAC;4CAAI,WAAU;sDAA8C,cAAA,8OAAC,gMAAA,CAAA,MAAG;;;;;;;;;;sDAC9F,8OAAC;4CAAI,WAAW,CAAC,wBAAwB,EAAE,IAAI,IAAI,KAAK,SAAS,uCAAuC,WAAW;sDACjH,cAAA,8OAAC;gDAAE,WAAU;0DAAuB,IAAI,OAAO;;;;;;;;;;;wCAEhD,IAAI,IAAI,KAAK,wBAAU,8OAAC;4CAAI,WAAU;sDAAuD,cAAA,8OAAC,kMAAA,CAAA,OAAI;;;;;;;;;;;mCAL3F;;;;;4BAQX,aAAa,SAAS,MAAM,GAAG,KAAK,QAAQ,CAAC,SAAS,MAAM,GAAC,EAAE,CAAC,IAAI,KAAK,wBACxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA8C,cAAA,8OAAC,gMAAA,CAAA,MAAG;;;;;;;;;;kDACjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,OAAO;wBAAW,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wBAAG,WAAW;wBAAe,aAAY;wBAAqF,WAAU;wBAAa,MAAM;wBAAG,UAAU;;;;;;kCAChP,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAO,WAAU;wBAA4C,SAAS;wBAAY,UAAU,aAAa,CAAC,UAAU,IAAI;kCAAI,cAAA,8OAAC,4MAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;AAIzJ;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,cAAc;QACd,WAAW;QACX,cAAc;QACd,aAAa;QACb,eAAe;QACf,eAAe;QACf,eAAe;QACf,qBAAqB;YACnB,UAAU;YACV,SAAS;YACT,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD;IACpD,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,iEAAiE;IACjE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gIAAA,CAAA,wBAAqB,CAAC,IAAI;QAC1B,qBAAqB,gIAAA,CAAA,wBAAqB,CAAC,oBAAoB;IACjE,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,UAAU,IAAI,IAAI;QAEvB,MAAM,QAAQ,UAAU,IAAI,GAAG,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;QAC1J,gBAAgB;QAChB,MAAM,cAAuB;YAAE,MAAM;YAAQ,SAAS;QAAU;QAChE,eAAe;YAAC;SAAY;QAC5B,oBAAoB;QACpB,aAAa;QAEb,gBAAgB;YACd,IAAI,cAAc;YAClB,IAAI;gBACF,+BAA+B;gBAC/B,MAAM,cAAc,MAAM,8HAAA,CAAA,gBAAa,CAAC,eAAe;gBACvD,IAAI,CAAC,aAAa;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,wBAAwB;gBACxB,8HAAA,CAAA,gBAAa,CAAC,eAAe,CAAC;gBAE9B,eAAe,CAAA,OAAQ;2BAAI;wBAAM;4BAAE,MAAM;4BAAa,SAAS,CAAC,qBAAqB,EAAE,SAAS,SAAS,CAAC,OAAO,EAAE,aAAa,GAAG,CAAC;wBAAC;qBAAE;gBACvI,MAAM,eAAqC;oBAAE;oBAAO,cAAc,SAAS,YAAY;oBAAE,qBAAqB,SAAS,mBAAmB;gBAAC;gBAC3I,MAAM,SAAS,MAAM,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE;gBACrC,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,IAAI,OAAO,IAAI,MAAM,IAAI,MAAM;gBAErE,cAAc,OAAO,OAAO;gBAC5B,oBAAoB;gBAEpB,IAAI,iBAAiB;gBACrB,MAAO,iBAAiB,SAAS,SAAS,CAAE;oBAC1C,gDAAgD;oBAChD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBAEjD,MAAM,iBAAiB,SAAS,SAAS,GAAG;oBAC5C,MAAM,yBAAyB,KAAK,GAAG,CAAC,IAAI;oBAC5C,MAAM,eAAe,iBAAiB;oBAEtC,eAAe,CAAA;wBACX,MAAM,aAAa;+BAAI;yBAAK;wBAC5B,MAAM,cAAc,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;wBACrD,MAAM,kBAAkB,CAAC,iBAAiB,EAAE,iBAAiB,EAAE,IAAI,EAAE,aAAa,IAAI,EAAE,SAAS,SAAS,CAAC,GAAG,CAAC;wBAC/G,IAAI,YAAY,IAAI,KAAK,eAAe,YAAY,OAAO,CAAC,UAAU,CAAC,oBAAoB;4BACvF,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG;gCAAE,MAAM;gCAAa,SAAS;4BAAgB;wBACtF,OAAO;4BACH,WAAW,IAAI,CAAC;gCAAE,MAAM;gCAAa,SAAS;4BAAgB;wBAClE;wBACA,OAAO;oBACX;oBAEA,MAAM,QAAQ,YAAY,KAAK,CAAC;oBAChC,MAAM,UAAU,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;oBAE/C,MAAM,gBAAuC;wBACzC;wBACA,cAAc,SAAS,YAAY;wBACnC;wBACA,iBAAiB;wBACjB,qBAAqB,SAAS,mBAAmB;oBACrD;oBACA,MAAM,iBAAiB,MAAM,CAAA,GAAA,oJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAE9C,IAAI,CAAC,eAAe,OAAO,IAAI,eAAe,OAAO,CAAC,IAAI,OAAO,IAAI;wBACjE,MAAM;4BAAE,SAAS;4BAAe,OAAO;4BAAsB,aAAa,CAAC,mDAAmD,EAAE,iBAAiB,EAAE,CAAC,CAAC;wBAAC;wBACtJ;oBACJ;oBAEA,eAAe,wBAAwB,eAAe,OAAO;oBAC7D,oBAAoB;oBACpB,gFAAgF;oBAChF,iBAAiB,YAAY,KAAK,CAAC,eAAe,MAAM;gBAC1D;gBAEA,MAAM,eAAwB;oBAAE,MAAM;oBAAa,SAAS,CAAC,6CAA6C,EAAE,MAAM,QAAQ,EAAE,aAAa,uCAAuC,CAAC;gBAAC;gBAClL,eAAe,CAAA,OAAQ;2BAAI,KAAK,KAAK,CAAC,GAAG,KAAK,SAAS,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,UAAU,CAAC;wBAAsB;qBAAa;gBAErH,MAAM,YAA4B;oBAAE,IAAI,IAAI,OAAO,WAAW;oBAAI;oBAAO,SAAS;oBAAa;oBAAU,WAAW,IAAI;gBAAO;gBAC/H,mBAAmB,CAAA,OAAQ;wBAAC;2BAAc;qBAAK;YAEjD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC;gBACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC9D,MAAM;oBAAE,SAAS;oBAAe,OAAO;oBAAqB,aAAa;gBAAa;gBACtF,eAAe,CAAA,OAAQ;2BAAI,KAAK,KAAK,CAAC,GAAG;wBAAI;4BAAE,MAAM;4BAAa,SAAS,CAAC,6CAA6C,EAAE,cAAc;wBAAC;qBAAE;YAC9I;QACF;IACF,GAAG;QAAC;QAAW;QAAU;QAAO;KAAa;IAE7C,MAAM,qBAAqB,IAAM,MAAM;YAAE,OAAO;YAAkB,aAAa;QAAqE;IACpJ,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB,IAAI,OAAO;QAC/B,gBAAgB,IAAI,KAAK;QACzB,YAAY,IAAI,QAAQ;QACxB,eAAe,EAAE;IACnB;IACA,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,gBAAgB;QAChB,eAAe,EAAE;QACjB,aAAa;IACf;IAEA,MAAM,gBAAgB;QACpB,eAAe,EAAE;QACjB,aAAa;IACf;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,cAAc,MAAM,gIAAA,CAAA,wBAAqB,CAAC,cAAc,CAAC;YAC/D,qBAAqB,CAAA,OAAQ;oBAAC;uBAAgB;iBAAK;YACnD,MAAM;gBAAE,OAAO;gBAAiB,aAAa,GAAG,KAAK,IAAI,CAAC,gCAAgC,CAAC;YAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAiB,aAAa;YAAiC;QACxG;IACF;IAEA,MAAM,uBAAuB,OAAO,KAAqB;QACvD,IAAI;YACF,MAAM;gBAAE,OAAO;gBAAU,aAAa,CAAC,sBAAsB,EAAE,OAAO,WAAW,GAAG,GAAG,CAAC;YAAC;YAEzF,OAAQ;gBACN,KAAK;oBACH,MAAM,gIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;oBACxC;gBACF,KAAK;oBACH,gIAAA,CAAA,wBAAqB,CAAC,WAAW,CAAC;oBAClC;gBACF,KAAK;oBACH,gIAAA,CAAA,wBAAqB,CAAC,aAAa,CAAC;oBACpC;gBACF,KAAK;oBACH,gIAAA,CAAA,wBAAqB,CAAC,YAAY,CAAC;oBACnC;YACJ;YAEA,MAAM;gBAAE,OAAO;gBAAmB,aAAa,CAAC,qBAAqB,EAAE,OAAO,WAAW,GAAG,cAAc,CAAC;YAAC;QAC9G,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;gBAAE,SAAS;gBAAe,OAAO;gBAAiB,aAAa,CAAC,6BAA6B,EAAE,OAAO,WAAW,GAAG,CAAC,CAAC;YAAC;QAC/H;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB;QAChB,8HAAA,CAAA,gBAAa,CAAC,eAAe,CAAC;QAC9B,MAAM;YAAE,OAAO;YAAiB,aAAa,CAAC,YAAY,EAAE,OAAO;QAAC;IACtE;IAEA,MAAM,mBAAmB,gBAAgB,CAAC;IAE1C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,kBAAe;gBACd,iBAAiB;gBACjB,mBAAmB;gBACnB,kBAAkB;gBAClB,eAAe;gBACf,WAAW;gBACX,kBAAkB;gBAClB,kBAAkB;gBAClB,cAAc;gBACd,eAAe;;;;;;0BAEjB,8OAAC;gBAAS,UAAU;gBAAa,WAAW;gBAAW,cAAc;gBAAc,YAAY;gBAAgB,WAAW;;;;;;0BAC1H,8OAAC;gBAAM,WAAU;0BACf,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACpC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAChB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;;;;;;;sCAElC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACnC,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACb,8OAAC,gIAAA,CAAA,aAAU;kDACP,cAAA,8OAAC;4CAAI,WAAU;sDACf,cAAA,8OAAC;;kEACG,8OAAC,gIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAgB;;;;;;kEACrC,8OAAC,gIAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;;;;;;kDAIzB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;kDACnB,cAAA,8OAAC,0IAAA,CAAA,aAAU;4CAAC,WAAU;sDACjB,iCACG,8OAAC;gDAAI,WAAU;;kEACX,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;qEAGxB,8OAAC;gDAAgB,SAAS;gDAAkB,OAAO;gDAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM/F,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACpC,cAAA,8OAAC;gCAAc,UAAU;gCAAU,aAAa;gCAAa,UAAU;gCAAc,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7G", "debugId": null}}]}