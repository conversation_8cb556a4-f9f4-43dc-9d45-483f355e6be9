# ASCAES: AI-Powered Academic Document Generation

ASCAES (Advanced System for Composing and Editing Scholarship) is a sophisticated Next.js application designed to assist with the creation of academic documents. It leverages the power of Google's Gemini 1.5 Pro model through the Genkit framework to generate nuanced, context-aware content based on a unique and powerful modular prompt system.

## Core Features

### 1. Advanced AI-Powered Text Generation
The core of ASCAES is its ability to generate high-quality, structured text.

- **Initial Document Composition**: Users can kick off the writing process by simply providing a topic. The AI then generates the first page of the document.
- **Context-Aware Continuation**: The application can seamlessly continue writing the document for a user-specified number of pages. It analyzes the last paragraph of the existing text to ensure stylistic and thematic consistency.
- **Batch Processing**: To handle API rate limits effectively, the document generation is done in batches (currently 10 pages at a time) with a configurable delay between each batch.

### 2. Modular Prompt System: Style & Cultural Inflection
ASCAES features a unique and powerful system for controlling the AI's output, allowing for a high degree of customization.

- **Writing Styles**: Users can choose from 8 distinct writing styles (e.g., Analytical, Instructional, Argumentative, Narrative) that define the fundamental tone, structure, and objective of the document.
- **Cultural Inflections**: The system includes a layer of 5 "cultural inflections" (American, Russian, German, Japanese, French) that act as a "flavor" or lens for the AI's communication style. Users can blend these inflections using percentage-based sliders to create sophisticated, hybrid writing styles.

### 3. Rich User Interface & Document Management
The application is built with a modern, responsive UI using **Next.js**, **React**, and **ShadCN UI** components.

- **Three-Column Layout**:
    - **Left Sidebar**: Manages the user's document history. Users can start a new document or select a previously generated one to view or export.
    - **Center Chat Panel**: The main interaction area where users input their document topic to the AI. It provides real-time feedback on the generation progress.
    - **Right Panel (Document & Settings)**:
        - **Document Preview**: A live, formatted preview of the generated document. It simulates an 8.5" x 11" page and includes academic formatting like student name, professor name, course details, date, and page numbers based on MLA/APA style.
        - **Settings**: A comprehensive panel to control all generation parameters, including Writing Style, total Page Count, and the Cultural Inflection sliders.

- **Exporting**: Completed documents can be exported in multiple formats, including **PDF**, **LaTeX**, **RTF**, and **TXT**. The PDF export feature uses `jspdf` and `html2canvas` to accurately render the formatted preview into a downloadable file.

## Tech Stack

- **Framework**: Next.js (with App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: ShadCN UI, Lucide Icons
- **AI Integration**: Genkit
- **AI Model**: Google Gemini 1.5 Pro
- **PDF Generation**: jsPDF, html2canvas
- **State Management**: React Hooks (`useState`, `useTransition`, etc.)

## Development & Troubleshooting History

This project's development involved several key decisions and troubleshooting steps:

1.  **AI Provider**: The initial goal was to use a local Ollama model. However, persistent `404 Not Found` errors during the `npm install` process for the `@genkit-ai/ollama` package indicated a likely environment or registry issue. To ensure a stable and buildable application, we pivoted to using the **Google Gemini API**.

2.  **API Rate Limiting**: Early tests with the Gemini API resulted in `429 Too Many Requests` errors. This was resolved by implementing a robust batching and delay system in the document generation logic, ensuring that API calls respect the service's rate limits.

3.  **Dependency Management**: We encountered and resolved several `npm install` and build failures related to dependency conflicts. The solution was to pin all `@genkit-ai/*` packages to a consistent version in `package.json`, ensuring a stable and predictable set of dependencies.

4.  **Prompt Engineering**: The most significant feature is the **Master System Prompt**. This was iteratively developed to give the user fine-grained control over the AI's output, moving beyond simple instructions to a modular system of styles and cultural lenses.
