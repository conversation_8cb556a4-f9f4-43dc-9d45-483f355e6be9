# ASCAES: Offline AI-Powered Academic Document Generation

ASCAES (Advanced System for Composing and Editing Scholarship) is a sophisticated Next.js application designed to assist with the creation of academic documents. It now uses **Ollama** for completely offline AI document generation, providing privacy and independence from cloud services.

## Core Features

### 1. Advanced AI-Powered Text Generation
The core of ASCAES is its ability to generate high-quality, structured text.

- **Initial Document Composition**: Users can kick off the writing process by simply providing a topic. The AI then generates the first page of the document.
- **Context-Aware Continuation**: The application can seamlessly continue writing the document for a user-specified number of pages. It analyzes the last paragraph of the existing text to ensure stylistic and thematic consistency.
- **Batch Processing**: To handle API rate limits effectively, the document generation is done in batches (currently 10 pages at a time) with a configurable delay between each batch.

### 2. Modular Prompt System: Style & Cultural Inflection
ASCAES features a unique and powerful system for controlling the AI's output, allowing for a high degree of customization.

- **Writing Styles**: Users can choose from 8 distinct writing styles (e.g., Analytical, Instructional, Argumentative, Narrative) that define the fundamental tone, structure, and objective of the document.
- **Cultural Inflections**: The system includes a layer of 5 "cultural inflections" (American, Russian, German, Japanese, French) that act as a "flavor" or lens for the AI's communication style. Users can blend these inflections using percentage-based sliders to create sophisticated, hybrid writing styles.

### 3. Rich User Interface & Document Management
The application is built with a modern, responsive UI using **Next.js**, **React**, and **ShadCN UI** components.

- **Three-Column Layout**:
    - **Left Sidebar**: Manages the user's document history. Users can start a new document or select a previously generated one to view or export.
    - **Center Chat Panel**: The main interaction area where users input their document topic to the AI. It provides real-time feedback on the generation progress.
    - **Right Panel (Document & Settings)**:
        - **Document Preview**: A live, formatted preview of the generated document. It simulates an 8.5" x 11" page and includes academic formatting like student name, professor name, course details, date, and page numbers based on MLA/APA style.
        - **Settings**: A comprehensive panel to control all generation parameters, including Writing Style, total Page Count, and the Cultural Inflection sliders.

- **Exporting**: Completed documents can be exported in multiple formats, including **PDF**, **LaTeX**, **RTF**, and **TXT**. The PDF export feature uses `jspdf` and `html2canvas` to accurately render the formatted preview into a downloadable file.

## Tech Stack

- **Framework**: Next.js (with App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: ShadCN UI, Lucide Icons
- **AI Integration**: Ollama (Offline)
- **AI Models**: Llama 3.2:3b, Gemma 3:2b (and any other Ollama-compatible models)
- **PDF Generation**: jsPDF, html2canvas
- **State Management**: React Hooks (`useState`, `useTransition`, etc.)
- **Document Management**: Local storage with export capabilities

## New Features (Offline Version)

### Enhanced UI Layout
- **Left Sidebar**: Comprehensive navigation with sections for:
  - New Chat and Chat history
  - Document management (uploaded and saved documents)
  - Model dashboard showing Ollama models with RAM usage
  - Settings panel
- **Middle Panel**: Chat interface for document generation
- **Right Panel**: Document preview and settings

### Offline AI Integration
- **Ollama Integration**: Complete offline AI processing using local models
- **Model Management**: Real-time monitoring of installed models, RAM usage, and status
- **Batch Processing**: 10 pages per batch with 6-second delays between batches
- **Model Switching**: Easy switching between available Ollama models

### Document Management
- **File Upload**: Support for PDF, Word, and TXT file uploads
- **Export Options**: Export documents in PDF, RTF, LaTeX, and TXT formats
- **Document History**: Chronological list with timestamps and quick export options
- **Local Storage**: All documents stored locally for privacy

### Prerequisites

1. **Install Ollama**: Download and install Ollama from [https://ollama.ai](https://ollama.ai)
2. **Install Models**: Run the following commands to install the default models:
   ```bash
   ollama pull llama3.2:3b
   ollama pull gemma2:2b
   ```
3. **Start Ollama**: Make sure Ollama is running on `localhost:11434`

## Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ASCAES
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser** and navigate to `http://localhost:9002`

## Usage

### Getting Started
1. **Check Model Status**: In the left sidebar, expand the "Models" section to see your installed Ollama models
2. **Select a Model**: Choose your preferred model (default: llama3.2:3b)
3. **Start Generating**: Type your document topic in the chat interface and press Enter

### Document Generation
- Documents are generated in batches of 10 pages
- 6-second delay between batches to prevent overloading
- Real-time progress updates in the chat interface
- Generated content appears in the right panel preview

### Document Management
- **Upload Files**: Use the "Documents" → "Uploaded" section to upload PDF, Word, or TXT files
- **Save Documents**: All generated documents are automatically saved with timestamps
- **Export Options**: Click on any saved document to export in PDF, RTF, LaTeX, or TXT format

### Model Management
- **View Models**: See all installed Ollama models with their sizes and RAM usage
- **Switch Models**: Select different models for different writing styles
- **Monitor Usage**: Real-time monitoring of model status and resource usage

## Configuration

### Writing Styles
Choose from 8 different writing styles:
- Analytical
- Instructional
- Reporting
- Argumentative/Persuasive
- Narrative
- Exploratory/Reflective
- Comparative
- Schematic/Referential

### Cultural Inflections
Blend up to 5 cultural communication styles:
- American (Direct, optimistic, pragmatic)
- Russian (Philosophical, thorough, historically-conscious)
- German (Systematic, precise, methodical)
- Japanese (Respectful, consensus-building, harmony-seeking)
- French (Elegant, intellectually sophisticated, debate-oriented)

## Privacy & Security

- **Completely Offline**: All AI processing happens locally via Ollama
- **No Data Transmission**: Your documents never leave your computer
- **Local Storage**: All data stored in browser's local storage
- **No API Keys Required**: No need for external AI service accounts
