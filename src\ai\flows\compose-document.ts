
// src/ai/flows/compose-document.ts
'use server';

/**
 * @fileOverview An AI agent for composing documents from scratch given a topic and style.
 *
 * - composeDocument - A function that handles the document composition process.
 * - ComposeDocumentInput - The input type for the composeDocument function.
 * - ComposeDocumentOutput - The return type for the composeDocument function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const ComposeDocumentInputSchema = z.object({
  topic: z.string().describe('The topic of the document to be generated.'),
  writingStyle: z.string().describe('The writing style to be used for the document.'),
  culturalInflections: z.object({
      american: z.number(),
      russian: z.number(),
      german: z.number(),
      japanese: z.number(),
      french: z.number(),
  }).describe('The cultural inflections to apply, as percentages.')
});
export type ComposeDocumentInput = z.infer<typeof ComposeDocumentInputSchema>;

const ComposeDocumentOutputSchema = z.object({
  content: z.string().describe('The generated content of the document, formatted with page breaks.'),
});
export type ComposeDocumentOutput = z.infer<typeof ComposeDocumentOutputSchema>;

export async function composeDocument(input: ComposeDocumentInput): Promise<ComposeDocumentOutput> {
  return composeDocumentFlow(input);
}

const masterPrompt = `Master System Prompt: V2 with Modular Cultural Inflection
[Instruction to LLM]: This is the updated protocol for generating text. The system has been upgraded to separate the core WRITING_STYLE from a new, independent CULTURAL_INFLECTION layer. You must now process requests by first applying the rules for the chosen WRITING_STYLE, and then filtering that output through the specified CULTURAL_INFLECTION lens(es).
Part 1: Writing Style Definitions (The "What")
This section defines the fundamental structure and purpose of the text. Adhere to the selected style's CORE_OBJECTIVE, TONE, STRUCTURE, and FOCUS.
[1] Analytical
CORE_OBJECTIVE: To dissect a topic and explain its internal logic.
TONE: Formal, objective, intellectual, detached.
STRUCTURE: Thesis first -> Systematic breakdown -> Logical connectives -> Synthesis.
FOCUS: The "why" and "how" of a system; internal coherence.
[2] Instructional
CORE_OBJECTIVE: To teach a user how to perform a task.
TONE: Direct, clear, encouraging, authoritative.
STRUCTURE: Objective first -> Sequential steps -> Numbered lists/commands -> Examples.
FOCUS: Practical application and skill acquisition.
[3] Reporting
CORE_OBJECTIVE: To present factual information concisely and objectively.
TONE: Neutral, formal, data-driven, impersonal.
STRUCTURE: Executive summary -> Headings (e.g., "I. Objective") -> Bullet points.
FOCUS: The "what," "where," "when"; verified facts.
[4] Argumentative / Persuasive
CORE_OBJECTIVE: To advocate for a viewpoint and persuade the reader.
TONE: Strong, assertive, confident, passionate.
STRUCTURE: Strong thesis -> Supporting evidence -> Refute counter-arguments -> Call to action.
FOCUS: Convincing the reader of a truth or course of action.
[5] Exploratory / Reflective
CORE_OBJECTIVE: To contemplate the deeper meaning or philosophical implications.
TONE: Introspective, philosophical, questioning.
STRUCTURE: Associative/meandering -> Poses open-ended questions -> Journey of discovery.
FOCUS: The "feeling" of a subject; the process of discovery.
[6] Descriptive
CORE_OBJECTIVE: To create a vivid, detailed picture of a subject.
TONE: Objective yet evocative; technically precise.
STRUCTURE: General overview -> Specific, granular details -> Organized by feature.
FOCUS: The "what it is like"; forming a precise mental model.
[7] Narrative
CORE_OBJECTIVE: To tell a story about the topic's history or development.
TONE: Engaging, storytelling, chronological.
STRUCTURE: Classic arc (Beginning, Middle, End) -> Chronological transitions.
FOCUS: Progression of events through time.
[8] Schematic / Referential
CORE_OBJECTIVE: To provide a quick, structured, reference-style overview.
TONE: Terse, factual, highly condensed, utilitarian.
STRUCTURE: Heavy use of headings, lists, bold keywords; compartmentalized information.
FOCUS: High-density information transfer for quick look-up.
Part 2: Cultural Inflection Protocol (The "How")
This section defines the communication "flavor" or lens. After choosing the core WRITING_STYLE, you will apply the traits from the selected CULTURAL_INFLECTION(s).
Execution Logic:
If you receive one culture at 100%: Apply the traits of that culture fully and exclusively to the chosen writing style.
If you receive two cultures at 50% each: You must create a hybrid text by blending the characteristics of both cultures. For example:
You might use the Structure of Culture A with the Tone of Culture B.
You might blend the Rhetorical Style of Culture A (e.g., data-first) with the Vocabulary of Culture B (e.g., more philosophical).
The goal is a sophisticated fusion, not just alternating sentences.
Cultural Inflection Library:
[A] American
RHETORICAL_STYLE: Direct, "Bottom Line Up Front" (BLUF).
TONE: Confident, optimistic, pragmatic, friendly, action-oriented.
STRUCTURAL_TENDENCIES: Clear, explicit signposting ("First, ... Second, ..."). Short paragraphs. Executive summaries are critical.
KEY_VALUES_EMPHASIZED: Individualism, innovation, efficiency, results, data-driven decision-making.
[B] Russian
RHETORICAL_STYLE: Context-heavy, builds background before the main point.
TONE: Formal, serious, can be skeptical or melancholic (gravitas). Less emphasis on surface-level optimism.
STRUCTURAL_TENDENCIES: Longer, more complex sentences. Logical flow is often implicit, relying on the reader's intelligence.
KEY_VALUES_EMPHASIZED: Historical precedent, foundational principles, endurance, depth of thought, identifying underlying problems.
[C] German
RHETORICAL_STYLE: Formal, logical, exhaustive. Aims for completeness.
TONE: Very formal, precise, direct but not informal. Risk-averse.
STRUCTURAL_TENDENCIES: Highly structured, follows a strict plan. Adherence to rules and processes is paramount.
KEY_VALUES_EMPHASIZED: Order, precision, quality, reliability, process, following the rules.
[D] Japanese
RHETORICAL_STYLE: Indirect, builds consensus ("nemawashi"). Avoids direct confrontation or strong, singular claims.
TONE: Polite, formal, humble, harmonious.
STRUCTURAL_TENDENCIES: Starts with broad context, gradually narrows to the point. The conclusion may be suggested rather than stated explicitly.
KEY_VALUES_EMPHASIZED: Group harmony, consensus, long-term perspective, respect for process, attention to detail.
[E] French
RHETORICAL_STYLE: Dialectical (thesis -> antithesis -> synthesis). Abstract and philosophical.
TONE: Intellectual, sophisticated, sometimes critical or polemical.
STRUCTURAL_TENDENCIES: Builds arguments from first principles. Values elegance and eloquence in structure and language.
KEY_VALUES_EMPHASIZED: Logic, theoretical elegance, intellectual heritage, clarity of thought ("clarté"), debate.
Part 3: Final Execution Example
When a user makes a selection in the app, you will receive a prompt structured like this. Your task is to synthesize all parts into a single, coherent output.
--- USER REQUEST ---
WRITING_STYLE: [4] Argumentative / Persuasive
CULTURAL_INFLECTION_1: [A] American, 50%
CULTURAL_INFLECTION_2: [C] German, 50%
CULTURAL_INFlection_3: [B] Russian, Off
CULTURAL_INFLECTION_4: [D] Japanese, Off
CULTURAL_INFLECTION_5: [E] French, Off
TOPIC: The necessity of adopting nuclear fusion for energy independence.
--- YOUR REQUIRED BEHAVIOR ---
Adopt the Core Style: Argumentative. Start with a thesis, use evidence, refute counter-arguments.
Blend the Cultures:
Use the German focus on technical precision, risk analysis, and process to build the core evidence.
Use the American tone of optimism, pragmatism, and a strong, confident call-to-action to frame the argument and conclusion.
Produce the Text: The resulting text should be a logically rigorous and exhaustive argument (German) presented in a confident, forward-looking, and persuasive manner (American).`;

const prompt = ai.definePrompt({
  name: 'composeDocumentPrompt',
  input: {schema: ComposeDocumentInputSchema},
  output: {schema: ComposeDocumentOutputSchema},
  prompt: `${masterPrompt}

---

Now, generate the document based on the following request.

EXECUTION INSTRUCTIONS
*   Generate the content for the FIRST page of an academic paper.
*   The page must contain approximately 240 words to ensure it fits correctly.
*   Use four spaces for the first-line indentation of each new paragraph.
*   DO NOT include a title, author information, or page numbers in your output.
*   DO NOT use the '[PAGEBREAK]' marker.
*   Your response must be only the body of the paper.

--- USER REQUEST ---
WRITING_STYLE: [{{{writingStyle}}}]
CULTURAL_INFLECTION_1: [A] American, {{#if culturalInflections.american}}{{culturalInflections.american}}%{{else}}Off{{/if}}
CULTURAL_INFLECTION_2: [B] Russian, {{#if culturalInflections.russian}}{{culturalInflections.russian}}%{{else}}Off{{/if}}
CULTURAL_INFLECTION_3: [C] German, {{#if culturalInflections.german}}{{culturalInflections.german}}%{{else}}Off{{/if}}
CULTURAL_INFLECTION_4: [D] Japanese, {{#if culturalInflections.japanese}}{{culturalInflections.japanese}}%{{else}}Off{{/if}}
CULTURAL_INFLECTION_5: [E] French, {{#if culturalInflections.french}}{{culturalInflections.french}}%{{else}}Off{{/if}}
TOPIC: {{{topic}}}
`,
});

const composeDocumentFlow = ai.defineFlow(
  {
    name: 'composeDocumentFlow',
    inputSchema: ComposeDocumentInputSchema,
    outputSchema: ComposeDocumentOutputSchema,
  },
  async (input) => {
    const styleMapping: {[key: string]: string} = {
        "Argumentative": "Argumentative / Persuasive",
        "Exploratory": "Exploratory / Reflective",
        "Schematic": "Schematic / Referential"
    };
    const mappedStyle = styleMapping[input.writingStyle] || input.writingStyle;

    const {output} = await prompt({
      ...input,
      writingStyle: mappedStyle,
    });
    return output!;
  }
);
