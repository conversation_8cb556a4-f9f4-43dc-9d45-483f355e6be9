import { NextRequest, NextResponse } from 'next/server';
import { Ollama } from 'ollama';

const ollama = new Ollama({ host: 'http://localhost:11434' });

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action');

  try {
    switch (action) {
      case 'list':
        const models = await ollama.list();
        return NextResponse.json({ models: models.models });

      case 'ps':
        const runningModels = await ollama.ps();
        return NextResponse.json(runningModels);

      case 'check':
        await ollama.list();
        return NextResponse.json({ connected: true });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Ollama API error:', error);
    return NextResponse.json({ 
      error: 'Failed to connect to Ollama. Make sure Ollama is running on localhost:11434',
      connected: false 
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, model, prompt, options } = body;

    switch (action) {
      case 'generate':
        const response = await ollama.generate({
          model: model || 'llama3.2:3b',
          prompt,
          stream: false,
          options: options || {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
          }
        });
        
        return NextResponse.json({ content: response.response });

      case 'pull':
        // For pulling models - this would be a streaming operation
        return NextResponse.json({ message: 'Model pull initiated' });

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 });
    }
  } catch (error) {
    console.error('Ollama API error:', error);
    return NextResponse.json({ 
      error: 'Failed to process request',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
