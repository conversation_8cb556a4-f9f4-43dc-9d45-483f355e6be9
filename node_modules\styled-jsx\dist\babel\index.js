var require$$1 = require('path');
var require$$0 = require('fs');
var require$$0$1 = require('buffer');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var require$$1__default = /*#__PURE__*/_interopDefaultLegacy(require$$1);
var require$$0__default = /*#__PURE__*/_interopDefaultLegacy(require$$0);
var require$$0__default$1 = /*#__PURE__*/_interopDefaultLegacy(require$$0$1);

var lib$3 = {};

var lib$2 = {};

function _createForOfIteratorHelperLoose$k(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
Object.defineProperty(lib$2, "__esModule", {
    value: true
});
lib$2.declare = declare;
lib$2.declarePreset = void 0;
function declare(builder) {
    return function(api, options, dirname) {
        var _clonedApi2;
        var clonedApi;
        for(var _iterator = _createForOfIteratorHelperLoose$k(Object.keys(apiPolyfills)), _step; !(_step = _iterator()).done;){
            var name = _step.value;
            var _clonedApi;
            if (api[name]) continue;
            clonedApi = (_clonedApi = clonedApi) != null ? _clonedApi : copyApiObject(api);
            clonedApi[name] = apiPolyfills[name](clonedApi);
        }
        return builder((_clonedApi2 = clonedApi) != null ? _clonedApi2 : api, options || {}, dirname);
    };
}
var declarePreset = declare;
lib$2.declarePreset = declarePreset;
var apiPolyfills = {
    assertVersion: function(api) {
        return function(range) {
            throwVersionError(range, api.version);
        };
    },
    targets: function() {
        return function() {
            return {};
        };
    },
    assumption: function() {
        return function() {
            return undefined;
        };
    }
};
function copyApiObject(api) {
    var proto = null;
    if (typeof api.version === "string" && /^7\./.test(api.version)) {
        proto = Object.getPrototypeOf(api);
        if (proto && (!has$1(proto, "version") || !has$1(proto, "transform") || !has$1(proto, "template") || !has$1(proto, "types"))) {
            proto = null;
        }
    }
    return Object.assign({}, proto, api);
}
function has$1(obj, key) {
    return Object.prototype.hasOwnProperty.call(obj, key);
}
function throwVersionError(range, version) {
    if (typeof range === "number") {
        if (!Number.isInteger(range)) {
            throw new Error("Expected string or integer value.");
        }
        range = "^" + range + ".0.0-0";
    }
    if (typeof range !== "string") {
        throw new Error("Expected string or integer value.");
    }
    var limit = Error.stackTraceLimit;
    if (typeof limit === "number" && limit < 25) {
        Error.stackTraceLimit = 25;
    }
    var err;
    if (version.slice(0, 2) === "7.") {
        err = new Error('Requires Babel "^7.0.0-beta.41", but was loaded with "' + version + '". ' + "You'll need to update your @babel/core version.");
    } else {
        err = new Error('Requires Babel "' + range + '", but was loaded with "' + version + '". ' + "If you are sure you have a compatible version of @babel/core, " + "it is likely that something in your build process is loading the " + "wrong version. Inspect the stack trace of this error to look for " + 'the first entry that doesn\'t mention "@babel/core" or "babel-core" ' + "to see what is calling Babel.");
    }
    if (typeof limit === "number") {
        Error.stackTraceLimit = limit;
    }
    throw Object.assign(err, {
        code: "BABEL_VERSION_UNSUPPORTED",
        version: version,
        range: range
    });
}

Object.defineProperty(lib$3, "__esModule", {
    value: true
});
var default_1 = lib$3.default = void 0;
var _helperPluginUtils = lib$2;
var _default$5 = (0, _helperPluginUtils.declare)(function(api) {
    api.assertVersion(7);
    return {
        name: "syntax-jsx",
        manipulateOptions: function manipulateOptions(opts, parserOpts) {
            if (parserOpts.plugins.some(function(p) {
                return (Array.isArray(p) ? p[0] : p) === "typescript";
            })) {
                return;
            }
            parserOpts.plugins.push("jsx");
        }
    };
});
default_1 = lib$3.default = _default$5;

var lib$1 = {};

var isReactComponent$1 = {};

var buildMatchMemberExpression$1 = {};

var matchesPattern$1 = {};

var generated$4 = {};

var shallowEqual$1 = {};

function _createForOfIteratorHelperLoose$j(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
Object.defineProperty(shallowEqual$1, "__esModule", {
    value: true
});
shallowEqual$1.default = shallowEqual;
function shallowEqual(actual, expected) {
    var keys = Object.keys(expected);
    for(var _iterator = _createForOfIteratorHelperLoose$j(keys), _step; !(_step = _iterator()).done;){
        var key = _step.value;
        if (actual[key] !== expected[key]) {
            return false;
        }
    }
    return true;
}

Object.defineProperty(generated$4, "__esModule", {
    value: true
});
generated$4.isArrayExpression = isArrayExpression;
generated$4.isAssignmentExpression = isAssignmentExpression;
generated$4.isBinaryExpression = isBinaryExpression;
generated$4.isInterpreterDirective = isInterpreterDirective;
generated$4.isDirective = isDirective;
generated$4.isDirectiveLiteral = isDirectiveLiteral;
generated$4.isBlockStatement = isBlockStatement;
generated$4.isBreakStatement = isBreakStatement;
generated$4.isCallExpression = isCallExpression;
generated$4.isCatchClause = isCatchClause;
generated$4.isConditionalExpression = isConditionalExpression;
generated$4.isContinueStatement = isContinueStatement;
generated$4.isDebuggerStatement = isDebuggerStatement;
generated$4.isDoWhileStatement = isDoWhileStatement;
generated$4.isEmptyStatement = isEmptyStatement;
generated$4.isExpressionStatement = isExpressionStatement;
generated$4.isFile = isFile;
generated$4.isForInStatement = isForInStatement;
generated$4.isForStatement = isForStatement;
generated$4.isFunctionDeclaration = isFunctionDeclaration;
generated$4.isFunctionExpression = isFunctionExpression;
generated$4.isIdentifier = isIdentifier;
generated$4.isIfStatement = isIfStatement;
generated$4.isLabeledStatement = isLabeledStatement;
generated$4.isStringLiteral = isStringLiteral;
generated$4.isNumericLiteral = isNumericLiteral;
generated$4.isNullLiteral = isNullLiteral;
generated$4.isBooleanLiteral = isBooleanLiteral;
generated$4.isRegExpLiteral = isRegExpLiteral;
generated$4.isLogicalExpression = isLogicalExpression;
generated$4.isMemberExpression = isMemberExpression;
generated$4.isNewExpression = isNewExpression;
generated$4.isProgram = isProgram;
generated$4.isObjectExpression = isObjectExpression;
generated$4.isObjectMethod = isObjectMethod;
generated$4.isObjectProperty = isObjectProperty;
generated$4.isRestElement = isRestElement;
generated$4.isReturnStatement = isReturnStatement;
generated$4.isSequenceExpression = isSequenceExpression;
generated$4.isParenthesizedExpression = isParenthesizedExpression;
generated$4.isSwitchCase = isSwitchCase;
generated$4.isSwitchStatement = isSwitchStatement;
generated$4.isThisExpression = isThisExpression;
generated$4.isThrowStatement = isThrowStatement;
generated$4.isTryStatement = isTryStatement;
generated$4.isUnaryExpression = isUnaryExpression;
generated$4.isUpdateExpression = isUpdateExpression;
generated$4.isVariableDeclaration = isVariableDeclaration;
generated$4.isVariableDeclarator = isVariableDeclarator;
generated$4.isWhileStatement = isWhileStatement;
generated$4.isWithStatement = isWithStatement;
generated$4.isAssignmentPattern = isAssignmentPattern;
generated$4.isArrayPattern = isArrayPattern;
generated$4.isArrowFunctionExpression = isArrowFunctionExpression;
generated$4.isClassBody = isClassBody;
generated$4.isClassExpression = isClassExpression;
generated$4.isClassDeclaration = isClassDeclaration;
generated$4.isExportAllDeclaration = isExportAllDeclaration;
generated$4.isExportDefaultDeclaration = isExportDefaultDeclaration;
generated$4.isExportNamedDeclaration = isExportNamedDeclaration;
generated$4.isExportSpecifier = isExportSpecifier;
generated$4.isForOfStatement = isForOfStatement;
generated$4.isImportDeclaration = isImportDeclaration;
generated$4.isImportDefaultSpecifier = isImportDefaultSpecifier;
generated$4.isImportNamespaceSpecifier = isImportNamespaceSpecifier;
generated$4.isImportSpecifier = isImportSpecifier;
generated$4.isMetaProperty = isMetaProperty;
generated$4.isClassMethod = isClassMethod;
generated$4.isObjectPattern = isObjectPattern;
generated$4.isSpreadElement = isSpreadElement;
generated$4.isSuper = isSuper;
generated$4.isTaggedTemplateExpression = isTaggedTemplateExpression;
generated$4.isTemplateElement = isTemplateElement;
generated$4.isTemplateLiteral = isTemplateLiteral;
generated$4.isYieldExpression = isYieldExpression;
generated$4.isAwaitExpression = isAwaitExpression;
generated$4.isImport = isImport;
generated$4.isBigIntLiteral = isBigIntLiteral;
generated$4.isExportNamespaceSpecifier = isExportNamespaceSpecifier;
generated$4.isOptionalMemberExpression = isOptionalMemberExpression;
generated$4.isOptionalCallExpression = isOptionalCallExpression;
generated$4.isClassProperty = isClassProperty;
generated$4.isClassPrivateProperty = isClassPrivateProperty;
generated$4.isClassPrivateMethod = isClassPrivateMethod;
generated$4.isPrivateName = isPrivateName;
generated$4.isAnyTypeAnnotation = isAnyTypeAnnotation;
generated$4.isArrayTypeAnnotation = isArrayTypeAnnotation;
generated$4.isBooleanTypeAnnotation = isBooleanTypeAnnotation;
generated$4.isBooleanLiteralTypeAnnotation = isBooleanLiteralTypeAnnotation;
generated$4.isNullLiteralTypeAnnotation = isNullLiteralTypeAnnotation;
generated$4.isClassImplements = isClassImplements;
generated$4.isDeclareClass = isDeclareClass;
generated$4.isDeclareFunction = isDeclareFunction;
generated$4.isDeclareInterface = isDeclareInterface;
generated$4.isDeclareModule = isDeclareModule;
generated$4.isDeclareModuleExports = isDeclareModuleExports;
generated$4.isDeclareTypeAlias = isDeclareTypeAlias;
generated$4.isDeclareOpaqueType = isDeclareOpaqueType;
generated$4.isDeclareVariable = isDeclareVariable;
generated$4.isDeclareExportDeclaration = isDeclareExportDeclaration;
generated$4.isDeclareExportAllDeclaration = isDeclareExportAllDeclaration;
generated$4.isDeclaredPredicate = isDeclaredPredicate;
generated$4.isExistsTypeAnnotation = isExistsTypeAnnotation;
generated$4.isFunctionTypeAnnotation = isFunctionTypeAnnotation;
generated$4.isFunctionTypeParam = isFunctionTypeParam;
generated$4.isGenericTypeAnnotation = isGenericTypeAnnotation;
generated$4.isInferredPredicate = isInferredPredicate;
generated$4.isInterfaceExtends = isInterfaceExtends;
generated$4.isInterfaceDeclaration = isInterfaceDeclaration;
generated$4.isInterfaceTypeAnnotation = isInterfaceTypeAnnotation;
generated$4.isIntersectionTypeAnnotation = isIntersectionTypeAnnotation;
generated$4.isMixedTypeAnnotation = isMixedTypeAnnotation;
generated$4.isEmptyTypeAnnotation = isEmptyTypeAnnotation;
generated$4.isNullableTypeAnnotation = isNullableTypeAnnotation;
generated$4.isNumberLiteralTypeAnnotation = isNumberLiteralTypeAnnotation;
generated$4.isNumberTypeAnnotation = isNumberTypeAnnotation;
generated$4.isObjectTypeAnnotation = isObjectTypeAnnotation;
generated$4.isObjectTypeInternalSlot = isObjectTypeInternalSlot;
generated$4.isObjectTypeCallProperty = isObjectTypeCallProperty;
generated$4.isObjectTypeIndexer = isObjectTypeIndexer;
generated$4.isObjectTypeProperty = isObjectTypeProperty;
generated$4.isObjectTypeSpreadProperty = isObjectTypeSpreadProperty;
generated$4.isOpaqueType = isOpaqueType;
generated$4.isQualifiedTypeIdentifier = isQualifiedTypeIdentifier;
generated$4.isStringLiteralTypeAnnotation = isStringLiteralTypeAnnotation;
generated$4.isStringTypeAnnotation = isStringTypeAnnotation;
generated$4.isSymbolTypeAnnotation = isSymbolTypeAnnotation;
generated$4.isThisTypeAnnotation = isThisTypeAnnotation;
generated$4.isTupleTypeAnnotation = isTupleTypeAnnotation;
generated$4.isTypeofTypeAnnotation = isTypeofTypeAnnotation;
generated$4.isTypeAlias = isTypeAlias;
generated$4.isTypeAnnotation = isTypeAnnotation;
generated$4.isTypeCastExpression = isTypeCastExpression;
generated$4.isTypeParameter = isTypeParameter;
generated$4.isTypeParameterDeclaration = isTypeParameterDeclaration;
generated$4.isTypeParameterInstantiation = isTypeParameterInstantiation;
generated$4.isUnionTypeAnnotation = isUnionTypeAnnotation;
generated$4.isVariance = isVariance;
generated$4.isVoidTypeAnnotation = isVoidTypeAnnotation;
generated$4.isEnumDeclaration = isEnumDeclaration;
generated$4.isEnumBooleanBody = isEnumBooleanBody;
generated$4.isEnumNumberBody = isEnumNumberBody;
generated$4.isEnumStringBody = isEnumStringBody;
generated$4.isEnumSymbolBody = isEnumSymbolBody;
generated$4.isEnumBooleanMember = isEnumBooleanMember;
generated$4.isEnumNumberMember = isEnumNumberMember;
generated$4.isEnumStringMember = isEnumStringMember;
generated$4.isEnumDefaultedMember = isEnumDefaultedMember;
generated$4.isIndexedAccessType = isIndexedAccessType;
generated$4.isOptionalIndexedAccessType = isOptionalIndexedAccessType;
generated$4.isJSXAttribute = isJSXAttribute;
generated$4.isJSXClosingElement = isJSXClosingElement;
generated$4.isJSXElement = isJSXElement;
generated$4.isJSXEmptyExpression = isJSXEmptyExpression;
generated$4.isJSXExpressionContainer = isJSXExpressionContainer;
generated$4.isJSXSpreadChild = isJSXSpreadChild;
generated$4.isJSXIdentifier = isJSXIdentifier;
generated$4.isJSXMemberExpression = isJSXMemberExpression;
generated$4.isJSXNamespacedName = isJSXNamespacedName;
generated$4.isJSXOpeningElement = isJSXOpeningElement;
generated$4.isJSXSpreadAttribute = isJSXSpreadAttribute;
generated$4.isJSXText = isJSXText;
generated$4.isJSXFragment = isJSXFragment;
generated$4.isJSXOpeningFragment = isJSXOpeningFragment;
generated$4.isJSXClosingFragment = isJSXClosingFragment;
generated$4.isNoop = isNoop;
generated$4.isPlaceholder = isPlaceholder;
generated$4.isV8IntrinsicIdentifier = isV8IntrinsicIdentifier;
generated$4.isArgumentPlaceholder = isArgumentPlaceholder;
generated$4.isBindExpression = isBindExpression;
generated$4.isImportAttribute = isImportAttribute;
generated$4.isDecorator = isDecorator;
generated$4.isDoExpression = isDoExpression;
generated$4.isExportDefaultSpecifier = isExportDefaultSpecifier;
generated$4.isRecordExpression = isRecordExpression;
generated$4.isTupleExpression = isTupleExpression;
generated$4.isDecimalLiteral = isDecimalLiteral;
generated$4.isStaticBlock = isStaticBlock;
generated$4.isModuleExpression = isModuleExpression;
generated$4.isTopicReference = isTopicReference;
generated$4.isPipelineTopicExpression = isPipelineTopicExpression;
generated$4.isPipelineBareFunction = isPipelineBareFunction;
generated$4.isPipelinePrimaryTopicReference = isPipelinePrimaryTopicReference;
generated$4.isTSParameterProperty = isTSParameterProperty;
generated$4.isTSDeclareFunction = isTSDeclareFunction;
generated$4.isTSDeclareMethod = isTSDeclareMethod;
generated$4.isTSQualifiedName = isTSQualifiedName;
generated$4.isTSCallSignatureDeclaration = isTSCallSignatureDeclaration;
generated$4.isTSConstructSignatureDeclaration = isTSConstructSignatureDeclaration;
generated$4.isTSPropertySignature = isTSPropertySignature;
generated$4.isTSMethodSignature = isTSMethodSignature;
generated$4.isTSIndexSignature = isTSIndexSignature;
generated$4.isTSAnyKeyword = isTSAnyKeyword;
generated$4.isTSBooleanKeyword = isTSBooleanKeyword;
generated$4.isTSBigIntKeyword = isTSBigIntKeyword;
generated$4.isTSIntrinsicKeyword = isTSIntrinsicKeyword;
generated$4.isTSNeverKeyword = isTSNeverKeyword;
generated$4.isTSNullKeyword = isTSNullKeyword;
generated$4.isTSNumberKeyword = isTSNumberKeyword;
generated$4.isTSObjectKeyword = isTSObjectKeyword;
generated$4.isTSStringKeyword = isTSStringKeyword;
generated$4.isTSSymbolKeyword = isTSSymbolKeyword;
generated$4.isTSUndefinedKeyword = isTSUndefinedKeyword;
generated$4.isTSUnknownKeyword = isTSUnknownKeyword;
generated$4.isTSVoidKeyword = isTSVoidKeyword;
generated$4.isTSThisType = isTSThisType;
generated$4.isTSFunctionType = isTSFunctionType;
generated$4.isTSConstructorType = isTSConstructorType;
generated$4.isTSTypeReference = isTSTypeReference;
generated$4.isTSTypePredicate = isTSTypePredicate;
generated$4.isTSTypeQuery = isTSTypeQuery;
generated$4.isTSTypeLiteral = isTSTypeLiteral;
generated$4.isTSArrayType = isTSArrayType;
generated$4.isTSTupleType = isTSTupleType;
generated$4.isTSOptionalType = isTSOptionalType;
generated$4.isTSRestType = isTSRestType;
generated$4.isTSNamedTupleMember = isTSNamedTupleMember;
generated$4.isTSUnionType = isTSUnionType;
generated$4.isTSIntersectionType = isTSIntersectionType;
generated$4.isTSConditionalType = isTSConditionalType;
generated$4.isTSInferType = isTSInferType;
generated$4.isTSParenthesizedType = isTSParenthesizedType;
generated$4.isTSTypeOperator = isTSTypeOperator;
generated$4.isTSIndexedAccessType = isTSIndexedAccessType;
generated$4.isTSMappedType = isTSMappedType;
generated$4.isTSLiteralType = isTSLiteralType;
generated$4.isTSExpressionWithTypeArguments = isTSExpressionWithTypeArguments;
generated$4.isTSInterfaceDeclaration = isTSInterfaceDeclaration;
generated$4.isTSInterfaceBody = isTSInterfaceBody;
generated$4.isTSTypeAliasDeclaration = isTSTypeAliasDeclaration;
generated$4.isTSAsExpression = isTSAsExpression;
generated$4.isTSTypeAssertion = isTSTypeAssertion;
generated$4.isTSEnumDeclaration = isTSEnumDeclaration;
generated$4.isTSEnumMember = isTSEnumMember;
generated$4.isTSModuleDeclaration = isTSModuleDeclaration;
generated$4.isTSModuleBlock = isTSModuleBlock;
generated$4.isTSImportType = isTSImportType;
generated$4.isTSImportEqualsDeclaration = isTSImportEqualsDeclaration;
generated$4.isTSExternalModuleReference = isTSExternalModuleReference;
generated$4.isTSNonNullExpression = isTSNonNullExpression;
generated$4.isTSExportAssignment = isTSExportAssignment;
generated$4.isTSNamespaceExportDeclaration = isTSNamespaceExportDeclaration;
generated$4.isTSTypeAnnotation = isTSTypeAnnotation;
generated$4.isTSTypeParameterInstantiation = isTSTypeParameterInstantiation;
generated$4.isTSTypeParameterDeclaration = isTSTypeParameterDeclaration;
generated$4.isTSTypeParameter = isTSTypeParameter;
generated$4.isExpression = isExpression;
generated$4.isBinary = isBinary;
generated$4.isScopable = isScopable;
generated$4.isBlockParent = isBlockParent;
generated$4.isBlock = isBlock;
generated$4.isStatement = isStatement;
generated$4.isTerminatorless = isTerminatorless;
generated$4.isCompletionStatement = isCompletionStatement;
generated$4.isConditional = isConditional;
generated$4.isLoop = isLoop;
generated$4.isWhile = isWhile;
generated$4.isExpressionWrapper = isExpressionWrapper;
generated$4.isFor = isFor;
generated$4.isForXStatement = isForXStatement;
generated$4.isFunction = isFunction;
generated$4.isFunctionParent = isFunctionParent;
generated$4.isPureish = isPureish;
generated$4.isDeclaration = isDeclaration;
generated$4.isPatternLike = isPatternLike;
generated$4.isLVal = isLVal;
generated$4.isTSEntityName = isTSEntityName;
generated$4.isLiteral = isLiteral;
generated$4.isImmutable = isImmutable$2;
generated$4.isUserWhitespacable = isUserWhitespacable;
generated$4.isMethod = isMethod;
generated$4.isObjectMember = isObjectMember;
generated$4.isProperty = isProperty;
generated$4.isUnaryLike = isUnaryLike;
generated$4.isPattern = isPattern;
generated$4.isClass = isClass;
generated$4.isModuleDeclaration = isModuleDeclaration;
generated$4.isExportDeclaration = isExportDeclaration;
generated$4.isModuleSpecifier = isModuleSpecifier;
generated$4.isPrivate = isPrivate;
generated$4.isFlow = isFlow;
generated$4.isFlowType = isFlowType;
generated$4.isFlowBaseAnnotation = isFlowBaseAnnotation;
generated$4.isFlowDeclaration = isFlowDeclaration;
generated$4.isFlowPredicate = isFlowPredicate;
generated$4.isEnumBody = isEnumBody;
generated$4.isEnumMember = isEnumMember;
generated$4.isJSX = isJSX;
generated$4.isTSTypeElement = isTSTypeElement;
generated$4.isTSType = isTSType;
generated$4.isTSBaseType = isTSBaseType;
generated$4.isNumberLiteral = isNumberLiteral;
generated$4.isRegexLiteral = isRegexLiteral;
generated$4.isRestProperty = isRestProperty;
generated$4.isSpreadProperty = isSpreadProperty;
var _shallowEqual = shallowEqual$1;
function isArrayExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ArrayExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isAssignmentExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "AssignmentExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBinaryExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BinaryExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isInterpreterDirective(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "InterpreterDirective") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDirective(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Directive") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDirectiveLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DirectiveLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBlockStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BlockStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBreakStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BreakStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isCallExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "CallExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isCatchClause(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "CatchClause") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isConditionalExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ConditionalExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isContinueStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ContinueStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDebuggerStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DebuggerStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDoWhileStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DoWhileStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEmptyStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EmptyStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExpressionStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExpressionStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFile(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "File") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isForInStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ForInStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isForStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ForStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunctionDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "FunctionDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunctionExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "FunctionExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isIdentifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Identifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isIfStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "IfStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isLabeledStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "LabeledStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isStringLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "StringLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNumericLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NumericLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNullLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NullLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBooleanLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BooleanLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isRegExpLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "RegExpLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isLogicalExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "LogicalExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isMemberExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "MemberExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNewExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NewExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isProgram(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Program") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectMethod(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectMethod") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isRestElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "RestElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isReturnStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ReturnStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSequenceExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SequenceExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isParenthesizedExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ParenthesizedExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSwitchCase(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SwitchCase") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSwitchStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SwitchStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isThisExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ThisExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isThrowStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ThrowStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTryStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TryStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isUnaryExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "UnaryExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isUpdateExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "UpdateExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isVariableDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "VariableDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isVariableDeclarator(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "VariableDeclarator") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isWhileStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "WhileStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isWithStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "WithStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isAssignmentPattern(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "AssignmentPattern") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isArrayPattern(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ArrayPattern") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isArrowFunctionExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ArrowFunctionExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportAllDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportAllDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportDefaultDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportDefaultDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportNamedDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportNamedDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isForOfStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ForOfStatement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImportDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ImportDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImportDefaultSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ImportDefaultSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImportNamespaceSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ImportNamespaceSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImportSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ImportSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isMetaProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "MetaProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassMethod(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassMethod") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectPattern(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectPattern") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSpreadElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SpreadElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSuper(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Super") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTaggedTemplateExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TaggedTemplateExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTemplateElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TemplateElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTemplateLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TemplateLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isYieldExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "YieldExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isAwaitExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "AwaitExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImport(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Import") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBigIntLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BigIntLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportNamespaceSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportNamespaceSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isOptionalMemberExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "OptionalMemberExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isOptionalCallExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "OptionalCallExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassPrivateProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassPrivateProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassPrivateMethod(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassPrivateMethod") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPrivateName(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "PrivateName") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isAnyTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "AnyTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isArrayTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ArrayTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBooleanTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BooleanTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBooleanLiteralTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BooleanLiteralTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNullLiteralTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NullLiteralTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClassImplements(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ClassImplements") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareClass(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareClass") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareFunction(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareFunction") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareInterface(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareInterface") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareModule(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareModule") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareModuleExports(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareModuleExports") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareTypeAlias(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareTypeAlias") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareOpaqueType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareOpaqueType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareVariable(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareVariable") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareExportDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareExportDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclareExportAllDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclareExportAllDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclaredPredicate(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DeclaredPredicate") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExistsTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExistsTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunctionTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "FunctionTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunctionTypeParam(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "FunctionTypeParam") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isGenericTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "GenericTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isInferredPredicate(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "InferredPredicate") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isInterfaceExtends(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "InterfaceExtends") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isInterfaceDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "InterfaceDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isInterfaceTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "InterfaceTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isIntersectionTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "IntersectionTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isMixedTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "MixedTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEmptyTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EmptyTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNullableTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NullableTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNumberLiteralTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NumberLiteralTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNumberTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NumberTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeInternalSlot(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeInternalSlot") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeCallProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeCallProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeIndexer(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeIndexer") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectTypeSpreadProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ObjectTypeSpreadProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isOpaqueType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "OpaqueType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isQualifiedTypeIdentifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "QualifiedTypeIdentifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isStringLiteralTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "StringLiteralTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isStringTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "StringTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSymbolTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SymbolTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isThisTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ThisTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTupleTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TupleTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeofTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeofTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeAlias(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeAlias") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeCastExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeCastExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeParameter(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeParameter") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeParameterDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeParameterDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTypeParameterInstantiation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TypeParameterInstantiation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isUnionTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "UnionTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isVariance(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Variance") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isVoidTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "VoidTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumBooleanBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumBooleanBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumNumberBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumNumberBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumStringBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumStringBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumSymbolBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumSymbolBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumBooleanMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumBooleanMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumNumberMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumNumberMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumStringMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumStringMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumDefaultedMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "EnumDefaultedMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isIndexedAccessType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "IndexedAccessType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isOptionalIndexedAccessType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "OptionalIndexedAccessType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXAttribute(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXAttribute") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXClosingElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXClosingElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXEmptyExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXEmptyExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXExpressionContainer(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXExpressionContainer") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXSpreadChild(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXSpreadChild") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXIdentifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXIdentifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXMemberExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXMemberExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXNamespacedName(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXNamespacedName") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXOpeningElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXOpeningElement") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXSpreadAttribute(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXSpreadAttribute") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXText(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXText") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXFragment(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXFragment") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXOpeningFragment(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXOpeningFragment") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSXClosingFragment(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "JSXClosingFragment") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNoop(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Noop") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPlaceholder(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Placeholder") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isV8IntrinsicIdentifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "V8IntrinsicIdentifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isArgumentPlaceholder(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ArgumentPlaceholder") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBindExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "BindExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImportAttribute(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ImportAttribute") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDecorator(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "Decorator") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDoExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DoExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportDefaultSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ExportDefaultSpecifier") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isRecordExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "RecordExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTupleExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TupleExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDecimalLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "DecimalLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isStaticBlock(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "StaticBlock") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isModuleExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "ModuleExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTopicReference(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TopicReference") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPipelineTopicExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "PipelineTopicExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPipelineBareFunction(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "PipelineBareFunction") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPipelinePrimaryTopicReference(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "PipelinePrimaryTopicReference") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSParameterProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSParameterProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSDeclareFunction(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSDeclareFunction") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSDeclareMethod(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSDeclareMethod") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSQualifiedName(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSQualifiedName") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSCallSignatureDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSCallSignatureDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSConstructSignatureDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSConstructSignatureDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSPropertySignature(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSPropertySignature") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSMethodSignature(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSMethodSignature") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSIndexSignature(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSIndexSignature") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSAnyKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSAnyKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSBooleanKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSBooleanKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSBigIntKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSBigIntKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSIntrinsicKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSIntrinsicKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNeverKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNeverKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNullKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNullKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNumberKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNumberKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSObjectKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSObjectKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSStringKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSStringKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSSymbolKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSSymbolKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSUndefinedKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSUndefinedKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSUnknownKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSUnknownKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSVoidKeyword(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSVoidKeyword") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSThisType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSThisType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSFunctionType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSFunctionType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSConstructorType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSConstructorType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeReference(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeReference") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypePredicate(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypePredicate") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeQuery(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeQuery") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSArrayType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSArrayType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTupleType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTupleType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSOptionalType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSOptionalType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSRestType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSRestType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNamedTupleMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNamedTupleMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSUnionType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSUnionType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSIntersectionType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSIntersectionType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSConditionalType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSConditionalType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSInferType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSInferType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSParenthesizedType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSParenthesizedType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeOperator(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeOperator") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSIndexedAccessType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSIndexedAccessType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSMappedType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSMappedType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSLiteralType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSLiteralType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSExpressionWithTypeArguments(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSExpressionWithTypeArguments") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSInterfaceDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSInterfaceDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSInterfaceBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSInterfaceBody") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeAliasDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeAliasDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSAsExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSAsExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeAssertion(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeAssertion") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSEnumDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSEnumDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSEnumMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSEnumMember") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSModuleDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSModuleDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSModuleBlock(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSModuleBlock") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSImportType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSImportType") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSImportEqualsDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSImportEqualsDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSExternalModuleReference(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSExternalModuleReference") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNonNullExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNonNullExpression") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSExportAssignment(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSExportAssignment") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSNamespaceExportDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSNamespaceExportDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeAnnotation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeParameterInstantiation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeParameterInstantiation") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeParameterDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeParameterDeclaration") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeParameter(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "TSTypeParameter") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExpression(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ArrayExpression" === nodeType || "AssignmentExpression" === nodeType || "BinaryExpression" === nodeType || "CallExpression" === nodeType || "ConditionalExpression" === nodeType || "FunctionExpression" === nodeType || "Identifier" === nodeType || "StringLiteral" === nodeType || "NumericLiteral" === nodeType || "NullLiteral" === nodeType || "BooleanLiteral" === nodeType || "RegExpLiteral" === nodeType || "LogicalExpression" === nodeType || "MemberExpression" === nodeType || "NewExpression" === nodeType || "ObjectExpression" === nodeType || "SequenceExpression" === nodeType || "ParenthesizedExpression" === nodeType || "ThisExpression" === nodeType || "UnaryExpression" === nodeType || "UpdateExpression" === nodeType || "ArrowFunctionExpression" === nodeType || "ClassExpression" === nodeType || "MetaProperty" === nodeType || "Super" === nodeType || "TaggedTemplateExpression" === nodeType || "TemplateLiteral" === nodeType || "YieldExpression" === nodeType || "AwaitExpression" === nodeType || "Import" === nodeType || "BigIntLiteral" === nodeType || "OptionalMemberExpression" === nodeType || "OptionalCallExpression" === nodeType || "TypeCastExpression" === nodeType || "JSXElement" === nodeType || "JSXFragment" === nodeType || "BindExpression" === nodeType || "DoExpression" === nodeType || "RecordExpression" === nodeType || "TupleExpression" === nodeType || "DecimalLiteral" === nodeType || "ModuleExpression" === nodeType || "TopicReference" === nodeType || "PipelineTopicExpression" === nodeType || "PipelineBareFunction" === nodeType || "PipelinePrimaryTopicReference" === nodeType || "TSAsExpression" === nodeType || "TSTypeAssertion" === nodeType || "TSNonNullExpression" === nodeType || nodeType === "Placeholder" && ("Expression" === node.expectedNode || "Identifier" === node.expectedNode || "StringLiteral" === node.expectedNode)) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBinary(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BinaryExpression" === nodeType || "LogicalExpression" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isScopable(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BlockStatement" === nodeType || "CatchClause" === nodeType || "DoWhileStatement" === nodeType || "ForInStatement" === nodeType || "ForStatement" === nodeType || "FunctionDeclaration" === nodeType || "FunctionExpression" === nodeType || "Program" === nodeType || "ObjectMethod" === nodeType || "SwitchStatement" === nodeType || "WhileStatement" === nodeType || "ArrowFunctionExpression" === nodeType || "ClassExpression" === nodeType || "ClassDeclaration" === nodeType || "ForOfStatement" === nodeType || "ClassMethod" === nodeType || "ClassPrivateMethod" === nodeType || "StaticBlock" === nodeType || "TSModuleBlock" === nodeType || nodeType === "Placeholder" && "BlockStatement" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBlockParent(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BlockStatement" === nodeType || "CatchClause" === nodeType || "DoWhileStatement" === nodeType || "ForInStatement" === nodeType || "ForStatement" === nodeType || "FunctionDeclaration" === nodeType || "FunctionExpression" === nodeType || "Program" === nodeType || "ObjectMethod" === nodeType || "SwitchStatement" === nodeType || "WhileStatement" === nodeType || "ArrowFunctionExpression" === nodeType || "ForOfStatement" === nodeType || "ClassMethod" === nodeType || "ClassPrivateMethod" === nodeType || "StaticBlock" === nodeType || "TSModuleBlock" === nodeType || nodeType === "Placeholder" && "BlockStatement" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isBlock(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BlockStatement" === nodeType || "Program" === nodeType || "TSModuleBlock" === nodeType || nodeType === "Placeholder" && "BlockStatement" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BlockStatement" === nodeType || "BreakStatement" === nodeType || "ContinueStatement" === nodeType || "DebuggerStatement" === nodeType || "DoWhileStatement" === nodeType || "EmptyStatement" === nodeType || "ExpressionStatement" === nodeType || "ForInStatement" === nodeType || "ForStatement" === nodeType || "FunctionDeclaration" === nodeType || "IfStatement" === nodeType || "LabeledStatement" === nodeType || "ReturnStatement" === nodeType || "SwitchStatement" === nodeType || "ThrowStatement" === nodeType || "TryStatement" === nodeType || "VariableDeclaration" === nodeType || "WhileStatement" === nodeType || "WithStatement" === nodeType || "ClassDeclaration" === nodeType || "ExportAllDeclaration" === nodeType || "ExportDefaultDeclaration" === nodeType || "ExportNamedDeclaration" === nodeType || "ForOfStatement" === nodeType || "ImportDeclaration" === nodeType || "DeclareClass" === nodeType || "DeclareFunction" === nodeType || "DeclareInterface" === nodeType || "DeclareModule" === nodeType || "DeclareModuleExports" === nodeType || "DeclareTypeAlias" === nodeType || "DeclareOpaqueType" === nodeType || "DeclareVariable" === nodeType || "DeclareExportDeclaration" === nodeType || "DeclareExportAllDeclaration" === nodeType || "InterfaceDeclaration" === nodeType || "OpaqueType" === nodeType || "TypeAlias" === nodeType || "EnumDeclaration" === nodeType || "TSDeclareFunction" === nodeType || "TSInterfaceDeclaration" === nodeType || "TSTypeAliasDeclaration" === nodeType || "TSEnumDeclaration" === nodeType || "TSModuleDeclaration" === nodeType || "TSImportEqualsDeclaration" === nodeType || "TSExportAssignment" === nodeType || "TSNamespaceExportDeclaration" === nodeType || nodeType === "Placeholder" && ("Statement" === node.expectedNode || "Declaration" === node.expectedNode || "BlockStatement" === node.expectedNode)) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTerminatorless(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BreakStatement" === nodeType || "ContinueStatement" === nodeType || "ReturnStatement" === nodeType || "ThrowStatement" === nodeType || "YieldExpression" === nodeType || "AwaitExpression" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isCompletionStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("BreakStatement" === nodeType || "ContinueStatement" === nodeType || "ReturnStatement" === nodeType || "ThrowStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isConditional(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ConditionalExpression" === nodeType || "IfStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isLoop(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("DoWhileStatement" === nodeType || "ForInStatement" === nodeType || "ForStatement" === nodeType || "WhileStatement" === nodeType || "ForOfStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isWhile(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("DoWhileStatement" === nodeType || "WhileStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExpressionWrapper(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ExpressionStatement" === nodeType || "ParenthesizedExpression" === nodeType || "TypeCastExpression" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFor(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ForInStatement" === nodeType || "ForStatement" === nodeType || "ForOfStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isForXStatement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ForInStatement" === nodeType || "ForOfStatement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunction(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("FunctionDeclaration" === nodeType || "FunctionExpression" === nodeType || "ObjectMethod" === nodeType || "ArrowFunctionExpression" === nodeType || "ClassMethod" === nodeType || "ClassPrivateMethod" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFunctionParent(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("FunctionDeclaration" === nodeType || "FunctionExpression" === nodeType || "ObjectMethod" === nodeType || "ArrowFunctionExpression" === nodeType || "ClassMethod" === nodeType || "ClassPrivateMethod" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPureish(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("FunctionDeclaration" === nodeType || "FunctionExpression" === nodeType || "StringLiteral" === nodeType || "NumericLiteral" === nodeType || "NullLiteral" === nodeType || "BooleanLiteral" === nodeType || "RegExpLiteral" === nodeType || "ArrowFunctionExpression" === nodeType || "BigIntLiteral" === nodeType || "DecimalLiteral" === nodeType || nodeType === "Placeholder" && "StringLiteral" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("FunctionDeclaration" === nodeType || "VariableDeclaration" === nodeType || "ClassDeclaration" === nodeType || "ExportAllDeclaration" === nodeType || "ExportDefaultDeclaration" === nodeType || "ExportNamedDeclaration" === nodeType || "ImportDeclaration" === nodeType || "DeclareClass" === nodeType || "DeclareFunction" === nodeType || "DeclareInterface" === nodeType || "DeclareModule" === nodeType || "DeclareModuleExports" === nodeType || "DeclareTypeAlias" === nodeType || "DeclareOpaqueType" === nodeType || "DeclareVariable" === nodeType || "DeclareExportDeclaration" === nodeType || "DeclareExportAllDeclaration" === nodeType || "InterfaceDeclaration" === nodeType || "OpaqueType" === nodeType || "TypeAlias" === nodeType || "EnumDeclaration" === nodeType || "TSDeclareFunction" === nodeType || "TSInterfaceDeclaration" === nodeType || "TSTypeAliasDeclaration" === nodeType || "TSEnumDeclaration" === nodeType || "TSModuleDeclaration" === nodeType || nodeType === "Placeholder" && "Declaration" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPatternLike(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("Identifier" === nodeType || "RestElement" === nodeType || "AssignmentPattern" === nodeType || "ArrayPattern" === nodeType || "ObjectPattern" === nodeType || nodeType === "Placeholder" && ("Pattern" === node.expectedNode || "Identifier" === node.expectedNode)) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isLVal(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("Identifier" === nodeType || "MemberExpression" === nodeType || "RestElement" === nodeType || "AssignmentPattern" === nodeType || "ArrayPattern" === nodeType || "ObjectPattern" === nodeType || "TSParameterProperty" === nodeType || nodeType === "Placeholder" && ("Pattern" === node.expectedNode || "Identifier" === node.expectedNode)) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSEntityName(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("Identifier" === nodeType || "TSQualifiedName" === nodeType || nodeType === "Placeholder" && "Identifier" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isLiteral(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("StringLiteral" === nodeType || "NumericLiteral" === nodeType || "NullLiteral" === nodeType || "BooleanLiteral" === nodeType || "RegExpLiteral" === nodeType || "TemplateLiteral" === nodeType || "BigIntLiteral" === nodeType || "DecimalLiteral" === nodeType || nodeType === "Placeholder" && "StringLiteral" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isImmutable$2(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("StringLiteral" === nodeType || "NumericLiteral" === nodeType || "NullLiteral" === nodeType || "BooleanLiteral" === nodeType || "BigIntLiteral" === nodeType || "JSXAttribute" === nodeType || "JSXClosingElement" === nodeType || "JSXElement" === nodeType || "JSXExpressionContainer" === nodeType || "JSXSpreadChild" === nodeType || "JSXOpeningElement" === nodeType || "JSXText" === nodeType || "JSXFragment" === nodeType || "JSXOpeningFragment" === nodeType || "JSXClosingFragment" === nodeType || "DecimalLiteral" === nodeType || nodeType === "Placeholder" && "StringLiteral" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isUserWhitespacable(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ObjectMethod" === nodeType || "ObjectProperty" === nodeType || "ObjectTypeInternalSlot" === nodeType || "ObjectTypeCallProperty" === nodeType || "ObjectTypeIndexer" === nodeType || "ObjectTypeProperty" === nodeType || "ObjectTypeSpreadProperty" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isMethod(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ObjectMethod" === nodeType || "ClassMethod" === nodeType || "ClassPrivateMethod" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isObjectMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ObjectMethod" === nodeType || "ObjectProperty" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isProperty(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ObjectProperty" === nodeType || "ClassProperty" === nodeType || "ClassPrivateProperty" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isUnaryLike(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("UnaryExpression" === nodeType || "SpreadElement" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPattern(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("AssignmentPattern" === nodeType || "ArrayPattern" === nodeType || "ObjectPattern" === nodeType || nodeType === "Placeholder" && "Pattern" === node.expectedNode) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isClass(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ClassExpression" === nodeType || "ClassDeclaration" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isModuleDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ExportAllDeclaration" === nodeType || "ExportDefaultDeclaration" === nodeType || "ExportNamedDeclaration" === nodeType || "ImportDeclaration" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isExportDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ExportAllDeclaration" === nodeType || "ExportDefaultDeclaration" === nodeType || "ExportNamedDeclaration" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isModuleSpecifier(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ExportSpecifier" === nodeType || "ImportDefaultSpecifier" === nodeType || "ImportNamespaceSpecifier" === nodeType || "ImportSpecifier" === nodeType || "ExportNamespaceSpecifier" === nodeType || "ExportDefaultSpecifier" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isPrivate(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("ClassPrivateProperty" === nodeType || "ClassPrivateMethod" === nodeType || "PrivateName" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFlow(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("AnyTypeAnnotation" === nodeType || "ArrayTypeAnnotation" === nodeType || "BooleanTypeAnnotation" === nodeType || "BooleanLiteralTypeAnnotation" === nodeType || "NullLiteralTypeAnnotation" === nodeType || "ClassImplements" === nodeType || "DeclareClass" === nodeType || "DeclareFunction" === nodeType || "DeclareInterface" === nodeType || "DeclareModule" === nodeType || "DeclareModuleExports" === nodeType || "DeclareTypeAlias" === nodeType || "DeclareOpaqueType" === nodeType || "DeclareVariable" === nodeType || "DeclareExportDeclaration" === nodeType || "DeclareExportAllDeclaration" === nodeType || "DeclaredPredicate" === nodeType || "ExistsTypeAnnotation" === nodeType || "FunctionTypeAnnotation" === nodeType || "FunctionTypeParam" === nodeType || "GenericTypeAnnotation" === nodeType || "InferredPredicate" === nodeType || "InterfaceExtends" === nodeType || "InterfaceDeclaration" === nodeType || "InterfaceTypeAnnotation" === nodeType || "IntersectionTypeAnnotation" === nodeType || "MixedTypeAnnotation" === nodeType || "EmptyTypeAnnotation" === nodeType || "NullableTypeAnnotation" === nodeType || "NumberLiteralTypeAnnotation" === nodeType || "NumberTypeAnnotation" === nodeType || "ObjectTypeAnnotation" === nodeType || "ObjectTypeInternalSlot" === nodeType || "ObjectTypeCallProperty" === nodeType || "ObjectTypeIndexer" === nodeType || "ObjectTypeProperty" === nodeType || "ObjectTypeSpreadProperty" === nodeType || "OpaqueType" === nodeType || "QualifiedTypeIdentifier" === nodeType || "StringLiteralTypeAnnotation" === nodeType || "StringTypeAnnotation" === nodeType || "SymbolTypeAnnotation" === nodeType || "ThisTypeAnnotation" === nodeType || "TupleTypeAnnotation" === nodeType || "TypeofTypeAnnotation" === nodeType || "TypeAlias" === nodeType || "TypeAnnotation" === nodeType || "TypeCastExpression" === nodeType || "TypeParameter" === nodeType || "TypeParameterDeclaration" === nodeType || "TypeParameterInstantiation" === nodeType || "UnionTypeAnnotation" === nodeType || "Variance" === nodeType || "VoidTypeAnnotation" === nodeType || "IndexedAccessType" === nodeType || "OptionalIndexedAccessType" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFlowType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("AnyTypeAnnotation" === nodeType || "ArrayTypeAnnotation" === nodeType || "BooleanTypeAnnotation" === nodeType || "BooleanLiteralTypeAnnotation" === nodeType || "NullLiteralTypeAnnotation" === nodeType || "ExistsTypeAnnotation" === nodeType || "FunctionTypeAnnotation" === nodeType || "GenericTypeAnnotation" === nodeType || "InterfaceTypeAnnotation" === nodeType || "IntersectionTypeAnnotation" === nodeType || "MixedTypeAnnotation" === nodeType || "EmptyTypeAnnotation" === nodeType || "NullableTypeAnnotation" === nodeType || "NumberLiteralTypeAnnotation" === nodeType || "NumberTypeAnnotation" === nodeType || "ObjectTypeAnnotation" === nodeType || "StringLiteralTypeAnnotation" === nodeType || "StringTypeAnnotation" === nodeType || "SymbolTypeAnnotation" === nodeType || "ThisTypeAnnotation" === nodeType || "TupleTypeAnnotation" === nodeType || "TypeofTypeAnnotation" === nodeType || "UnionTypeAnnotation" === nodeType || "VoidTypeAnnotation" === nodeType || "IndexedAccessType" === nodeType || "OptionalIndexedAccessType" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFlowBaseAnnotation(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("AnyTypeAnnotation" === nodeType || "BooleanTypeAnnotation" === nodeType || "NullLiteralTypeAnnotation" === nodeType || "MixedTypeAnnotation" === nodeType || "EmptyTypeAnnotation" === nodeType || "NumberTypeAnnotation" === nodeType || "StringTypeAnnotation" === nodeType || "SymbolTypeAnnotation" === nodeType || "ThisTypeAnnotation" === nodeType || "VoidTypeAnnotation" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFlowDeclaration(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("DeclareClass" === nodeType || "DeclareFunction" === nodeType || "DeclareInterface" === nodeType || "DeclareModule" === nodeType || "DeclareModuleExports" === nodeType || "DeclareTypeAlias" === nodeType || "DeclareOpaqueType" === nodeType || "DeclareVariable" === nodeType || "DeclareExportDeclaration" === nodeType || "DeclareExportAllDeclaration" === nodeType || "InterfaceDeclaration" === nodeType || "OpaqueType" === nodeType || "TypeAlias" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isFlowPredicate(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("DeclaredPredicate" === nodeType || "InferredPredicate" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumBody(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("EnumBooleanBody" === nodeType || "EnumNumberBody" === nodeType || "EnumStringBody" === nodeType || "EnumSymbolBody" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isEnumMember(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("EnumBooleanMember" === nodeType || "EnumNumberMember" === nodeType || "EnumStringMember" === nodeType || "EnumDefaultedMember" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isJSX(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("JSXAttribute" === nodeType || "JSXClosingElement" === nodeType || "JSXElement" === nodeType || "JSXEmptyExpression" === nodeType || "JSXExpressionContainer" === nodeType || "JSXSpreadChild" === nodeType || "JSXIdentifier" === nodeType || "JSXMemberExpression" === nodeType || "JSXNamespacedName" === nodeType || "JSXOpeningElement" === nodeType || "JSXSpreadAttribute" === nodeType || "JSXText" === nodeType || "JSXFragment" === nodeType || "JSXOpeningFragment" === nodeType || "JSXClosingFragment" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSTypeElement(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("TSCallSignatureDeclaration" === nodeType || "TSConstructSignatureDeclaration" === nodeType || "TSPropertySignature" === nodeType || "TSMethodSignature" === nodeType || "TSIndexSignature" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("TSAnyKeyword" === nodeType || "TSBooleanKeyword" === nodeType || "TSBigIntKeyword" === nodeType || "TSIntrinsicKeyword" === nodeType || "TSNeverKeyword" === nodeType || "TSNullKeyword" === nodeType || "TSNumberKeyword" === nodeType || "TSObjectKeyword" === nodeType || "TSStringKeyword" === nodeType || "TSSymbolKeyword" === nodeType || "TSUndefinedKeyword" === nodeType || "TSUnknownKeyword" === nodeType || "TSVoidKeyword" === nodeType || "TSThisType" === nodeType || "TSFunctionType" === nodeType || "TSConstructorType" === nodeType || "TSTypeReference" === nodeType || "TSTypePredicate" === nodeType || "TSTypeQuery" === nodeType || "TSTypeLiteral" === nodeType || "TSArrayType" === nodeType || "TSTupleType" === nodeType || "TSOptionalType" === nodeType || "TSRestType" === nodeType || "TSUnionType" === nodeType || "TSIntersectionType" === nodeType || "TSConditionalType" === nodeType || "TSInferType" === nodeType || "TSParenthesizedType" === nodeType || "TSTypeOperator" === nodeType || "TSIndexedAccessType" === nodeType || "TSMappedType" === nodeType || "TSLiteralType" === nodeType || "TSExpressionWithTypeArguments" === nodeType || "TSImportType" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isTSBaseType(node, opts) {
    if (!node) return false;
    var nodeType = node.type;
    if ("TSAnyKeyword" === nodeType || "TSBooleanKeyword" === nodeType || "TSBigIntKeyword" === nodeType || "TSIntrinsicKeyword" === nodeType || "TSNeverKeyword" === nodeType || "TSNullKeyword" === nodeType || "TSNumberKeyword" === nodeType || "TSObjectKeyword" === nodeType || "TSStringKeyword" === nodeType || "TSSymbolKeyword" === nodeType || "TSUndefinedKeyword" === nodeType || "TSUnknownKeyword" === nodeType || "TSVoidKeyword" === nodeType || "TSThisType" === nodeType || "TSLiteralType" === nodeType) {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isNumberLiteral(node, opts) {
    console.trace("The node type NumberLiteral has been renamed to NumericLiteral");
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "NumberLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isRegexLiteral(node, opts) {
    console.trace("The node type RegexLiteral has been renamed to RegExpLiteral");
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "RegexLiteral") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isRestProperty(node, opts) {
    console.trace("The node type RestProperty has been renamed to RestElement");
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "RestProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}
function isSpreadProperty(node, opts) {
    console.trace("The node type SpreadProperty has been renamed to SpreadElement");
    if (!node) return false;
    var nodeType = node.type;
    if (nodeType === "SpreadProperty") {
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    }
    return false;
}

Object.defineProperty(matchesPattern$1, "__esModule", {
    value: true
});
matchesPattern$1.default = matchesPattern;
var _generated$o = generated$4;
function matchesPattern(member, match, allowPartial) {
    if (!(0, _generated$o.isMemberExpression)(member)) return false;
    var parts = Array.isArray(match) ? match : match.split(".");
    var nodes = [];
    var node;
    for(node = member; (0, _generated$o.isMemberExpression)(node); node = node.object){
        nodes.push(node.property);
    }
    nodes.push(node);
    if (nodes.length < parts.length) return false;
    if (!allowPartial && nodes.length > parts.length) return false;
    for(var i = 0, j = nodes.length - 1; i < parts.length; i++, j--){
        var node1 = nodes[j];
        var value = void 0;
        if ((0, _generated$o.isIdentifier)(node1)) {
            value = node1.name;
        } else if ((0, _generated$o.isStringLiteral)(node1)) {
            value = node1.value;
        } else if ((0, _generated$o.isThisExpression)(node1)) {
            value = "this";
        } else {
            return false;
        }
        if (parts[i] !== value) return false;
    }
    return true;
}

Object.defineProperty(buildMatchMemberExpression$1, "__esModule", {
    value: true
});
buildMatchMemberExpression$1.default = buildMatchMemberExpression;
var _matchesPattern = matchesPattern$1;
function buildMatchMemberExpression(match, allowPartial) {
    var parts = match.split(".");
    return function(member) {
        return (0, _matchesPattern.default)(member, parts, allowPartial);
    };
}

Object.defineProperty(isReactComponent$1, "__esModule", {
    value: true
});
isReactComponent$1.default = void 0;
var _buildMatchMemberExpression = buildMatchMemberExpression$1;
var isReactComponent = (0, _buildMatchMemberExpression.default)("React.Component");
var _default$4 = isReactComponent;
isReactComponent$1.default = _default$4;

var isCompatTag$1 = {};

Object.defineProperty(isCompatTag$1, "__esModule", {
    value: true
});
isCompatTag$1.default = isCompatTag;
function isCompatTag(tagName) {
    return !!tagName && /^[a-z]/.test(tagName);
}

var buildChildren$1 = {};

var cleanJSXElementLiteralChild$1 = {};

var generated$3 = {};

var builder$1 = {};

var definitions = {};

var _typeof$4 = function(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
};
var toFastProperties;
var hasRequiredToFastProperties;
function requireToFastProperties() {
    if (hasRequiredToFastProperties) return toFastProperties;
    hasRequiredToFastProperties = 1;
    var fastProto = null;
    // Creates an object with permanently fast properties in V8. See Toon Verwaest's
    // post https://medium.com/@tverwaes/setting-up-prototypes-in-v8-ec9c9491dfe2#5f62
    // for more details. Use %HasFastProperties(object) and the Node.js flag
    // --allow-natives-syntax to check whether an object has fast properties.
    function FastObject(o) {
        // A prototype object will have "fast properties" enabled once it is checked
        // against the inline property cache of a function, e.g. fastProto.property:
        // https://github.com/v8/v8/blob/6.0.122/test/mjsunit/fast-prototype.js#L48-L63
        if (fastProto !== null && _typeof$4(fastProto.property)) {
            var result = fastProto;
            fastProto = FastObject.prototype = null;
            return result;
        }
        fastProto = FastObject.prototype = o == null ? Object.create(null) : o;
        return new FastObject;
    }
    // Initialize the inline property cache of FastObject
    FastObject();
    toFastProperties = function toFastproperties(o) {
        return FastObject(o);
    };
    return toFastProperties;
}

var core = {};

var is = {};

var isType = {};

function _createForOfIteratorHelperLoose$i(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
var hasRequiredIsType;
function requireIsType() {
    var isType$1 = function isType(nodeType, targetType) {
        if (nodeType === targetType) return true;
        if (_definitions.ALIAS_KEYS[targetType]) return false;
        var aliases = _definitions.FLIPPED_ALIAS_KEYS[targetType];
        if (aliases) {
            if (aliases[0] === nodeType) return true;
            for(var _iterator = _createForOfIteratorHelperLoose$i(aliases), _step; !(_step = _iterator()).done;){
                var alias = _step.value;
                if (nodeType === alias) return true;
            }
        }
        return false;
    };
    if (hasRequiredIsType) return isType;
    hasRequiredIsType = 1;
    Object.defineProperty(isType, "__esModule", {
        value: true
    });
    isType.default = isType$1;
    var _definitions = requireDefinitions();
    return isType;
}

var isPlaceholderType = {};

function _createForOfIteratorHelperLoose$h(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
var hasRequiredIsPlaceholderType;
function requireIsPlaceholderType() {
    var isPlaceholderType$1 = function isPlaceholderType(placeholderType, targetType) {
        if (placeholderType === targetType) return true;
        var aliases = _definitions.PLACEHOLDERS_ALIAS[placeholderType];
        if (aliases) {
            for(var _iterator = _createForOfIteratorHelperLoose$h(aliases), _step; !(_step = _iterator()).done;){
                var alias = _step.value;
                if (targetType === alias) return true;
            }
        }
        return false;
    };
    if (hasRequiredIsPlaceholderType) return isPlaceholderType;
    hasRequiredIsPlaceholderType = 1;
    Object.defineProperty(isPlaceholderType, "__esModule", {
        value: true
    });
    isPlaceholderType.default = isPlaceholderType$1;
    var _definitions = requireDefinitions();
    return isPlaceholderType;
}

var hasRequiredIs;
function requireIs() {
    var is$1 = function is(type, node, opts) {
        if (!node) return false;
        var matches = (0, _isType.default)(node.type, type);
        if (!matches) {
            if (!opts && node.type === "Placeholder" && type in _definitions.FLIPPED_ALIAS_KEYS) {
                return (0, _isPlaceholderType.default)(node.expectedNode, type);
            }
            return false;
        }
        if (typeof opts === "undefined") {
            return true;
        } else {
            return (0, _shallowEqual.default)(node, opts);
        }
    };
    if (hasRequiredIs) return is;
    hasRequiredIs = 1;
    Object.defineProperty(is, "__esModule", {
        value: true
    });
    is.default = is$1;
    var _shallowEqual = shallowEqual$1;
    var _isType = requireIsType();
    var _isPlaceholderType = requireIsPlaceholderType();
    var _definitions = requireDefinitions();
    return is;
}

var isValidIdentifier$1 = {};

var lib = {};

var identifier$1 = {};

Object.defineProperty(identifier$1, "__esModule", {
    value: true
});
identifier$1.isIdentifierStart = isIdentifierStart;
identifier$1.isIdentifierChar = isIdentifierChar;
identifier$1.isIdentifierName = isIdentifierName;
var nonASCIIidentifierStartChars = "\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ";
var nonASCIIidentifierChars = "‌‍\xb7̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿";
var nonASCIIidentifierStart = new RegExp("[" + nonASCIIidentifierStartChars + "]");
var nonASCIIidentifier = new RegExp("[" + nonASCIIidentifierStartChars + nonASCIIidentifierChars + "]");
nonASCIIidentifierStartChars = nonASCIIidentifierChars = null;
var astralIdentifierStartCodes = [
    0,
    11,
    2,
    25,
    2,
    18,
    2,
    1,
    2,
    14,
    3,
    13,
    35,
    122,
    70,
    52,
    268,
    28,
    4,
    48,
    48,
    31,
    14,
    29,
    6,
    37,
    11,
    29,
    3,
    35,
    5,
    7,
    2,
    4,
    43,
    157,
    19,
    35,
    5,
    35,
    5,
    39,
    9,
    51,
    157,
    310,
    10,
    21,
    11,
    7,
    153,
    5,
    3,
    0,
    2,
    43,
    2,
    1,
    4,
    0,
    3,
    22,
    11,
    22,
    10,
    30,
    66,
    18,
    2,
    1,
    11,
    21,
    11,
    25,
    71,
    55,
    7,
    1,
    65,
    0,
    16,
    3,
    2,
    2,
    2,
    28,
    43,
    28,
    4,
    28,
    36,
    7,
    2,
    27,
    28,
    53,
    11,
    21,
    11,
    18,
    14,
    17,
    111,
    72,
    56,
    50,
    14,
    50,
    14,
    35,
    349,
    41,
    7,
    1,
    79,
    28,
    11,
    0,
    9,
    21,
    107,
    20,
    28,
    22,
    13,
    52,
    76,
    44,
    33,
    24,
    27,
    35,
    30,
    0,
    3,
    0,
    9,
    34,
    4,
    0,
    13,
    47,
    15,
    3,
    22,
    0,
    2,
    0,
    36,
    17,
    2,
    24,
    85,
    6,
    2,
    0,
    2,
    3,
    2,
    14,
    2,
    9,
    8,
    46,
    39,
    7,
    3,
    1,
    3,
    21,
    2,
    6,
    2,
    1,
    2,
    4,
    4,
    0,
    19,
    0,
    13,
    4,
    159,
    52,
    19,
    3,
    21,
    2,
    31,
    47,
    21,
    1,
    2,
    0,
    185,
    46,
    42,
    3,
    37,
    47,
    21,
    0,
    60,
    42,
    14,
    0,
    72,
    26,
    230,
    43,
    117,
    63,
    32,
    7,
    3,
    0,
    3,
    7,
    2,
    1,
    2,
    23,
    16,
    0,
    2,
    0,
    95,
    7,
    3,
    38,
    17,
    0,
    2,
    0,
    29,
    0,
    11,
    39,
    8,
    0,
    22,
    0,
    12,
    45,
    20,
    0,
    35,
    56,
    264,
    8,
    2,
    36,
    18,
    0,
    50,
    29,
    113,
    6,
    2,
    1,
    2,
    37,
    22,
    0,
    26,
    5,
    2,
    1,
    2,
    31,
    15,
    0,
    328,
    18,
    190,
    0,
    80,
    921,
    103,
    110,
    18,
    195,
    2749,
    1070,
    4050,
    582,
    8634,
    568,
    8,
    30,
    114,
    29,
    19,
    47,
    17,
    3,
    32,
    20,
    6,
    18,
    689,
    63,
    129,
    74,
    6,
    0,
    67,
    12,
    65,
    1,
    2,
    0,
    29,
    6135,
    9,
    1237,
    43,
    8,
    8952,
    286,
    50,
    2,
    18,
    3,
    9,
    395,
    2309,
    106,
    6,
    12,
    4,
    8,
    8,
    9,
    5991,
    84,
    2,
    70,
    2,
    1,
    3,
    0,
    3,
    1,
    3,
    3,
    2,
    11,
    2,
    0,
    2,
    6,
    2,
    64,
    2,
    3,
    3,
    7,
    2,
    6,
    2,
    27,
    2,
    3,
    2,
    4,
    2,
    0,
    4,
    6,
    2,
    339,
    3,
    24,
    2,
    24,
    2,
    30,
    2,
    24,
    2,
    30,
    2,
    24,
    2,
    30,
    2,
    24,
    2,
    30,
    2,
    24,
    2,
    7,
    2357,
    44,
    11,
    6,
    17,
    0,
    370,
    43,
    1301,
    196,
    60,
    67,
    8,
    0,
    1205,
    3,
    2,
    26,
    2,
    1,
    2,
    0,
    3,
    0,
    2,
    9,
    2,
    3,
    2,
    0,
    2,
    0,
    7,
    0,
    5,
    0,
    2,
    0,
    2,
    0,
    2,
    2,
    2,
    1,
    2,
    0,
    3,
    0,
    2,
    0,
    2,
    0,
    2,
    0,
    2,
    0,
    2,
    1,
    2,
    0,
    3,
    3,
    2,
    6,
    2,
    3,
    2,
    3,
    2,
    0,
    2,
    9,
    2,
    16,
    6,
    2,
    2,
    4,
    2,
    16,
    4421,
    42717,
    35,
    4148,
    12,
    221,
    3,
    5761,
    15,
    7472,
    3104,
    541,
    1507,
    4938
];
var astralIdentifierCodes = [
    509,
    0,
    227,
    0,
    150,
    4,
    294,
    9,
    1368,
    2,
    2,
    1,
    6,
    3,
    41,
    2,
    5,
    0,
    166,
    1,
    574,
    3,
    9,
    9,
    370,
    1,
    154,
    10,
    176,
    2,
    54,
    14,
    32,
    9,
    16,
    3,
    46,
    10,
    54,
    9,
    7,
    2,
    37,
    13,
    2,
    9,
    6,
    1,
    45,
    0,
    13,
    2,
    49,
    13,
    9,
    3,
    2,
    11,
    83,
    11,
    7,
    0,
    161,
    11,
    6,
    9,
    7,
    3,
    56,
    1,
    2,
    6,
    3,
    1,
    3,
    2,
    10,
    0,
    11,
    1,
    3,
    6,
    4,
    4,
    193,
    17,
    10,
    9,
    5,
    0,
    82,
    19,
    13,
    9,
    214,
    6,
    3,
    8,
    28,
    1,
    83,
    16,
    16,
    9,
    82,
    12,
    9,
    9,
    84,
    14,
    5,
    9,
    243,
    14,
    166,
    9,
    71,
    5,
    2,
    1,
    3,
    3,
    2,
    0,
    2,
    1,
    13,
    9,
    120,
    6,
    3,
    6,
    4,
    0,
    29,
    9,
    41,
    6,
    2,
    3,
    9,
    0,
    10,
    10,
    47,
    15,
    406,
    7,
    2,
    7,
    17,
    9,
    57,
    21,
    2,
    13,
    123,
    5,
    4,
    0,
    2,
    1,
    2,
    6,
    2,
    0,
    9,
    9,
    49,
    4,
    2,
    1,
    2,
    4,
    9,
    9,
    330,
    3,
    19306,
    9,
    135,
    4,
    60,
    6,
    26,
    9,
    1014,
    0,
    2,
    54,
    8,
    3,
    82,
    0,
    12,
    1,
    19628,
    1,
    5319,
    4,
    4,
    5,
    9,
    7,
    3,
    6,
    31,
    3,
    149,
    2,
    1418,
    49,
    513,
    54,
    5,
    49,
    9,
    0,
    15,
    0,
    23,
    4,
    2,
    14,
    1361,
    6,
    2,
    16,
    3,
    6,
    2,
    1,
    2,
    4,
    262,
    6,
    10,
    9,
    419,
    13,
    1495,
    6,
    110,
    6,
    6,
    9,
    4759,
    9,
    787719,
    239
];
function isInAstralSet(code, set) {
    var pos = 0x10000;
    for(var i = 0, length = set.length; i < length; i += 2){
        pos += set[i];
        if (pos > code) return false;
        pos += set[i + 1];
        if (pos >= code) return true;
    }
    return false;
}
function isIdentifierStart(code) {
    if (code < 65) return code === 36;
    if (code <= 90) return true;
    if (code < 97) return code === 95;
    if (code <= 122) return true;
    if (code <= 0xffff) {
        return code >= 0xaa && nonASCIIidentifierStart.test(String.fromCharCode(code));
    }
    return isInAstralSet(code, astralIdentifierStartCodes);
}
function isIdentifierChar(code) {
    if (code < 48) return code === 36;
    if (code < 58) return true;
    if (code < 65) return false;
    if (code <= 90) return true;
    if (code < 97) return code === 95;
    if (code <= 122) return true;
    if (code <= 0xffff) {
        return code >= 0xaa && nonASCIIidentifier.test(String.fromCharCode(code));
    }
    return isInAstralSet(code, astralIdentifierStartCodes) || isInAstralSet(code, astralIdentifierCodes);
}
function isIdentifierName(name) {
    var isFirst = true;
    for(var i = 0; i < name.length; i++){
        var cp = name.charCodeAt(i);
        if ((cp & 0xfc00) === 0xd800 && i + 1 < name.length) {
            var trail = name.charCodeAt(++i);
            if ((trail & 0xfc00) === 0xdc00) {
                cp = 0x10000 + ((cp & 0x3ff) << 10) + (trail & 0x3ff);
            }
        }
        if (isFirst) {
            isFirst = false;
            if (!isIdentifierStart(cp)) {
                return false;
            }
        } else if (!isIdentifierChar(cp)) {
            return false;
        }
    }
    return !isFirst;
}

var keyword = {};

Object.defineProperty(keyword, "__esModule", {
    value: true
});
keyword.isReservedWord = isReservedWord;
keyword.isStrictReservedWord = isStrictReservedWord;
keyword.isStrictBindOnlyReservedWord = isStrictBindOnlyReservedWord;
keyword.isStrictBindReservedWord = isStrictBindReservedWord;
keyword.isKeyword = isKeyword;
var reservedWords = {
    keyword: [
        "break",
        "case",
        "catch",
        "continue",
        "debugger",
        "default",
        "do",
        "else",
        "finally",
        "for",
        "function",
        "if",
        "return",
        "switch",
        "throw",
        "try",
        "var",
        "const",
        "while",
        "with",
        "new",
        "this",
        "super",
        "class",
        "extends",
        "export",
        "import",
        "null",
        "true",
        "false",
        "in",
        "instanceof",
        "typeof",
        "void",
        "delete"
    ],
    strict: [
        "implements",
        "interface",
        "let",
        "package",
        "private",
        "protected",
        "public",
        "static",
        "yield"
    ],
    strictBind: [
        "eval",
        "arguments"
    ]
};
var keywords = new Set(reservedWords.keyword);
var reservedWordsStrictSet = new Set(reservedWords.strict);
var reservedWordsStrictBindSet = new Set(reservedWords.strictBind);
function isReservedWord(word, inModule) {
    return inModule && word === "await" || word === "enum";
}
function isStrictReservedWord(word, inModule) {
    return isReservedWord(word, inModule) || reservedWordsStrictSet.has(word);
}
function isStrictBindOnlyReservedWord(word) {
    return reservedWordsStrictBindSet.has(word);
}
function isStrictBindReservedWord(word, inModule) {
    return isStrictReservedWord(word, inModule) || isStrictBindOnlyReservedWord(word);
}
function isKeyword(word) {
    return keywords.has(word);
}

(function(exports) {
    Object.defineProperty(exports, "__esModule", {
        value: true
    });
    Object.defineProperty(exports, "isIdentifierName", {
        enumerable: true,
        get: function get() {
            return _identifier.isIdentifierName;
        }
    });
    Object.defineProperty(exports, "isIdentifierChar", {
        enumerable: true,
        get: function get() {
            return _identifier.isIdentifierChar;
        }
    });
    Object.defineProperty(exports, "isIdentifierStart", {
        enumerable: true,
        get: function get() {
            return _identifier.isIdentifierStart;
        }
    });
    Object.defineProperty(exports, "isReservedWord", {
        enumerable: true,
        get: function get() {
            return _keyword.isReservedWord;
        }
    });
    Object.defineProperty(exports, "isStrictBindOnlyReservedWord", {
        enumerable: true,
        get: function get() {
            return _keyword.isStrictBindOnlyReservedWord;
        }
    });
    Object.defineProperty(exports, "isStrictBindReservedWord", {
        enumerable: true,
        get: function get() {
            return _keyword.isStrictBindReservedWord;
        }
    });
    Object.defineProperty(exports, "isStrictReservedWord", {
        enumerable: true,
        get: function get() {
            return _keyword.isStrictReservedWord;
        }
    });
    Object.defineProperty(exports, "isKeyword", {
        enumerable: true,
        get: function get() {
            return _keyword.isKeyword;
        }
    });
    var _identifier = identifier$1;
    var _keyword = keyword;
})(lib);

Object.defineProperty(isValidIdentifier$1, "__esModule", {
    value: true
});
isValidIdentifier$1.default = isValidIdentifier;
var _helperValidatorIdentifier$1 = lib;
function isValidIdentifier(name, reserved) {
    if (reserved === void 0) reserved = true;
    if (typeof name !== "string") return false;
    if (reserved) {
        if ((0, _helperValidatorIdentifier$1.isKeyword)(name) || (0, _helperValidatorIdentifier$1.isStrictReservedWord)(name, true)) {
            return false;
        }
    }
    return (0, _helperValidatorIdentifier$1.isIdentifierName)(name);
}

var constants = {};

function _arrayLikeToArray$3(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _arrayWithoutHoles$3(arr) {
    if (Array.isArray(arr)) return _arrayLikeToArray$3(arr);
}
function _iterableToArray$3(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _nonIterableSpread$3() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toConsumableArray$3(arr) {
    return _arrayWithoutHoles$3(arr) || _iterableToArray$3(arr) || _unsupportedIterableToArray$3(arr) || _nonIterableSpread$3();
}
function _unsupportedIterableToArray$3(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray$3(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray$3(o, minLen);
}
Object.defineProperty(constants, "__esModule", {
    value: true
});
constants.NOT_LOCAL_BINDING = constants.BLOCK_SCOPED_SYMBOL = constants.INHERIT_KEYS = constants.UNARY_OPERATORS = constants.STRING_UNARY_OPERATORS = constants.NUMBER_UNARY_OPERATORS = constants.BOOLEAN_UNARY_OPERATORS = constants.ASSIGNMENT_OPERATORS = constants.BINARY_OPERATORS = constants.NUMBER_BINARY_OPERATORS = constants.BOOLEAN_BINARY_OPERATORS = constants.COMPARISON_BINARY_OPERATORS = constants.EQUALITY_BINARY_OPERATORS = constants.BOOLEAN_NUMBER_BINARY_OPERATORS = constants.UPDATE_OPERATORS = constants.LOGICAL_OPERATORS = constants.COMMENT_KEYS = constants.FOR_INIT_KEYS = constants.FLATTENABLE_KEYS = constants.STATEMENT_OR_BLOCK_KEYS = void 0;
var STATEMENT_OR_BLOCK_KEYS = [
    "consequent",
    "body",
    "alternate"
];
constants.STATEMENT_OR_BLOCK_KEYS = STATEMENT_OR_BLOCK_KEYS;
var FLATTENABLE_KEYS = [
    "body",
    "expressions"
];
constants.FLATTENABLE_KEYS = FLATTENABLE_KEYS;
var FOR_INIT_KEYS = [
    "left",
    "init"
];
constants.FOR_INIT_KEYS = FOR_INIT_KEYS;
var COMMENT_KEYS = [
    "leadingComments",
    "trailingComments",
    "innerComments"
];
constants.COMMENT_KEYS = COMMENT_KEYS;
var LOGICAL_OPERATORS = [
    "||",
    "&&",
    "??"
];
constants.LOGICAL_OPERATORS = LOGICAL_OPERATORS;
var UPDATE_OPERATORS = [
    "++",
    "--"
];
constants.UPDATE_OPERATORS = UPDATE_OPERATORS;
var BOOLEAN_NUMBER_BINARY_OPERATORS = [
    ">",
    "<",
    ">=",
    "<="
];
constants.BOOLEAN_NUMBER_BINARY_OPERATORS = BOOLEAN_NUMBER_BINARY_OPERATORS;
var EQUALITY_BINARY_OPERATORS = [
    "==",
    "===",
    "!=",
    "!=="
];
constants.EQUALITY_BINARY_OPERATORS = EQUALITY_BINARY_OPERATORS;
var COMPARISON_BINARY_OPERATORS = _toConsumableArray$3(EQUALITY_BINARY_OPERATORS).concat([
    "in",
    "instanceof"
]);
constants.COMPARISON_BINARY_OPERATORS = COMPARISON_BINARY_OPERATORS;
var BOOLEAN_BINARY_OPERATORS = _toConsumableArray$3(COMPARISON_BINARY_OPERATORS).concat(_toConsumableArray$3(BOOLEAN_NUMBER_BINARY_OPERATORS));
constants.BOOLEAN_BINARY_OPERATORS = BOOLEAN_BINARY_OPERATORS;
var NUMBER_BINARY_OPERATORS = [
    "-",
    "/",
    "%",
    "*",
    "**",
    "&",
    "|",
    ">>",
    ">>>",
    "<<",
    "^"
];
constants.NUMBER_BINARY_OPERATORS = NUMBER_BINARY_OPERATORS;
var BINARY_OPERATORS = [
    "+"
].concat(_toConsumableArray$3(NUMBER_BINARY_OPERATORS), _toConsumableArray$3(BOOLEAN_BINARY_OPERATORS));
constants.BINARY_OPERATORS = BINARY_OPERATORS;
var ASSIGNMENT_OPERATORS = [
    "=",
    "+="
].concat(_toConsumableArray$3(NUMBER_BINARY_OPERATORS.map(function(op) {
    return op + "=";
})), _toConsumableArray$3(LOGICAL_OPERATORS.map(function(op) {
    return op + "=";
})));
constants.ASSIGNMENT_OPERATORS = ASSIGNMENT_OPERATORS;
var BOOLEAN_UNARY_OPERATORS = [
    "delete",
    "!"
];
constants.BOOLEAN_UNARY_OPERATORS = BOOLEAN_UNARY_OPERATORS;
var NUMBER_UNARY_OPERATORS = [
    "+",
    "-",
    "~"
];
constants.NUMBER_UNARY_OPERATORS = NUMBER_UNARY_OPERATORS;
var STRING_UNARY_OPERATORS = [
    "typeof"
];
constants.STRING_UNARY_OPERATORS = STRING_UNARY_OPERATORS;
var UNARY_OPERATORS = [
    "void",
    "throw"
].concat(_toConsumableArray$3(BOOLEAN_UNARY_OPERATORS), _toConsumableArray$3(NUMBER_UNARY_OPERATORS), _toConsumableArray$3(STRING_UNARY_OPERATORS));
constants.UNARY_OPERATORS = UNARY_OPERATORS;
var INHERIT_KEYS = {
    optional: [
        "typeAnnotation",
        "typeParameters",
        "returnType"
    ],
    force: [
        "start",
        "loc",
        "end"
    ]
};
constants.INHERIT_KEYS = INHERIT_KEYS;
var BLOCK_SCOPED_SYMBOL = Symbol.for("var used to be block scoped");
constants.BLOCK_SCOPED_SYMBOL = BLOCK_SCOPED_SYMBOL;
var NOT_LOCAL_BINDING = Symbol.for("should not be considered a local binding");
constants.NOT_LOCAL_BINDING = NOT_LOCAL_BINDING;

var utils = {};

var validate = {};

var hasRequiredValidate;
function requireValidate() {
    var validate$1 = function validate(node, key, val) {
        if (!node) return;
        var fields = _definitions.NODE_FIELDS[node.type];
        if (!fields) return;
        var field = fields[key];
        validateField(node, key, val, field);
        validateChild(node, key, val);
    };
    var validateField = function validateField(node, key, val, field) {
        if (!(field != null && field.validate)) return;
        if (field.optional && val == null) return;
        field.validate(node, key, val);
    };
    var validateChild = function validateChild(node, key, val) {
        if (val == null) return;
        var validate = _definitions.NODE_PARENT_VALIDATIONS[val.type];
        if (!validate) return;
        validate(node, key, val);
    };
    if (hasRequiredValidate) return validate;
    hasRequiredValidate = 1;
    Object.defineProperty(validate, "__esModule", {
        value: true
    });
    validate.default = validate$1;
    validate.validateField = validateField;
    validate.validateChild = validateChild;
    var _definitions = requireDefinitions();
    return validate;
}

function _instanceof(left, right) {
    if (right != null && typeof Symbol !== "undefined" && right[Symbol.hasInstance]) {
        return !!right[Symbol.hasInstance](left);
    } else {
        return left instanceof right;
    }
}
var _typeof$3 = function(obj) {
    "@swc/helpers - typeof";
    return obj && typeof Symbol !== "undefined" && obj.constructor === Symbol ? "symbol" : typeof obj;
};
function _createForOfIteratorHelperLoose$g(o, allowArrayLike) {
    var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"];
    if (it) return (it = it.call(o)).next.bind(it);
    if (Array.isArray(o) || (it = unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") {
        if (it) o = it;
        var i = 0;
        return function() {
            if (i >= o.length) return {
                done: true
            };
            return {
                done: false,
                value: o[i++]
            };
        };
    }
    throw new TypeError("Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
var hasRequiredUtils;
function requireUtils() {
    var getType = function getType(val) {
        if (Array.isArray(val)) {
            return "array";
        } else if (val === null) {
            return "null";
        } else {
            return typeof val === "undefined" ? "undefined" : _typeof$3(val);
        }
    };
    var validate = function validate(validate) {
        return {
            validate: validate
        };
    };
    var typeIs = function typeIs(typeName) {
        return typeof typeName === "string" ? assertNodeType(typeName) : assertNodeType.apply(void 0, typeName);
    };
    var validateType = function validateType(typeName) {
        return validate(typeIs(typeName));
    };
    var validateOptional = function validateOptional(validate) {
        return {
            validate: validate,
            optional: true
        };
    };
    var validateOptionalType = function validateOptionalType(typeName) {
        return {
            validate: typeIs(typeName),
            optional: true
        };
    };
    var arrayOf = function arrayOf(elementType) {
        return chain(assertValueType("array"), assertEach(elementType));
    };
    var arrayOfType = function arrayOfType(typeName) {
        return arrayOf(typeIs(typeName));
    };
    var validateArrayOfType = function validateArrayOfType(typeName) {
        return validate(arrayOfType(typeName));
    };
    var assertEach = function assertEach(callback) {
        function validator(node, key, val) {
            if (!Array.isArray(val)) return;
            for(var i = 0; i < val.length; i++){
                var subkey = key + "[" + i + "]";
                var v = val[i];
                callback(node, subkey, v);
                if (process.env.BABEL_TYPES_8_BREAKING) (0, _validate.validateChild)(node, subkey, v);
            }
        }
        validator.each = callback;
        return validator;
    };
    var assertOneOf = function assertOneOf() {
        for(var _len = arguments.length, values = new Array(_len), _key = 0; _key < _len; _key++){
            values[_key] = arguments[_key];
        }
        function validate(node, key, val) {
            if (values.indexOf(val) < 0) {
                throw new TypeError("Property " + key + " expected value to be one of " + JSON.stringify(values) + " but got " + JSON.stringify(val));
            }
        }
        validate.oneOf = values;
        return validate;
    };
    var assertNodeType = function assertNodeType() {
        for(var _len = arguments.length, types = new Array(_len), _key = 0; _key < _len; _key++){
            types[_key] = arguments[_key];
        }
        function validate(node, key, val) {
            for(var _iterator = _createForOfIteratorHelperLoose$g(types), _step; !(_step = _iterator()).done;){
                var type = _step.value;
                if ((0, _is.default)(type, val)) {
                    (0, _validate.validateChild)(node, key, val);
                    return;
                }
            }
            throw new TypeError("Property " + key + " of " + node.type + " expected node to be of a type " + JSON.stringify(types) + " but instead got " + JSON.stringify(val == null ? void 0 : val.type));
        }
        validate.oneOfNodeTypes = types;
        return validate;
    };
    var assertNodeOrValueType = function assertNodeOrValueType() {
        for(var _len = arguments.length, types = new Array(_len), _key = 0; _key < _len; _key++){
            types[_key] = arguments[_key];
        }
        function validate(node, key, val) {
            for(var _iterator = _createForOfIteratorHelperLoose$g(types), _step; !(_step = _iterator()).done;){
                var type = _step.value;
                if (getType(val) === type || (0, _is.default)(type, val)) {
                    (0, _validate.validateChild)(node, key, val);
                    return;
                }
            }
            throw new TypeError("Property " + key + " of " + node.type + " expected node to be of a type " + JSON.stringify(types) + " but instead got " + JSON.stringify(val == null ? void 0 : val.type));
        }
        validate.oneOfNodeOrValueTypes = types;
        return validate;
    };
    var assertValueType = function assertValueType(type) {
        function validate(node, key, val) {
            var valid = getType(val) === type;
            if (!valid) {
                throw new TypeError("Property " + key + " expected type of " + type + " but got " + getType(val));
            }
        }
        validate.type = type;
        return validate;
    };
    var assertShape = function assertShape(shape) {
        function validate(node, key, val) {
            var errors = [];
            for(var _iterator = _createForOfIteratorHelperLoose$g(Object.keys(shape)), _step; !(_step = _iterator()).done;){
                var property = _step.value;
                try {
                    (0, _validate.validateField)(node, property, val[property], shape[property]);
                } catch (error) {
                    if (_instanceof(error, TypeError)) {
                        errors.push(error.message);
                        continue;
                    }
                    throw error;
                }
            }
            if (errors.length) {
                throw new TypeError("Property " + key + " of " + node.type + " expected to have the following:\n" + errors.join("\n"));
            }
        }
        validate.shapeOf = shape;
        return validate;
    };
    var assertOptionalChainStart = function assertOptionalChainStart() {
        function validate(node) {
            var _current;
            var current = node;
            while(node){
                var type = current.type;
                if (type === "OptionalCallExpression") {
                    if (current.optional) return;
                    current = current.callee;
                    continue;
                }
                if (type === "OptionalMemberExpression") {
                    if (current.optional) return;
                    current = current.object;
                    continue;
                }
                break;
            }
            throw new TypeError("Non-optional " + node.type + " must chain from an optional OptionalMemberExpression or OptionalCallExpression. Found chain from " + ((_current = current) == null ? void 0 : _current.type));
        }
        return validate;
    };
    var chain = function chain() {
        for(var _len = arguments.length, fns = new Array(_len), _key = 0; _key < _len; _key++){
            fns[_key] = arguments[_key];
        }
        function validate() {
            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                args[_key] = arguments[_key];
            }
            for(var _iterator = _createForOfIteratorHelperLoose$g(fns), _step; !(_step = _iterator()).done;){
                var fn = _step.value;
                fn.apply(void 0, args);
            }
        }
        validate.chainOf = fns;
        if (fns.length >= 2 && "type" in fns[0] && fns[0].type === "array" && !("each" in fns[1])) {
            throw new Error('An assertValueType("array") validator can only be followed by an assertEach(...) validator.');
        }
        return validate;
    };
    var defineType = function defineType(type, opts) {
        if (opts === void 0) opts = {};
        var inherits = opts.inherits && store[opts.inherits] || {};
        var fields = opts.fields;
        if (!fields) {
            fields = {};
            if (inherits.fields) {
                var keys = Object.getOwnPropertyNames(inherits.fields);
                for(var _iterator = _createForOfIteratorHelperLoose$g(keys), _step; !(_step = _iterator()).done;){
                    var key = _step.value;
                    var field = inherits.fields[key];
                    var def = field.default;
                    if (Array.isArray(def) ? def.length > 0 : def && typeof def === "object") {
                        throw new Error("field defaults can only be primitives or empty arrays currently");
                    }
                    fields[key] = {
                        default: Array.isArray(def) ? [] : def,
                        optional: field.optional,
                        validate: field.validate
                    };
                }
            }
        }
        var visitor = opts.visitor || inherits.visitor || [];
        var aliases = opts.aliases || inherits.aliases || [];
        var builder = opts.builder || inherits.builder || opts.visitor || [];
        for(var _iterator1 = _createForOfIteratorHelperLoose$g(Object.keys(opts)), _step1; !(_step1 = _iterator1()).done;){
            var k = _step1.value;
            if (validTypeOpts.indexOf(k) === -1) {
                throw new Error('Unknown type option "' + k + '" on ' + type);
            }
        }
        if (opts.deprecatedAlias) {
            DEPRECATED_KEYS[opts.deprecatedAlias] = type;
        }
        for(var _iterator2 = _createForOfIteratorHelperLoose$g(visitor.concat(builder)), _step2; !(_step2 = _iterator2()).done;){
            var key1 = _step2.value;
            fields[key1] = fields[key1] || {};
        }
        for(var _iterator3 = _createForOfIteratorHelperLoose$g(Object.keys(fields)), _step3; !(_step3 = _iterator3()).done;){
            var key2 = _step3.value;
            var field1 = fields[key2];
            if (field1.default !== undefined && builder.indexOf(key2) === -1) {
                field1.optional = true;
            }
            if (field1.default === undefined) {
                field1.default = null;
            } else if (!field1.validate && field1.default != null) {
                field1.validate = assertValueType(getType(field1.default));
            }
            for(var _iterator4 = _createForOfIteratorHelperLoose$g(Object.keys(field1)), _step4; !(_step4 = _iterator4()).done;){
                var k1 = _step4.value;
                if (validFieldKeys.indexOf(k1) === -1) {
                    throw new Error('Unknown field key "' + k1 + '" on ' + type + "." + key2);
                }
            }
        }
        VISITOR_KEYS[type] = opts.visitor = visitor;
        BUILDER_KEYS[type] = opts.builder = builder;
        NODE_FIELDS[type] = opts.fields = fields;
        ALIAS_KEYS[type] = opts.aliases = aliases;
        aliases.forEach(function(alias) {
            FLIPPED_ALIAS_KEYS[alias] = FLIPPED_ALIAS_KEYS[alias] || [];
            FLIPPED_ALIAS_KEYS[alias].push(type);
        });
        if (opts.validate) {
            NODE_PARENT_VALIDATIONS[type] = opts.validate;
        }
        store[type] = opts;
    };
    if (hasRequiredUtils) return utils;
    hasRequiredUtils = 1;
    Object.defineProperty(utils, "__esModule", {
        value: true
    });
    utils.validate = validate;
    utils.typeIs = typeIs;
    utils.validateType = validateType;
    utils.validateOptional = validateOptional;
    utils.validateOptionalType = validateOptionalType;
    utils.arrayOf = arrayOf;
    utils.arrayOfType = arrayOfType;
    utils.validateArrayOfType = validateArrayOfType;
    utils.assertEach = assertEach;
    utils.assertOneOf = assertOneOf;
    utils.assertNodeType = assertNodeType;
    utils.assertNodeOrValueType = assertNodeOrValueType;
    utils.assertValueType = assertValueType;
    utils.assertShape = assertShape;
    utils.assertOptionalChainStart = assertOptionalChainStart;
    utils.chain = chain;
    utils.default = defineType;
    utils.NODE_PARENT_VALIDATIONS = utils.DEPRECATED_KEYS = utils.BUILDER_KEYS = utils.NODE_FIELDS = utils.FLIPPED_ALIAS_KEYS = utils.ALIAS_KEYS = utils.VISITOR_KEYS = void 0;
    var _is = requireIs();
    var _validate = requireValidate();
    var VISITOR_KEYS = {};
    utils.VISITOR_KEYS = VISITOR_KEYS;
    var ALIAS_KEYS = {};
    utils.ALIAS_KEYS = ALIAS_KEYS;
    var FLIPPED_ALIAS_KEYS = {};
    utils.FLIPPED_ALIAS_KEYS = FLIPPED_ALIAS_KEYS;
    var NODE_FIELDS = {};
    utils.NODE_FIELDS = NODE_FIELDS;
    var BUILDER_KEYS = {};
    utils.BUILDER_KEYS = BUILDER_KEYS;
    var DEPRECATED_KEYS = {};
    utils.DEPRECATED_KEYS = DEPRECATED_KEYS;
    var NODE_PARENT_VALIDATIONS = {};
    utils.NODE_PARENT_VALIDATIONS = NODE_PARENT_VALIDATIONS;
    var validTypeOpts = [
        "aliases",
        "builder",
        "deprecatedAlias",
        "fields",
        "inherits",
        "visitor",
        "validate"
    ];
    var validFieldKeys = [
        "default",
        "optional",
        "validate"
    ];
    var store = {};
    return utils;
}

function _arrayLikeToArray$2(arr, len) {
    if (len == null || len > arr.length) len = arr.length;
    for(var i = 0, arr2 = new Array(len); i < len; i++)arr2[i] = arr[i];
    return arr2;
}
function _arrayWithoutHoles$2(arr) {
    if (Array.isArray(arr)) return _arrayLikeToArray$2(arr);
}
function _iterableToArray$2(iter) {
    if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter);
}
function _nonIterableSpread$2() {
    throw new TypeError("Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function _toConsumableArray$2(arr) {
    return _arrayWithoutHoles$2(arr) || _iterableToArray$2(arr) || _unsupportedIterableToArray$2(arr) || _nonIterableSpread$2();
}
function _unsupportedIterableToArray$2(o, minLen) {
    if (!o) return;
    if (typeof o === "string") return _arrayLikeToArray$2(o, minLen);
    var n = Object.prototype.toString.call(o).slice(8, -1);
    if (n === "Object" && o.constructor) n = o.constructor.name;
    if (n === "Map" || n === "Set") return Array.from(n);
    if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray$2(o, minLen);
}
var hasRequiredCore;
function requireCore() {
    if (hasRequiredCore) return core;
    hasRequiredCore = 1;
    Object.defineProperty(core, "__esModule", {
        value: true
    });
    core.classMethodOrDeclareMethodCommon = core.classMethodOrPropertyCommon = core.patternLikeCommon = core.functionDeclarationCommon = core.functionTypeAnnotationCommon = core.functionCommon = void 0;
    var _is = requireIs();
    var _isValidIdentifier = isValidIdentifier$1;
    var _helperValidatorIdentifier = lib;
    var _constants = constants;
    var _utils = requireUtils();
    (0, _utils.default)("ArrayExpression", {
        fields: {
            elements: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeOrValueType)("null", "Expression", "SpreadElement"))),
                default: !process.env.BABEL_TYPES_8_BREAKING ? [] : undefined
            }
        },
        visitor: [
            "elements"
        ],
        aliases: [
            "Expression"
        ]
    });
    (0, _utils.default)("AssignmentExpression", {
        fields: {
            operator: {
                validate: function() {
                    if (!process.env.BABEL_TYPES_8_BREAKING) {
                        return (0, _utils.assertValueType)("string");
                    }
                    var identifier = (_utils.assertOneOf).apply(this, _constants.ASSIGNMENT_OPERATORS);
                    var pattern = (0, _utils.assertOneOf)("=");
                    return function(node, key, val) {
                        var validator = (0, _is.default)("Pattern", node.left) ? pattern : identifier;
                        validator(node, key, val);
                    };
                }()
            },
            left: {
                validate: !process.env.BABEL_TYPES_8_BREAKING ? (0, _utils.assertNodeType)("LVal") : (0, _utils.assertNodeType)("Identifier", "MemberExpression", "ArrayPattern", "ObjectPattern")
            },
            right: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        },
        builder: [
            "operator",
            "left",
            "right"
        ],
        visitor: [
            "left",
            "right"
        ],
        aliases: [
            "Expression"
        ]
    });
    (0, _utils.default)("BinaryExpression", {
        builder: [
            "operator",
            "left",
            "right"
        ],
        fields: {
            operator: {
                validate: (_utils.assertOneOf).apply(this, _constants.BINARY_OPERATORS)
            },
            left: {
                validate: function() {
                    var expression = (0, _utils.assertNodeType)("Expression");
                    var inOp = (0, _utils.assertNodeType)("Expression", "PrivateName");
                    var validator = function validator(node, key, val) {
                        var validator = node.operator === "in" ? inOp : expression;
                        validator(node, key, val);
                    };
                    validator.oneOfNodeTypes = [
                        "Expression",
                        "PrivateName"
                    ];
                    return validator;
                }()
            },
            right: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        },
        visitor: [
            "left",
            "right"
        ],
        aliases: [
            "Binary",
            "Expression"
        ]
    });
    (0, _utils.default)("InterpreterDirective", {
        builder: [
            "value"
        ],
        fields: {
            value: {
                validate: (0, _utils.assertValueType)("string")
            }
        }
    });
    (0, _utils.default)("Directive", {
        visitor: [
            "value"
        ],
        fields: {
            value: {
                validate: (0, _utils.assertNodeType)("DirectiveLiteral")
            }
        }
    });
    (0, _utils.default)("DirectiveLiteral", {
        builder: [
            "value"
        ],
        fields: {
            value: {
                validate: (0, _utils.assertValueType)("string")
            }
        }
    });
    (0, _utils.default)("BlockStatement", {
        builder: [
            "body",
            "directives"
        ],
        visitor: [
            "directives",
            "body"
        ],
        fields: {
            directives: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Directive"))),
                default: []
            },
            body: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Statement")))
            }
        },
        aliases: [
            "Scopable",
            "BlockParent",
            "Block",
            "Statement"
        ]
    });
    (0, _utils.default)("BreakStatement", {
        visitor: [
            "label"
        ],
        fields: {
            label: {
                validate: (0, _utils.assertNodeType)("Identifier"),
                optional: true
            }
        },
        aliases: [
            "Statement",
            "Terminatorless",
            "CompletionStatement"
        ]
    });
    (0, _utils.default)("CallExpression", {
        visitor: [
            "callee",
            "arguments",
            "typeParameters",
            "typeArguments"
        ],
        builder: [
            "callee",
            "arguments"
        ],
        aliases: [
            "Expression"
        ],
        fields: Object.assign({
            callee: {
                validate: (0, _utils.assertNodeType)("Expression", "V8IntrinsicIdentifier")
            },
            arguments: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Expression", "SpreadElement", "JSXNamespacedName", "ArgumentPlaceholder")))
            }
        }, !process.env.BABEL_TYPES_8_BREAKING ? {
            optional: {
                validate: (0, _utils.assertOneOf)(true, false),
                optional: true
            }
        } : {}, {
            typeArguments: {
                validate: (0, _utils.assertNodeType)("TypeParameterInstantiation"),
                optional: true
            },
            typeParameters: {
                validate: (0, _utils.assertNodeType)("TSTypeParameterInstantiation"),
                optional: true
            }
        })
    });
    (0, _utils.default)("CatchClause", {
        visitor: [
            "param",
            "body"
        ],
        fields: {
            param: {
                validate: (0, _utils.assertNodeType)("Identifier", "ArrayPattern", "ObjectPattern"),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("BlockStatement")
            }
        },
        aliases: [
            "Scopable",
            "BlockParent"
        ]
    });
    (0, _utils.default)("ConditionalExpression", {
        visitor: [
            "test",
            "consequent",
            "alternate"
        ],
        fields: {
            test: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            consequent: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            alternate: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        },
        aliases: [
            "Expression",
            "Conditional"
        ]
    });
    (0, _utils.default)("ContinueStatement", {
        visitor: [
            "label"
        ],
        fields: {
            label: {
                validate: (0, _utils.assertNodeType)("Identifier"),
                optional: true
            }
        },
        aliases: [
            "Statement",
            "Terminatorless",
            "CompletionStatement"
        ]
    });
    (0, _utils.default)("DebuggerStatement", {
        aliases: [
            "Statement"
        ]
    });
    (0, _utils.default)("DoWhileStatement", {
        visitor: [
            "test",
            "body"
        ],
        fields: {
            test: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        },
        aliases: [
            "Statement",
            "BlockParent",
            "Loop",
            "While",
            "Scopable"
        ]
    });
    (0, _utils.default)("EmptyStatement", {
        aliases: [
            "Statement"
        ]
    });
    (0, _utils.default)("ExpressionStatement", {
        visitor: [
            "expression"
        ],
        fields: {
            expression: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        },
        aliases: [
            "Statement",
            "ExpressionWrapper"
        ]
    });
    (0, _utils.default)("File", {
        builder: [
            "program",
            "comments",
            "tokens"
        ],
        visitor: [
            "program"
        ],
        fields: {
            program: {
                validate: (0, _utils.assertNodeType)("Program")
            },
            comments: {
                validate: !process.env.BABEL_TYPES_8_BREAKING ? Object.assign(function() {}, {
                    each: {
                        oneOfNodeTypes: [
                            "CommentBlock",
                            "CommentLine"
                        ]
                    }
                }) : (0, _utils.assertEach)((0, _utils.assertNodeType)("CommentBlock", "CommentLine")),
                optional: true
            },
            tokens: {
                validate: (0, _utils.assertEach)(Object.assign(function() {}, {
                    type: "any"
                })),
                optional: true
            }
        }
    });
    (0, _utils.default)("ForInStatement", {
        visitor: [
            "left",
            "right",
            "body"
        ],
        aliases: [
            "Scopable",
            "Statement",
            "For",
            "BlockParent",
            "Loop",
            "ForXStatement"
        ],
        fields: {
            left: {
                validate: !process.env.BABEL_TYPES_8_BREAKING ? (0, _utils.assertNodeType)("VariableDeclaration", "LVal") : (0, _utils.assertNodeType)("VariableDeclaration", "Identifier", "MemberExpression", "ArrayPattern", "ObjectPattern")
            },
            right: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    (0, _utils.default)("ForStatement", {
        visitor: [
            "init",
            "test",
            "update",
            "body"
        ],
        aliases: [
            "Scopable",
            "Statement",
            "For",
            "BlockParent",
            "Loop"
        ],
        fields: {
            init: {
                validate: (0, _utils.assertNodeType)("VariableDeclaration", "Expression"),
                optional: true
            },
            test: {
                validate: (0, _utils.assertNodeType)("Expression"),
                optional: true
            },
            update: {
                validate: (0, _utils.assertNodeType)("Expression"),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    var functionCommon = {
        params: {
            validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Identifier", "Pattern", "RestElement")))
        },
        generator: {
            default: false
        },
        async: {
            default: false
        }
    };
    core.functionCommon = functionCommon;
    var functionTypeAnnotationCommon = {
        returnType: {
            validate: (0, _utils.assertNodeType)("TypeAnnotation", "TSTypeAnnotation", "Noop"),
            optional: true
        },
        typeParameters: {
            validate: (0, _utils.assertNodeType)("TypeParameterDeclaration", "TSTypeParameterDeclaration", "Noop"),
            optional: true
        }
    };
    core.functionTypeAnnotationCommon = functionTypeAnnotationCommon;
    var functionDeclarationCommon = Object.assign({}, functionCommon, {
        declare: {
            validate: (0, _utils.assertValueType)("boolean"),
            optional: true
        },
        id: {
            validate: (0, _utils.assertNodeType)("Identifier"),
            optional: true
        }
    });
    core.functionDeclarationCommon = functionDeclarationCommon;
    (0, _utils.default)("FunctionDeclaration", {
        builder: [
            "id",
            "params",
            "body",
            "generator",
            "async"
        ],
        visitor: [
            "id",
            "params",
            "body",
            "returnType",
            "typeParameters"
        ],
        fields: Object.assign({}, functionDeclarationCommon, functionTypeAnnotationCommon, {
            body: {
                validate: (0, _utils.assertNodeType)("BlockStatement")
            }
        }),
        aliases: [
            "Scopable",
            "Function",
            "BlockParent",
            "FunctionParent",
            "Statement",
            "Pureish",
            "Declaration"
        ],
        validate: function() {
            if (!process.env.BABEL_TYPES_8_BREAKING) return function() {};
            var identifier = (0, _utils.assertNodeType)("Identifier");
            return function(parent, key, node) {
                if (!(0, _is.default)("ExportDefaultDeclaration", parent)) {
                    identifier(node, "id", node.id);
                }
            };
        }()
    });
    (0, _utils.default)("FunctionExpression", {
        inherits: "FunctionDeclaration",
        aliases: [
            "Scopable",
            "Function",
            "BlockParent",
            "FunctionParent",
            "Expression",
            "Pureish"
        ],
        fields: Object.assign({}, functionCommon, functionTypeAnnotationCommon, {
            id: {
                validate: (0, _utils.assertNodeType)("Identifier"),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("BlockStatement")
            }
        })
    });
    var patternLikeCommon = {
        typeAnnotation: {
            validate: (0, _utils.assertNodeType)("TypeAnnotation", "TSTypeAnnotation", "Noop"),
            optional: true
        },
        decorators: {
            validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator")))
        }
    };
    core.patternLikeCommon = patternLikeCommon;
    (0, _utils.default)("Identifier", {
        builder: [
            "name"
        ],
        visitor: [
            "typeAnnotation",
            "decorators"
        ],
        aliases: [
            "Expression",
            "PatternLike",
            "LVal",
            "TSEntityName"
        ],
        fields: Object.assign({}, patternLikeCommon, {
            name: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("string"), Object.assign(function(node, key, val) {
                    if (!process.env.BABEL_TYPES_8_BREAKING) return;
                    if (!(0, _isValidIdentifier.default)(val, false)) {
                        throw new TypeError('"' + val + '" is not a valid identifier name');
                    }
                }, {
                    type: "string"
                }))
            },
            optional: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            }
        }),
        validate: function validate(parent, key, node) {
            if (!process.env.BABEL_TYPES_8_BREAKING) return;
            var match = /\.(\w+)$/.exec(key);
            if (!match) return;
            var parentKey = match[1];
            var nonComp = {
                computed: false
            };
            if (parentKey === "property") {
                if ((0, _is.default)("MemberExpression", parent, nonComp)) return;
                if ((0, _is.default)("OptionalMemberExpression", parent, nonComp)) return;
            } else if (parentKey === "key") {
                if ((0, _is.default)("Property", parent, nonComp)) return;
                if ((0, _is.default)("Method", parent, nonComp)) return;
            } else if (parentKey === "exported") {
                if ((0, _is.default)("ExportSpecifier", parent)) return;
            } else if (parentKey === "imported") {
                if ((0, _is.default)("ImportSpecifier", parent, {
                    imported: node
                })) return;
            } else if (parentKey === "meta") {
                if ((0, _is.default)("MetaProperty", parent, {
                    meta: node
                })) return;
            }
            if (((0, _helperValidatorIdentifier.isKeyword)(node.name) || (0, _helperValidatorIdentifier.isReservedWord)(node.name, false)) && node.name !== "this") {
                throw new TypeError('"' + node.name + '" is not a valid identifier');
            }
        }
    });
    (0, _utils.default)("IfStatement", {
        visitor: [
            "test",
            "consequent",
            "alternate"
        ],
        aliases: [
            "Statement",
            "Conditional"
        ],
        fields: {
            test: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            consequent: {
                validate: (0, _utils.assertNodeType)("Statement")
            },
            alternate: {
                optional: true,
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    (0, _utils.default)("LabeledStatement", {
        visitor: [
            "label",
            "body"
        ],
        aliases: [
            "Statement"
        ],
        fields: {
            label: {
                validate: (0, _utils.assertNodeType)("Identifier")
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    (0, _utils.default)("StringLiteral", {
        builder: [
            "value"
        ],
        fields: {
            value: {
                validate: (0, _utils.assertValueType)("string")
            }
        },
        aliases: [
            "Expression",
            "Pureish",
            "Literal",
            "Immutable"
        ]
    });
    (0, _utils.default)("NumericLiteral", {
        builder: [
            "value"
        ],
        deprecatedAlias: "NumberLiteral",
        fields: {
            value: {
                validate: (0, _utils.assertValueType)("number")
            }
        },
        aliases: [
            "Expression",
            "Pureish",
            "Literal",
            "Immutable"
        ]
    });
    (0, _utils.default)("NullLiteral", {
        aliases: [
            "Expression",
            "Pureish",
            "Literal",
            "Immutable"
        ]
    });
    (0, _utils.default)("BooleanLiteral", {
        builder: [
            "value"
        ],
        fields: {
            value: {
                validate: (0, _utils.assertValueType)("boolean")
            }
        },
        aliases: [
            "Expression",
            "Pureish",
            "Literal",
            "Immutable"
        ]
    });
    (0, _utils.default)("RegExpLiteral", {
        builder: [
            "pattern",
            "flags"
        ],
        deprecatedAlias: "RegexLiteral",
        aliases: [
            "Expression",
            "Pureish",
            "Literal"
        ],
        fields: {
            pattern: {
                validate: (0, _utils.assertValueType)("string")
            },
            flags: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("string"), Object.assign(function(node, key, val) {
                    if (!process.env.BABEL_TYPES_8_BREAKING) return;
                    var invalid = /[^gimsuy]/.exec(val);
                    if (invalid) {
                        throw new TypeError('"' + invalid[0] + '" is not a valid RegExp flag');
                    }
                }, {
                    type: "string"
                })),
                default: ""
            }
        }
    });
    (0, _utils.default)("LogicalExpression", {
        builder: [
            "operator",
            "left",
            "right"
        ],
        visitor: [
            "left",
            "right"
        ],
        aliases: [
            "Binary",
            "Expression"
        ],
        fields: {
            operator: {
                validate: (_utils.assertOneOf).apply(this, _constants.LOGICAL_OPERATORS)
            },
            left: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            right: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        }
    });
    (0, _utils.default)("MemberExpression", {
        builder: [
            "object",
            "property",
            "computed"
        ].concat(_toConsumableArray$2(!process.env.BABEL_TYPES_8_BREAKING ? [
            "optional"
        ] : [])),
        visitor: [
            "object",
            "property"
        ],
        aliases: [
            "Expression",
            "LVal"
        ],
        fields: Object.assign({
            object: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            property: {
                validate: function() {
                    var normal = (0, _utils.assertNodeType)("Identifier", "PrivateName");
                    var computed = (0, _utils.assertNodeType)("Expression");
                    var validator = function validator(node, key, val) {
                        var validator = node.computed ? computed : normal;
                        validator(node, key, val);
                    };
                    validator.oneOfNodeTypes = [
                        "Expression",
                        "Identifier",
                        "PrivateName"
                    ];
                    return validator;
                }()
            },
            computed: {
                default: false
            }
        }, !process.env.BABEL_TYPES_8_BREAKING ? {
            optional: {
                validate: (0, _utils.assertOneOf)(true, false),
                optional: true
            }
        } : {})
    });
    (0, _utils.default)("NewExpression", {
        inherits: "CallExpression"
    });
    (0, _utils.default)("Program", {
        visitor: [
            "directives",
            "body"
        ],
        builder: [
            "body",
            "directives",
            "sourceType",
            "interpreter"
        ],
        fields: {
            sourceFile: {
                validate: (0, _utils.assertValueType)("string")
            },
            sourceType: {
                validate: (0, _utils.assertOneOf)("script", "module"),
                default: "script"
            },
            interpreter: {
                validate: (0, _utils.assertNodeType)("InterpreterDirective"),
                default: null,
                optional: true
            },
            directives: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Directive"))),
                default: []
            },
            body: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Statement")))
            }
        },
        aliases: [
            "Scopable",
            "BlockParent",
            "Block"
        ]
    });
    (0, _utils.default)("ObjectExpression", {
        visitor: [
            "properties"
        ],
        aliases: [
            "Expression"
        ],
        fields: {
            properties: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("ObjectMethod", "ObjectProperty", "SpreadElement")))
            }
        }
    });
    (0, _utils.default)("ObjectMethod", {
        builder: [
            "kind",
            "key",
            "params",
            "body",
            "computed",
            "generator",
            "async"
        ],
        fields: Object.assign({}, functionCommon, functionTypeAnnotationCommon, {
            kind: Object.assign({
                validate: (0, _utils.assertOneOf)("method", "get", "set")
            }, !process.env.BABEL_TYPES_8_BREAKING ? {
                default: "method"
            } : {}),
            computed: {
                default: false
            },
            key: {
                validate: function() {
                    var normal = (0, _utils.assertNodeType)("Identifier", "StringLiteral", "NumericLiteral");
                    var computed = (0, _utils.assertNodeType)("Expression");
                    var validator = function validator(node, key, val) {
                        var validator = node.computed ? computed : normal;
                        validator(node, key, val);
                    };
                    validator.oneOfNodeTypes = [
                        "Expression",
                        "Identifier",
                        "StringLiteral",
                        "NumericLiteral"
                    ];
                    return validator;
                }()
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("BlockStatement")
            }
        }),
        visitor: [
            "key",
            "params",
            "body",
            "decorators",
            "returnType",
            "typeParameters"
        ],
        aliases: [
            "UserWhitespacable",
            "Function",
            "Scopable",
            "BlockParent",
            "FunctionParent",
            "Method",
            "ObjectMember"
        ]
    });
    (0, _utils.default)("ObjectProperty", {
        builder: [
            "key",
            "value",
            "computed",
            "shorthand"
        ].concat(_toConsumableArray$2(!process.env.BABEL_TYPES_8_BREAKING ? [
            "decorators"
        ] : [])),
        fields: {
            computed: {
                default: false
            },
            key: {
                validate: function() {
                    var normal = (0, _utils.assertNodeType)("Identifier", "StringLiteral", "NumericLiteral");
                    var computed = (0, _utils.assertNodeType)("Expression");
                    var validator = function validator(node, key, val) {
                        var validator = node.computed ? computed : normal;
                        validator(node, key, val);
                    };
                    validator.oneOfNodeTypes = [
                        "Expression",
                        "Identifier",
                        "StringLiteral",
                        "NumericLiteral"
                    ];
                    return validator;
                }()
            },
            value: {
                validate: (0, _utils.assertNodeType)("Expression", "PatternLike")
            },
            shorthand: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("boolean"), Object.assign(function(node, key, val) {
                    if (!process.env.BABEL_TYPES_8_BREAKING) return;
                    if (val && node.computed) {
                        throw new TypeError("Property shorthand of ObjectProperty cannot be true if computed is true");
                    }
                }, {
                    type: "boolean"
                }), function(node, key, val) {
                    if (!process.env.BABEL_TYPES_8_BREAKING) return;
                    if (val && !(0, _is.default)("Identifier", node.key)) {
                        throw new TypeError("Property shorthand of ObjectProperty cannot be true if key is not an Identifier");
                    }
                }),
                default: false
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            }
        },
        visitor: [
            "key",
            "value",
            "decorators"
        ],
        aliases: [
            "UserWhitespacable",
            "Property",
            "ObjectMember"
        ],
        validate: function() {
            var pattern = (0, _utils.assertNodeType)("Identifier", "Pattern");
            var expression = (0, _utils.assertNodeType)("Expression");
            return function(parent, key, node) {
                if (!process.env.BABEL_TYPES_8_BREAKING) return;
                var validator = (0, _is.default)("ObjectPattern", parent) ? pattern : expression;
                validator(node, "value", node.value);
            };
        }()
    });
    (0, _utils.default)("RestElement", {
        visitor: [
            "argument",
            "typeAnnotation"
        ],
        builder: [
            "argument"
        ],
        aliases: [
            "LVal",
            "PatternLike"
        ],
        deprecatedAlias: "RestProperty",
        fields: Object.assign({}, patternLikeCommon, {
            argument: {
                validate: !process.env.BABEL_TYPES_8_BREAKING ? (0, _utils.assertNodeType)("LVal") : (0, _utils.assertNodeType)("Identifier", "Pattern", "MemberExpression")
            },
            optional: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            }
        }),
        validate: function validate(parent, key) {
            if (!process.env.BABEL_TYPES_8_BREAKING) return;
            var match = /(\w+)\[(\d+)\]/.exec(key);
            if (!match) throw new Error("Internal Babel error: malformed key.");
            var listKey = match[1], index = match[2];
            if (parent[listKey].length > index + 1) {
                throw new TypeError("RestElement must be last element of " + listKey);
            }
        }
    });
    (0, _utils.default)("ReturnStatement", {
        visitor: [
            "argument"
        ],
        aliases: [
            "Statement",
            "Terminatorless",
            "CompletionStatement"
        ],
        fields: {
            argument: {
                validate: (0, _utils.assertNodeType)("Expression"),
                optional: true
            }
        }
    });
    (0, _utils.default)("SequenceExpression", {
        visitor: [
            "expressions"
        ],
        fields: {
            expressions: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Expression")))
            }
        },
        aliases: [
            "Expression"
        ]
    });
    (0, _utils.default)("ParenthesizedExpression", {
        visitor: [
            "expression"
        ],
        aliases: [
            "Expression",
            "ExpressionWrapper"
        ],
        fields: {
            expression: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        }
    });
    (0, _utils.default)("SwitchCase", {
        visitor: [
            "test",
            "consequent"
        ],
        fields: {
            test: {
                validate: (0, _utils.assertNodeType)("Expression"),
                optional: true
            },
            consequent: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Statement")))
            }
        }
    });
    (0, _utils.default)("SwitchStatement", {
        visitor: [
            "discriminant",
            "cases"
        ],
        aliases: [
            "Statement",
            "BlockParent",
            "Scopable"
        ],
        fields: {
            discriminant: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            cases: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("SwitchCase")))
            }
        }
    });
    (0, _utils.default)("ThisExpression", {
        aliases: [
            "Expression"
        ]
    });
    (0, _utils.default)("ThrowStatement", {
        visitor: [
            "argument"
        ],
        aliases: [
            "Statement",
            "Terminatorless",
            "CompletionStatement"
        ],
        fields: {
            argument: {
                validate: (0, _utils.assertNodeType)("Expression")
            }
        }
    });
    (0, _utils.default)("TryStatement", {
        visitor: [
            "block",
            "handler",
            "finalizer"
        ],
        aliases: [
            "Statement"
        ],
        fields: {
            block: {
                validate: (0, _utils.chain)((0, _utils.assertNodeType)("BlockStatement"), Object.assign(function(node) {
                    if (!process.env.BABEL_TYPES_8_BREAKING) return;
                    if (!node.handler && !node.finalizer) {
                        throw new TypeError("TryStatement expects either a handler or finalizer, or both");
                    }
                }, {
                    oneOfNodeTypes: [
                        "BlockStatement"
                    ]
                }))
            },
            handler: {
                optional: true,
                validate: (0, _utils.assertNodeType)("CatchClause")
            },
            finalizer: {
                optional: true,
                validate: (0, _utils.assertNodeType)("BlockStatement")
            }
        }
    });
    (0, _utils.default)("UnaryExpression", {
        builder: [
            "operator",
            "argument",
            "prefix"
        ],
        fields: {
            prefix: {
                default: true
            },
            argument: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            operator: {
                validate: (_utils.assertOneOf).apply(this, _constants.UNARY_OPERATORS)
            }
        },
        visitor: [
            "argument"
        ],
        aliases: [
            "UnaryLike",
            "Expression"
        ]
    });
    (0, _utils.default)("UpdateExpression", {
        builder: [
            "operator",
            "argument",
            "prefix"
        ],
        fields: {
            prefix: {
                default: false
            },
            argument: {
                validate: !process.env.BABEL_TYPES_8_BREAKING ? (0, _utils.assertNodeType)("Expression") : (0, _utils.assertNodeType)("Identifier", "MemberExpression")
            },
            operator: {
                validate: (_utils.assertOneOf).apply(this, _constants.UPDATE_OPERATORS)
            }
        },
        visitor: [
            "argument"
        ],
        aliases: [
            "Expression"
        ]
    });
    (0, _utils.default)("VariableDeclaration", {
        builder: [
            "kind",
            "declarations"
        ],
        visitor: [
            "declarations"
        ],
        aliases: [
            "Statement",
            "Declaration"
        ],
        fields: {
            declare: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            },
            kind: {
                validate: (0, _utils.assertOneOf)("var", "let", "const")
            },
            declarations: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("VariableDeclarator")))
            }
        },
        validate: function validate(parent, key, node) {
            if (!process.env.BABEL_TYPES_8_BREAKING) return;
            if (!(0, _is.default)("ForXStatement", parent, {
                left: node
            })) return;
            if (node.declarations.length !== 1) {
                throw new TypeError("Exactly one VariableDeclarator is required in the VariableDeclaration of a " + parent.type);
            }
        }
    });
    (0, _utils.default)("VariableDeclarator", {
        visitor: [
            "id",
            "init"
        ],
        fields: {
            id: {
                validate: function() {
                    if (!process.env.BABEL_TYPES_8_BREAKING) {
                        return (0, _utils.assertNodeType)("LVal");
                    }
                    var normal = (0, _utils.assertNodeType)("Identifier", "ArrayPattern", "ObjectPattern");
                    var without = (0, _utils.assertNodeType)("Identifier");
                    return function(node, key, val) {
                        var validator = node.init ? normal : without;
                        validator(node, key, val);
                    };
                }()
            },
            definite: {
                optional: true,
                validate: (0, _utils.assertValueType)("boolean")
            },
            init: {
                optional: true,
                validate: (0, _utils.assertNodeType)("Expression")
            }
        }
    });
    (0, _utils.default)("WhileStatement", {
        visitor: [
            "test",
            "body"
        ],
        aliases: [
            "Statement",
            "BlockParent",
            "Loop",
            "While",
            "Scopable"
        ],
        fields: {
            test: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    (0, _utils.default)("WithStatement", {
        visitor: [
            "object",
            "body"
        ],
        aliases: [
            "Statement"
        ],
        fields: {
            object: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            body: {
                validate: (0, _utils.assertNodeType)("Statement")
            }
        }
    });
    (0, _utils.default)("AssignmentPattern", {
        visitor: [
            "left",
            "right",
            "decorators"
        ],
        builder: [
            "left",
            "right"
        ],
        aliases: [
            "Pattern",
            "PatternLike",
            "LVal"
        ],
        fields: Object.assign({}, patternLikeCommon, {
            left: {
                validate: (0, _utils.assertNodeType)("Identifier", "ObjectPattern", "ArrayPattern", "MemberExpression")
            },
            right: {
                validate: (0, _utils.assertNodeType)("Expression")
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            }
        })
    });
    (0, _utils.default)("ArrayPattern", {
        visitor: [
            "elements",
            "typeAnnotation"
        ],
        builder: [
            "elements"
        ],
        aliases: [
            "Pattern",
            "PatternLike",
            "LVal"
        ],
        fields: Object.assign({}, patternLikeCommon, {
            elements: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeOrValueType)("null", "PatternLike")))
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            },
            optional: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            }
        })
    });
    (0, _utils.default)("ArrowFunctionExpression", {
        builder: [
            "params",
            "body",
            "async"
        ],
        visitor: [
            "params",
            "body",
            "returnType",
            "typeParameters"
        ],
        aliases: [
            "Scopable",
            "Function",
            "BlockParent",
            "FunctionParent",
            "Expression",
            "Pureish"
        ],
        fields: Object.assign({}, functionCommon, functionTypeAnnotationCommon, {
            expression: {
                validate: (0, _utils.assertValueType)("boolean")
            },
            body: {
                validate: (0, _utils.assertNodeType)("BlockStatement", "Expression")
            }
        })
    });
    (0, _utils.default)("ClassBody", {
        visitor: [
            "body"
        ],
        fields: {
            body: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("ClassMethod", "ClassPrivateMethod", "ClassProperty", "ClassPrivateProperty", "TSDeclareMethod", "TSIndexSignature")))
            }
        }
    });
    (0, _utils.default)("ClassExpression", {
        builder: [
            "id",
            "superClass",
            "body",
            "decorators"
        ],
        visitor: [
            "id",
            "body",
            "superClass",
            "mixins",
            "typeParameters",
            "superTypeParameters",
            "implements",
            "decorators"
        ],
        aliases: [
            "Scopable",
            "Class",
            "Expression"
        ],
        fields: {
            id: {
                validate: (0, _utils.assertNodeType)("Identifier"),
                optional: true
            },
            typeParameters: {
                validate: (0, _utils.assertNodeType)("TypeParameterDeclaration", "TSTypeParameterDeclaration", "Noop"),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("ClassBody")
            },
            superClass: {
                optional: true,
                validate: (0, _utils.assertNodeType)("Expression")
            },
            superTypeParameters: {
                validate: (0, _utils.assertNodeType)("TypeParameterInstantiation", "TSTypeParameterInstantiation"),
                optional: true
            },
            implements: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("TSExpressionWithTypeArguments", "ClassImplements"))),
                optional: true
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            },
            mixins: {
                validate: (0, _utils.assertNodeType)("InterfaceExtends"),
                optional: true
            }
        }
    });
    (0, _utils.default)("ClassDeclaration", {
        inherits: "ClassExpression",
        aliases: [
            "Scopable",
            "Class",
            "Statement",
            "Declaration"
        ],
        fields: {
            id: {
                validate: (0, _utils.assertNodeType)("Identifier")
            },
            typeParameters: {
                validate: (0, _utils.assertNodeType)("TypeParameterDeclaration", "TSTypeParameterDeclaration", "Noop"),
                optional: true
            },
            body: {
                validate: (0, _utils.assertNodeType)("ClassBody")
            },
            superClass: {
                optional: true,
                validate: (0, _utils.assertNodeType)("Expression")
            },
            superTypeParameters: {
                validate: (0, _utils.assertNodeType)("TypeParameterInstantiation", "TSTypeParameterInstantiation"),
                optional: true
            },
            implements: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("TSExpressionWithTypeArguments", "ClassImplements"))),
                optional: true
            },
            decorators: {
                validate: (0, _utils.chain)((0, _utils.assertValueType)("array"), (0, _utils.assertEach)((0, _utils.assertNodeType)("Decorator"))),
                optional: true
            },
            mixins: {
                validate: (0, _utils.assertNodeType)("InterfaceExtends"),
                optional: true
            },
            declare: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            },
            abstract: {
                validate: (0, _utils.assertValueType)("boolean"),
                optional: true
            }
        },
        validate: function() {
            var identifier = (0, _utils.assertNodeType)("Identifier");
            return function(parent, key, node) {
                if (!process.env.BABEL_TYPES_8_BREAKING) return;
                if (!(0, _is.default)("ExportDefaultDeclaration", parent)) {
                    identifier(node, "id", node.id);
                }
            };
        }()
    });
    (0, _utils.default)("ExportAllDeclaration", {
        visitor: [
            "source"
        ],
        aliases: [
            "Statem