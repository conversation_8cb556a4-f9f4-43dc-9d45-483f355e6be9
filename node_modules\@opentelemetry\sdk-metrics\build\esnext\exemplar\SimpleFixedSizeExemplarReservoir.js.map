{"version": 3, "file": "SimpleFixedSizeExemplarReservoir.js", "sourceRoot": "", "sources": ["../../../src/exemplar/SimpleFixedSizeExemplarReservoir.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,EAAE,8BAA8B,EAAE,MAAM,qBAAqB,CAAC;AAErE;;;;GAIG;AACH,MAAM,OAAO,gCAAiC,SAAQ,8BAA8B;IAElF,YAAY,IAAY;QACtB,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IAChC,CAAC;IAEO,YAAY,CAAC,GAAW,EAAE,GAAW;QAC3C,YAAY;QACZ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;IACvD,CAAC;IAEO,gBAAgB,CACtB,MAAc,EACd,UAAkB,EAClB,WAA6B,EAC7B,IAAa;QAEb,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,KAAK;YACxC,OAAO,IAAI,CAAC,oBAAoB,EAAE,CAAC;QACrC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAChE,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC;IAED,KAAK,CACH,KAAa,EACb,SAAiB,EACjB,UAA4B,EAC5B,GAAY;QAEZ,MAAM,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;QACvE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC;SACxE;IACH,CAAC;IAEQ,KAAK;QACZ,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC;IAChC,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Context, HrTime, MetricAttributes } from '@opentelemetry/api';\nimport { FixedSizeExemplarReservoirBase } from './ExemplarReservoir';\n\n/**\n * Fixed size reservoir that uses equivalent of naive reservoir sampling\n * algorithm to accept measurements.\n *\n */\nexport class SimpleFixedSizeExemplarReservoir extends FixedSizeExemplarReservoirBase {\n  private _numMeasurementsSeen: number;\n  constructor(size: number) {\n    super(size);\n    this._numMeasurementsSeen = 0;\n  }\n\n  private getRandomInt(min: number, max: number) {\n    //[min, max)\n    return Math.floor(Math.random() * (max - min) + min);\n  }\n\n  private _findBucketIndex(\n    _value: number,\n    _timestamp: HrTime,\n    _attributes: MetricAttributes,\n    _ctx: Context\n  ) {\n    if (this._numMeasurementsSeen < this._size)\n      return this._numMeasurementsSeen++;\n    const index = this.getRandomInt(0, ++this._numMeasurementsSeen);\n    return index < this._size ? index : -1;\n  }\n\n  offer(\n    value: number,\n    timestamp: HrTime,\n    attributes: MetricAttributes,\n    ctx: Context\n  ): void {\n    const index = this._findBucketIndex(value, timestamp, attributes, ctx);\n    if (index !== -1) {\n      this._reservoirStorage[index].offer(value, timestamp, attributes, ctx);\n    }\n  }\n\n  override reset() {\n    this._numMeasurementsSeen = 0;\n  }\n}\n"]}