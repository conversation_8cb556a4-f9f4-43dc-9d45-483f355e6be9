"use client"

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { 
  MessageSquare, 
  FileText, 
  Upload, 
  Download, 
  Settings, 
  Bot, 
  ChevronDown, 
  ChevronRight,
  Feather,
  FilePlus2,
  Clock,
  HardDrive,
  Cpu,
  Activity
} from 'lucide-react';
import { format as formatDate } from 'date-fns';
import { ollamaService, type ModelUsage } from '@/ai/ollama-service';

export interface DocumentRecord {
  id: string;
  topic: string;
  content: string;
  createdAt: Date;
  settings: any;
}

export interface UploadedDocument {
  id: string;
  name: string;
  type: 'pdf' | 'word' | 'txt';
  size: number;
  uploadedAt: Date;
  content?: string;
}

interface EnhancedSidebarProps {
  documentHistory: DocumentRecord[];
  uploadedDocuments: UploadedDocument[];
  onSelectDocument: (doc: DocumentRecord) => void;
  onNewDocument: () => void;
  onNewChat: () => void;
  onUploadDocument: (file: File) => void;
  onExportDocument: (doc: DocumentRecord, format: 'pdf' | 'rtf' | 'latex' | 'txt') => void;
  currentModel: string;
  onModelChange: (model: string) => void;
}

export function EnhancedSidebar({
  documentHistory,
  uploadedDocuments,
  onSelectDocument,
  onNewDocument,
  onNewChat,
  onUploadDocument,
  onExportDocument,
  currentModel,
  onModelChange
}: EnhancedSidebarProps) {
  const [activeSection, setActiveSection] = useState<string>('chat');
  const [modelsOpen, setModelsOpen] = useState(false);
  const [documentsOpen, setDocumentsOpen] = useState(true);
  const [uploadedDocsOpen, setUploadedDocsOpen] = useState(false);
  const [savedDocsOpen, setSavedDocsOpen] = useState(true);
  const [modelUsage, setModelUsage] = useState<ModelUsage[]>([]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  useEffect(() => {
    loadModelUsage();
  }, []);

  const loadModelUsage = async () => {
    setIsLoadingModels(true);
    try {
      const usage = await ollamaService.getModelUsage();
      setModelUsage(usage);
    } catch (error) {
      console.error('Failed to load model usage:', error);
    } finally {
      setIsLoadingModels(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onUploadDocument(file);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf': return '📄';
      case 'word': return '📝';
      case 'txt': return '📃';
      default: return '📄';
    }
  };

  return (
    <aside className="bg-card text-card-foreground p-4 flex flex-col gap-4 border-r w-80 h-full">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Feather className="text-primary" size={28} />
        <h1 className="text-xl font-headline font-semibold">ASCAES</h1>
      </div>

      <ScrollArea className="flex-1">
        <div className="space-y-4">
          {/* New Chat Button */}
          <Button onClick={onNewChat} className="w-full justify-start">
            <MessageSquare className="mr-2 h-4 w-4" />
            New Chat
          </Button>

          {/* Chat Section */}
          <div className="space-y-2">
            <Button 
              variant={activeSection === 'chat' ? 'secondary' : 'ghost'} 
              className="w-full justify-start"
              onClick={() => setActiveSection('chat')}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Chat
            </Button>
          </div>

          <Separator />

          {/* Documents Section */}
          <Collapsible open={documentsOpen} onOpenChange={setDocumentsOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between">
                <div className="flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Documents
                </div>
                {documentsOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 ml-4">
              {/* Uploaded Documents */}
              <Collapsible open={uploadedDocsOpen} onOpenChange={setUploadedDocsOpen}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-full justify-between">
                    <div className="flex items-center">
                      <Upload className="mr-2 h-3 w-3" />
                      Uploaded ({uploadedDocuments.length})
                    </div>
                    {uploadedDocsOpen ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 ml-4">
                  <div className="mb-2">
                    <input
                      type="file"
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileUpload}
                      className="hidden"
                      id="file-upload"
                    />
                    <label htmlFor="file-upload">
                      <Button variant="outline" size="sm" className="w-full cursor-pointer">
                        <Upload className="mr-2 h-3 w-3" />
                        Upload File
                      </Button>
                    </label>
                  </div>
                  {uploadedDocuments.map((doc) => (
                    <div key={doc.id} className="p-2 rounded border text-xs">
                      <div className="flex items-center justify-between">
                        <span className="flex items-center">
                          <span className="mr-1">{getFileIcon(doc.type)}</span>
                          {doc.name}
                        </span>
                      </div>
                      <div className="text-muted-foreground mt-1">
                        {formatFileSize(doc.size)} • {formatDate(doc.uploadedAt, 'MMM dd, yyyy')}
                      </div>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>

              {/* Saved Documents */}
              <Collapsible open={savedDocsOpen} onOpenChange={setSavedDocsOpen}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="w-full justify-between">
                    <div className="flex items-center">
                      <FileText className="mr-2 h-3 w-3" />
                      Saved ({documentHistory.length})
                    </div>
                    {savedDocsOpen ? <ChevronDown className="h-3 w-3" /> : <ChevronRight className="h-3 w-3" />}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent className="space-y-1 ml-4">
                  <Button onClick={onNewDocument} size="sm" className="w-full mb-2">
                    <FilePlus2 className="mr-2 h-3 w-3" />
                    New Document
                  </Button>
                  {documentHistory.slice(0, 10).map((doc) => (
                    <div key={doc.id} className="group">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-left p-2 h-auto"
                        onClick={() => onSelectDocument(doc)}
                      >
                        <div className="flex-1 min-w-0">
                          <div className="text-xs font-medium truncate">
                            {doc.topic}
                          </div>
                          <div className="text-xs text-muted-foreground flex items-center mt-1">
                            <Clock className="mr-1 h-3 w-3" />
                            {formatDate(doc.createdAt, 'MMM dd, HH:mm')}
                          </div>
                        </div>
                      </Button>
                      <div className="flex gap-1 mt-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        {['pdf', 'rtf', 'latex', 'txt'].map((format) => (
                          <Button
                            key={format}
                            variant="outline"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              onExportDocument(doc, format as any);
                            }}
                          >
                            {format.toUpperCase()}
                          </Button>
                        ))}
                      </div>
                    </div>
                  ))}
                </CollapsibleContent>
              </Collapsible>
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          {/* Models Section */}
          <Collapsible open={modelsOpen} onOpenChange={setModelsOpen}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between">
                <div className="flex items-center">
                  <Bot className="mr-2 h-4 w-4" />
                  Models
                </div>
                {modelsOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-2 ml-4">
              <Button onClick={loadModelUsage} size="sm" variant="outline" className="w-full">
                <Activity className="mr-2 h-3 w-3" />
                Refresh
              </Button>
              {isLoadingModels ? (
                <div className="text-xs text-muted-foreground">Loading models...</div>
              ) : (
                modelUsage.map((model) => (
                  <div key={model.name} className="p-2 rounded border text-xs space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{model.name}</span>
                      <Badge variant={model.status === 'running' ? 'default' : 'secondary'}>
                        {model.status}
                      </Badge>
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <HardDrive className="mr-1 h-3 w-3" />
                          Size:
                        </span>
                        <span>{model.size}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="flex items-center">
                          <Cpu className="mr-1 h-3 w-3" />
                          RAM:
                        </span>
                        <span>{model.ramUsage}</span>
                      </div>
                      {model.lastUsed && (
                        <div className="flex justify-between">
                          <span className="flex items-center">
                            <Clock className="mr-1 h-3 w-3" />
                            Last used:
                          </span>
                          <span>{formatDate(model.lastUsed, 'HH:mm')}</span>
                        </div>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant={currentModel === model.name ? 'default' : 'outline'}
                      className="w-full"
                      onClick={() => onModelChange(model.name)}
                    >
                      {currentModel === model.name ? 'Current' : 'Select'}
                    </Button>
                  </div>
                ))
              )}
            </CollapsibleContent>
          </Collapsible>

          <Separator />

          {/* Settings Section */}
          <Button 
            variant={activeSection === 'settings' ? 'secondary' : 'ghost'} 
            className="w-full justify-start"
            onClick={() => setActiveSection('settings')}
          >
            <Settings className="mr-2 h-4 w-4" />
            Settings
          </Button>
        </div>
      </ScrollArea>
    </aside>
  );
}
