export interface UploadedDocument {
  id: string;
  name: string;
  type: 'pdf' | 'word' | 'txt';
  size: number;
  uploadedAt: Date;
  content?: string;
}

export class DocumentUploadService {
  private static instance: DocumentUploadService;
  private uploadedDocuments: UploadedDocument[] = [];

  static getInstance(): DocumentUploadService {
    if (!DocumentUploadService.instance) {
      DocumentUploadService.instance = new DocumentUploadService();
    }
    return DocumentUploadService.instance;
  }

  async uploadDocument(file: File): Promise<UploadedDocument> {
    const id = this.generateId();
    const type = this.getFileType(file.name);
    
    let content: string | undefined;
    
    try {
      if (type === 'txt') {
        content = await this.readTextFile(file);
      } else if (type === 'pdf') {
        // For PDF files, we'll need a PDF parser library
        // For now, we'll just store the file info without content
        content = undefined;
      } else if (type === 'word') {
        // For Word files, we'll need a Word parser library
        // For now, we'll just store the file info without content
        content = undefined;
      }
    } catch (error) {
      console.error('Error reading file content:', error);
    }

    const uploadedDoc: UploadedDocument = {
      id,
      name: file.name,
      type,
      size: file.size,
      uploadedAt: new Date(),
      content
    };

    this.uploadedDocuments.unshift(uploadedDoc); // Add to beginning for most recent first
    this.saveToLocalStorage();
    
    return uploadedDoc;
  }

  getUploadedDocuments(): UploadedDocument[] {
    return [...this.uploadedDocuments];
  }

  deleteDocument(id: string): boolean {
    const index = this.uploadedDocuments.findIndex(doc => doc.id === id);
    if (index !== -1) {
      this.uploadedDocuments.splice(index, 1);
      this.saveToLocalStorage();
      return true;
    }
    return false;
  }

  getDocument(id: string): UploadedDocument | undefined {
    return this.uploadedDocuments.find(doc => doc.id === id);
  }

  private getFileType(filename: string): 'pdf' | 'word' | 'txt' {
    const extension = filename.toLowerCase().split('.').pop();
    
    switch (extension) {
      case 'pdf':
        return 'pdf';
      case 'doc':
      case 'docx':
        return 'word';
      case 'txt':
        return 'txt';
      default:
        return 'txt'; // Default to txt for unknown types
    }
  }

  private async readTextFile(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (event) => {
        const content = event.target?.result as string;
        resolve(content);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.readAsText(file);
    });
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private saveToLocalStorage(): void {
    try {
      localStorage.setItem('uploadedDocuments', JSON.stringify(this.uploadedDocuments));
    } catch (error) {
      console.error('Failed to save uploaded documents to localStorage:', error);
    }
  }

  loadFromLocalStorage(): void {
    try {
      const stored = localStorage.getItem('uploadedDocuments');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.uploadedDocuments = parsed.map((doc: any) => ({
          ...doc,
          uploadedAt: new Date(doc.uploadedAt)
        }));
      }
    } catch (error) {
      console.error('Failed to load uploaded documents from localStorage:', error);
      this.uploadedDocuments = [];
    }
  }

  // Initialize the service
  init(): void {
    this.loadFromLocalStorage();
  }
}

// Export singleton instance
export const documentUploadService = DocumentUploadService.getInstance();
