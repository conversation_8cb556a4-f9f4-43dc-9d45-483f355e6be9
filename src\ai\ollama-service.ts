// Ollama service now uses API routes instead of direct imports to avoid browser compatibility issues

export interface OllamaModel {
  name: string;
  model: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  expires_at: string;
  size_vram: number;
}

export interface ModelUsage {
  name: string;
  size: string;
  ramUsage: string;
  status: 'running' | 'idle' | 'loading';
  lastUsed?: Date;
}

class OllamaService {
  private currentModel: string = 'llama3.2:3b'; // Default model
  private apiBase: string = '/api/ollama';

  constructor() {
    // No direct Ollama instance - using API routes
  }

  async listModels(): Promise<any[]> {
    try {
      const response = await fetch(`${this.apiBase}?action=list`);
      if (!response.ok) {
        throw new Error('Failed to fetch models');
      }
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('Error listing models:', error);
      throw new Error('Failed to connect to Ollama. Make sure <PERSON>lla<PERSON> is running.');
    }
  }

  async getModelUsage(): Promise<ModelUsage[]> {
    try {
      const [models, runningResponse] = await Promise.all([
        this.listModels(),
        fetch(`${this.apiBase}?action=ps`)
      ]);

      const runningModels = runningResponse.ok ? await runningResponse.json() : { models: [] };

      return models.map((model: any) => {
        const running = runningModels.models?.find((rm: any) => rm.name === model.name);
        return {
          name: model.name,
          size: this.formatBytes(model.size || 0),
          ramUsage: running ? this.formatBytes(running.size_vram || 0) : '0 MB',
          status: running ? 'running' as const : 'idle' as const,
          lastUsed: running ? new Date() : undefined
        };
      });
    } catch (error) {
      console.error('Error getting model usage:', error);
      return [];
    }
  }

  async generateText(prompt: string, model?: string): Promise<string> {
    const modelToUse = model || this.currentModel;

    try {
      const response = await fetch(this.apiBase, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'generate',
          model: modelToUse,
          prompt: prompt,
          options: {
            temperature: 0.7,
            top_p: 0.9,
            top_k: 40,
          }
        })
      });

      if (!response.ok) {
        throw new Error('Failed to generate text');
      }

      const data = await response.json();
      return data.content;
    } catch (error) {
      console.error('Error generating text:', error);
      throw new Error(`Failed to generate text with model ${modelToUse}`);
    }
  }

  async generateTextStream(
    prompt: string,
    onChunk: (chunk: string) => void,
    model?: string
  ): Promise<void> {
    // For now, we'll use the non-streaming version and call onChunk once
    // Streaming would require Server-Sent Events or WebSockets
    const modelToUse = model || this.currentModel;

    try {
      const content = await this.generateText(prompt, modelToUse);
      onChunk(content);
    } catch (error) {
      console.error('Error generating text stream:', error);
      throw new Error(`Failed to generate text stream with model ${modelToUse}`);
    }
  }

  setCurrentModel(model: string) {
    this.currentModel = model;
  }

  getCurrentModel(): string {
    return this.currentModel;
  }

  async isModelAvailable(model: string): Promise<boolean> {
    try {
      const models = await this.listModels();
      return models.some((m: any) => m.name === model);
    } catch (error) {
      return false;
    }
  }

  async pullModel(model: string, onProgress?: (progress: any) => void): Promise<void> {
    try {
      const response = await fetch(this.apiBase, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'pull',
          model: model
        })
      });

      if (!response.ok) {
        throw new Error('Failed to pull model');
      }

      if (onProgress) {
        onProgress({ status: 'completed' });
      }
    } catch (error) {
      console.error('Error pulling model:', error);
      throw new Error(`Failed to pull model ${model}`);
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.apiBase}?action=check`);
      const data = await response.json();
      return data.connected === true;
    } catch (error) {
      return false;
    }
  }
}

export const ollamaService = new OllamaService();
