import { Ollama } from 'ollama';

export interface OllamaModel {
  name: string;
  model: string;
  size: number;
  digest: string;
  details: {
    parent_model: string;
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  expires_at: string;
  size_vram: number;
}

export interface ModelUsage {
  name: string;
  size: string;
  ramUsage: string;
  status: 'running' | 'idle' | 'loading';
  lastUsed?: Date;
}

class OllamaService {
  private ollama: Ollama;
  private currentModel: string = 'llama3.2:3b'; // Default model
  
  constructor() {
    this.ollama = new Ollama({ host: 'http://localhost:11434' });
  }

  async listModels(): Promise<OllamaModel[]> {
    try {
      const response = await this.ollama.list();
      return response.models as OllamaModel[];
    } catch (error) {
      console.error('Error listing models:', error);
      throw new Error('Failed to connect to Ollama. Make sure Ollama is running.');
    }
  }

  async getModelUsage(): Promise<ModelUsage[]> {
    try {
      const models = await this.listModels();
      const runningModels = await this.ollama.ps();
      
      return models.map(model => {
        const running = runningModels.models?.find(rm => rm.name === model.name);
        return {
          name: model.name,
          size: this.formatBytes(model.size),
          ramUsage: running ? this.formatBytes(running.size_vram || 0) : '0 MB',
          status: running ? 'running' : 'idle',
          lastUsed: running ? new Date() : undefined
        };
      });
    } catch (error) {
      console.error('Error getting model usage:', error);
      return [];
    }
  }

  async generateText(prompt: string, model?: string): Promise<string> {
    const modelToUse = model || this.currentModel;
    
    try {
      const response = await this.ollama.generate({
        model: modelToUse,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40,
        }
      });
      
      return response.response;
    } catch (error) {
      console.error('Error generating text:', error);
      throw new Error(`Failed to generate text with model ${modelToUse}`);
    }
  }

  async generateTextStream(
    prompt: string, 
    onChunk: (chunk: string) => void,
    model?: string
  ): Promise<void> {
    const modelToUse = model || this.currentModel;
    
    try {
      const stream = await this.ollama.generate({
        model: modelToUse,
        prompt: prompt,
        stream: true,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40,
        }
      });

      for await (const chunk of stream) {
        if (chunk.response) {
          onChunk(chunk.response);
        }
      }
    } catch (error) {
      console.error('Error generating text stream:', error);
      throw new Error(`Failed to generate text stream with model ${modelToUse}`);
    }
  }

  setCurrentModel(model: string) {
    this.currentModel = model;
  }

  getCurrentModel(): string {
    return this.currentModel;
  }

  async isModelAvailable(model: string): Promise<boolean> {
    try {
      const models = await this.listModels();
      return models.some(m => m.name === model);
    } catch (error) {
      return false;
    }
  }

  async pullModel(model: string, onProgress?: (progress: any) => void): Promise<void> {
    try {
      const stream = await this.ollama.pull({ model, stream: true });
      
      for await (const chunk of stream) {
        if (onProgress) {
          onProgress(chunk);
        }
      }
    } catch (error) {
      console.error('Error pulling model:', error);
      throw new Error(`Failed to pull model ${model}`);
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async checkConnection(): Promise<boolean> {
    try {
      await this.ollama.list();
      return true;
    } catch (error) {
      return false;
    }
  }
}

export const ollamaService = new OllamaService();
