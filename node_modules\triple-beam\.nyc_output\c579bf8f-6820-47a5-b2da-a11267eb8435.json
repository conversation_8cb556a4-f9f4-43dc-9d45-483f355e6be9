{"/Users/<USER>/triple-beam/index.js":{"path":"/Users/<USER>/triple-beam/index.js","statementMap":{"0":{"start":{"line":11,"column":0},"end":{"line":13,"column":3}},"1":{"start":{"line":23,"column":0},"end":{"line":25,"column":3}},"2":{"start":{"line":34,"column":0},"end":{"line":36,"column":3}},"3":{"start":{"line":44,"column":0},"end":{"line":46,"column":3}}},"fnMap":{},"branchMap":{},"s":{"0":1,"1":1,"2":1,"3":1},"f":{},"b":{},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"cc95d94182fb79b1be57a24c1394db64cdb2ae43","contentHash":"fc4e5a28fd95e03cae6b5495203bb1875d4000296fe4308cb90943317c6cd273"},"/Users/<USER>/triple-beam/config/index.js":{"path":"/Users/<USER>/triple-beam/config/index.js","statementMap":{"0":{"start":{"line":14,"column":0},"end":{"line":16,"column":3}},"1":{"start":{"line":22,"column":0},"end":{"line":24,"column":3}},"2":{"start":{"line":30,"column":0},"end":{"line":32,"column":3}}},"fnMap":{},"branchMap":{},"s":{"0":1,"1":1,"2":1},"f":{},"b":{},"_coverageSchema":"1a1c01bbd47fc00a2c39e90264f33305004495a9","hash":"153b0bdac58673ccf1f2107