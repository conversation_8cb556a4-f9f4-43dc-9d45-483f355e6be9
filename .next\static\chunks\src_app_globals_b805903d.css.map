{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/ASCAES/src/app/globals.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  :root {\n    --background: 208 100% 97%;\n    --foreground: 224 71.4% 4.1%;\n    --card: 0 0% 100%;\n    --card-foreground: 224 71.4% 4.1%;\n    --popover: 0 0% 100%;\n    --popover-foreground: 224 71.4% 4.1%;\n    --primary: 170 42% 56%;\n    --primary-foreground: 0 0% 100%;\n    --secondary: 218 41% 90%;\n    --secondary-foreground: 224 71.4% 4.1%;\n    --muted: 210 20% 94%;\n    --muted-foreground: 223.1 39.3% 42%;\n    --accent: 218 41% 68%;\n    --accent-foreground: 0 0% 100%;\n    --destructive: 0 84.2% 60.2%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 220 13% 91%;\n    --input: 220 13% 91%;\n    --ring: 218 41% 68%;\n    --chart-1: 12 76% 61%;\n    --chart-2: 173 58% 39%;\n    --chart-3: 197 37% 24%;\n    --chart-4: 43 74% 66%;\n    --chart-5: 27 87% 67%;\n    --radius: 0.5rem;\n  }\n  .dark {\n    --background: 224 71.4% 4.1%;\n    --foreground: 210 20% 98%;\n    --card: 224 71.4% 5.5%;\n    --card-foreground: 210 20% 98%;\n    --popover: 224 71.4% 4.1%;\n    --popover-foreground: 210 20% 98%;\n    --primary: 170 42% 56%;\n    --primary-foreground: 224 71.4% 4.1%;\n    --secondary: 215 27.9% 16.9%;\n    --secondary-foreground: 210 20% 98%;\n    --muted: 215 27.9% 16.9%;\n    --muted-foreground: 217.9 10.6% 64.9%;\n    --accent: 218 41% 68%;\n    --accent-foreground: 210 20% 98%;\n    --destructive: 0 62.8% 30.6%;\n    --destructive-foreground: 0 0% 98%;\n    --border: 215 27.9% 16.9%;\n    --input: 215 27.9% 16.9%;\n    --ring: 218 41% 68%;\n    --chart-1: 220 70% 50%;\n    --chart-2: 160 60% 45%;\n    --chart-3: 30 80% 55%;\n    --chart-4: 280 65% 60%;\n    --chart-5: 340 75% 55%;\n  }\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n  body {\n    @apply bg-background text-foreground;\n  }\n}\n"], "names": [], "mappings": "AAAA;;AACA;;AACA;;AAEA;EACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2BA;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BE;;EAGA", "debugId": null}}]}