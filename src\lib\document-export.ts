import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export interface DocumentRecord {
  id: string;
  topic: string;
  content: string;
  createdAt: Date;
  settings: any;
}

export class DocumentExportService {
  static async exportAsPDF(doc: DocumentRecord): Promise<void> {
    try {
      // Create a temporary div with the document content
      const tempDiv = document.createElement('div');
      tempDiv.style.position = 'absolute';
      tempDiv.style.left = '-9999px';
      tempDiv.style.width = '8.5in';
      tempDiv.style.padding = '1in';
      tempDiv.style.fontFamily = 'Times New Roman, serif';
      tempDiv.style.fontSize = '12pt';
      tempDiv.style.lineHeight = '1.6';
      tempDiv.style.backgroundColor = 'white';
      tempDiv.style.color = 'black';
      
      // Format content for PDF
      const formattedContent = this.formatContentForPDF(doc);
      tempDiv.innerHTML = formattedContent;
      
      document.body.appendChild(tempDiv);
      
      // Convert to canvas and then PDF
      const canvas = await html2canvas(tempDiv, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      });
      
      document.body.removeChild(tempDiv);
      
      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      const imgWidth = 210;
      const pageHeight = 295;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      
      let position = 0;
      
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
      
      while (heightLeft >= 0) {
        position = heightLeft - imgHeight;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }
      
      pdf.save(`${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`);
    } catch (error) {
      console.error('Error exporting PDF:', error);
      throw new Error('Failed to export as PDF');
    }
  }

  static exportAsRTF(doc: DocumentRecord): void {
    try {
      const rtfContent = this.convertToRTF(doc);
      const blob = new Blob([rtfContent], { type: 'application/rtf' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.rtf`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting RTF:', error);
      throw new Error('Failed to export as RTF');
    }
  }

  static exportAsLaTeX(doc: DocumentRecord): void {
    try {
      const latexContent = this.convertToLaTeX(doc);
      const blob = new Blob([latexContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.tex`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting LaTeX:', error);
      throw new Error('Failed to export as LaTeX');
    }
  }

  static exportAsText(doc: DocumentRecord): void {
    try {
      const textContent = this.convertToPlainText(doc);
      const blob = new Blob([textContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${doc.topic.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting text:', error);
      throw new Error('Failed to export as text');
    }
  }

  private static formatContentForPDF(doc: DocumentRecord): string {
    const header = `
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 16pt; margin: 0;">${doc.topic}</h1>
        <p style="margin: 10px 0; font-size: 12pt;">
          ${doc.settings?.studentName || 'Student Name'}<br>
          ${doc.settings?.professorName || 'Professor Name'}<br>
          ${doc.settings?.courseDetails || 'Course Details'}<br>
          ${new Date(doc.createdAt).toLocaleDateString()}
        </p>
      </div>
    `;
    
    const content = doc.content
      .replace(/\[PAGEBREAK\]/g, '<div style="page-break-before: always;"></div>')
      .replace(/\n\n/g, '</p><p style="text-indent: 0.5in; margin: 0; margin-bottom: 12pt;">')
      .replace(/\n/g, '<br>');
    
    return `${header}<p style="text-indent: 0.5in; margin: 0; margin-bottom: 12pt;">${content}</p>`;
  }

  private static convertToRTF(doc: DocumentRecord): string {
    const header = `{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}
\\f0\\fs24 \\qc ${doc.topic}\\par
\\ql ${doc.settings?.studentName || 'Student Name'}\\par
${doc.settings?.professorName || 'Professor Name'}\\par
${doc.settings?.courseDetails || 'Course Details'}\\par
${new Date(doc.createdAt).toLocaleDateString()}\\par\\par
`;
    
    const content = doc.content
      .replace(/\[PAGEBREAK\]/g, '\\page ')
      .replace(/\n\n/g, '\\par ')
      .replace(/\n/g, '\\line ')
      .replace(/[{}\\]/g, '\\$&');
    
    return `${header}${content}\\par}`;
  }

  private static convertToLaTeX(doc: DocumentRecord): string {
    const header = `\\documentclass[12pt]{article}
\\usepackage[margin=1in]{geometry}
\\usepackage{setspace}
\\doublespacing
\\title{${doc.topic.replace(/[&%$#_{}~^\\]/g, '\\$&')}}
\\author{${(doc.settings?.studentName || 'Student Name').replace(/[&%$#_{}~^\\]/g, '\\$&')}}
\\date{${new Date(doc.createdAt).toLocaleDateString()}}
\\begin{document}
\\maketitle
`;
    
    const content = doc.content
      .replace(/\[PAGEBREAK\]/g, '\\newpage\n')
      .replace(/\n\n/g, '\n\n\\par ')
      .replace(/[&%$#_{}~^\\]/g, '\\$&');
    
    return `${header}\n${content}\n\n\\end{document}`;
  }

  private static convertToPlainText(doc: DocumentRecord): string {
    const header = `${doc.topic}

${doc.settings?.studentName || 'Student Name'}
${doc.settings?.professorName || 'Professor Name'}
${doc.settings?.courseDetails || 'Course Details'}
${new Date(doc.createdAt).toLocaleDateString()}

`;
    
    const content = doc.content
      .replace(/\[PAGEBREAK\]/g, '\n\n--- Page Break ---\n\n')
      .replace(/\n\n/g, '\n\n    '); // Add indentation for paragraphs
    
    return `${header}    ${content}`;
  }
}

export const documentExportService = DocumentExportService;
