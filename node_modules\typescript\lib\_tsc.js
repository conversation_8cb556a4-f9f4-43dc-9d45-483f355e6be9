/*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */


"use strict";

// src/compiler/corePublic.ts
var versionMajorMinor = "5.8";
var version = "5.8.3";

// src/compiler/core.ts
var emptyArray = [];
var emptyMap = /* @__PURE__ */ new Map();
function length(array) {
  return array !== void 0 ? array.length : 0;
}
function forEach(array, callback) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const result = callback(array[i], i);
      if (result) {
        return result;
      }
    }
  }
  return void 0;
}
function firstDefined(array, callback) {
  if (array === void 0) {
    return void 0;
  }
  for (let i = 0; i < array.length; i++) {
    const result = callback(array[i], i);
    if (result !== void 0) {
      return result;
    }
  }
  return void 0;
}
function firstDefinedIterator(iter, callback) {
  for (const value of iter) {
    const result = callback(value);
    if (result !== void 0) {
      return result;
    }
  }
  return void 0;
}
function reduceLeftIterator(iterator, f, initial) {
  let result = initial;
  if (iterator) {
    let pos = 0;
    for (const value of iterator) {
      result = f(result, value, pos);
      pos++;
    }
  }
  return result;
}
function zipWith(arrayA, arrayB, callback) {
  const result = [];
  Debug.assertEqual(arrayA.length, arrayB.length);
  for (let i = 0; i < arrayA.length; i++) {
    result.push(callback(arrayA[i], arrayB[i], i));
  }
  return result;
}
function every(array, callback) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      if (!callback(array[i], i)) {
        return false;
      }
    }
  }
  return true;
}
function find(array, predicate, startIndex) {
  if (array === void 0) return void 0;
  for (let i = startIndex ?? 0; i < array.length; i++) {
    const value = array[i];
    if (predicate(value, i)) {
      return value;
    }
  }
  return void 0;
}
function findLast(array, predicate, startIndex) {
  if (array === void 0) return void 0;
  for (let i = startIndex ?? array.length - 1; i >= 0; i--) {
    const value = array[i];
    if (predicate(value, i)) {
      return value;
    }
  }
  return void 0;
}
function findIndex(array, predicate, startIndex) {
  if (array === void 0) return -1;
  for (let i = startIndex ?? 0; i < array.length; i++) {
    if (predicate(array[i], i)) {
      return i;
    }
  }
  return -1;
}
function findLastIndex(array, predicate, startIndex) {
  if (array === void 0) return -1;
  for (let i = startIndex ?? array.length - 1; i >= 0; i--) {
    if (predicate(array[i], i)) {
      return i;
    }
  }
  return -1;
}
function contains(array, value, equalityComparer = equateValues) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      if (equalityComparer(array[i], value)) {
        return true;
      }
    }
  }
  return false;
}
function indexOfAnyCharCode(text, charCodes, start) {
  for (let i = start ?? 0; i < text.length; i++) {
    if (contains(charCodes, text.charCodeAt(i))) {
      return i;
    }
  }
  return -1;
}
function countWhere(array, predicate) {
  let count = 0;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = array[i];
      if (predicate(v, i)) {
        count++;
      }
    }
  }
  return count;
}
function filter(array, f) {
  if (array !== void 0) {
    const len = array.length;
    let i = 0;
    while (i < len && f(array[i])) i++;
    if (i < len) {
      const result = array.slice(0, i);
      i++;
      while (i < len) {
        const item = array[i];
        if (f(item)) {
          result.push(item);
        }
        i++;
      }
      return result;
    }
  }
  return array;
}
function filterMutate(array, f) {
  let outIndex = 0;
  for (let i = 0; i < array.length; i++) {
    if (f(array[i], i, array)) {
      array[outIndex] = array[i];
      outIndex++;
    }
  }
  array.length = outIndex;
}
function clear(array) {
  array.length = 0;
}
function map(array, f) {
  let result;
  if (array !== void 0) {
    result = [];
    for (let i = 0; i < array.length; i++) {
      result.push(f(array[i], i));
    }
  }
  return result;
}
function* mapIterator(iter, mapFn) {
  for (const x of iter) {
    yield mapFn(x);
  }
}
function sameMap(array, f) {
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      const mapped = f(item, i);
      if (item !== mapped) {
        const result = array.slice(0, i);
        result.push(mapped);
        for (i++; i < array.length; i++) {
          result.push(f(array[i], i));
        }
        return result;
      }
    }
  }
  return array;
}
function flatten(array) {
  const result = [];
  for (let i = 0; i < array.length; i++) {
    const v = array[i];
    if (v) {
      if (isArray(v)) {
        addRange(result, v);
      } else {
        result.push(v);
      }
    }
  }
  return result;
}
function flatMap(array, mapfn) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = mapfn(array[i], i);
      if (v) {
        if (isArray(v)) {
          result = addRange(result, v);
        } else {
          result = append(result, v);
        }
      }
    }
  }
  return result ?? emptyArray;
}
function flatMapToMutable(array, mapfn) {
  const result = [];
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = mapfn(array[i], i);
      if (v) {
        if (isArray(v)) {
          addRange(result, v);
        } else {
          result.push(v);
        }
      }
    }
  }
  return result;
}
function sameFlatMap(array, mapfn) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const item = array[i];
      const mapped = mapfn(item, i);
      if (result || item !== mapped || isArray(mapped)) {
        if (!result) {
          result = array.slice(0, i);
        }
        if (isArray(mapped)) {
          addRange(result, mapped);
        } else {
          result.push(mapped);
        }
      }
    }
  }
  return result ?? array;
}
function mapDefined(array, mapFn) {
  const result = [];
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const mapped = mapFn(array[i], i);
      if (mapped !== void 0) {
        result.push(mapped);
      }
    }
  }
  return result;
}
function* mapDefinedIterator(iter, mapFn) {
  for (const x of iter) {
    const value = mapFn(x);
    if (value !== void 0) {
      yield value;
    }
  }
}
function getOrUpdate(map2, key, callback) {
  if (map2.has(key)) {
    return map2.get(key);
  }
  const value = callback();
  map2.set(key, value);
  return value;
}
function tryAddToSet(set, value) {
  if (!set.has(value)) {
    set.add(value);
    return true;
  }
  return false;
}
function spanMap(array, keyfn, mapfn) {
  let result;
  if (array !== void 0) {
    result = [];
    const len = array.length;
    let previousKey;
    let key;
    let start = 0;
    let pos = 0;
    while (start < len) {
      while (pos < len) {
        const value = array[pos];
        key = keyfn(value, pos);
        if (pos === 0) {
          previousKey = key;
        } else if (key !== previousKey) {
          break;
        }
        pos++;
      }
      if (start < pos) {
        const v = mapfn(array.slice(start, pos), previousKey, start, pos);
        if (v) {
          result.push(v);
        }
        start = pos;
      }
      previousKey = key;
      pos++;
    }
  }
  return result;
}
function some(array, predicate) {
  if (array !== void 0) {
    if (predicate !== void 0) {
      for (let i = 0; i < array.length; i++) {
        if (predicate(array[i])) {
          return true;
        }
      }
    } else {
      return array.length > 0;
    }
  }
  return false;
}
function getRangesWhere(arr, pred, cb) {
  let start;
  for (let i = 0; i < arr.length; i++) {
    if (pred(arr[i])) {
      start = start === void 0 ? i : start;
    } else {
      if (start !== void 0) {
        cb(start, i);
        start = void 0;
      }
    }
  }
  if (start !== void 0) cb(start, arr.length);
}
function concatenate(array1, array2) {
  if (array2 === void 0 || array2.length === 0) return array1;
  if (array1 === void 0 || array1.length === 0) return array2;
  return [...array1, ...array2];
}
function selectIndex(_, i) {
  return i;
}
function indicesOf(array) {
  return array.map(selectIndex);
}
function deduplicateRelational(array, equalityComparer, comparer) {
  const indices = indicesOf(array);
  stableSortIndices(array, indices, comparer);
  let last2 = array[indices[0]];
  const deduplicated = [indices[0]];
  for (let i = 1; i < indices.length; i++) {
    const index = indices[i];
    const item = array[index];
    if (!equalityComparer(last2, item)) {
      deduplicated.push(index);
      last2 = item;
    }
  }
  deduplicated.sort();
  return deduplicated.map((i) => array[i]);
}
function deduplicateEquality(array, equalityComparer) {
  const result = [];
  for (let i = 0; i < array.length; i++) {
    pushIfUnique(result, array[i], equalityComparer);
  }
  return result;
}
function deduplicate(array, equalityComparer, comparer) {
  return array.length === 0 ? [] : array.length === 1 ? array.slice() : comparer ? deduplicateRelational(array, equalityComparer, comparer) : deduplicateEquality(array, equalityComparer);
}
function deduplicateSorted(array, comparer) {
  if (array.length === 0) return emptyArray;
  let last2 = array[0];
  const deduplicated = [last2];
  for (let i = 1; i < array.length; i++) {
    const next = array[i];
    switch (comparer(next, last2)) {
      // equality comparison
      case true:
      // relational comparison
      // falls through
      case 0 /* EqualTo */:
        continue;
      case -1 /* LessThan */:
        return Debug.fail("Array is unsorted.");
    }
    deduplicated.push(last2 = next);
  }
  return deduplicated;
}
function insertSorted(array, insert, compare, equalityComparer, allowDuplicates) {
  if (array.length === 0) {
    array.push(insert);
    return true;
  }
  const insertIndex = binarySearch(array, insert, identity, compare);
  if (insertIndex < 0) {
    if (equalityComparer && !allowDuplicates) {
      const idx = ~insertIndex;
      if (idx > 0 && equalityComparer(insert, array[idx - 1])) {
        return false;
      }
      if (idx < array.length && equalityComparer(insert, array[idx])) {
        array.splice(idx, 1, insert);
        return true;
      }
    }
    array.splice(~insertIndex, 0, insert);
    return true;
  }
  if (allowDuplicates) {
    array.splice(insertIndex, 0, insert);
    return true;
  }
  return false;
}
function sortAndDeduplicate(array, comparer, equalityComparer) {
  return deduplicateSorted(toSorted(array, comparer), equalityComparer ?? comparer ?? compareStringsCaseSensitive);
}
function arrayIsEqualTo(array1, array2, equalityComparer = equateValues) {
  if (array1 === void 0 || array2 === void 0) {
    return array1 === array2;
  }
  if (array1.length !== array2.length) {
    return false;
  }
  for (let i = 0; i < array1.length; i++) {
    if (!equalityComparer(array1[i], array2[i], i)) {
      return false;
    }
  }
  return true;
}
function compact(array) {
  let result;
  if (array !== void 0) {
    for (let i = 0; i < array.length; i++) {
      const v = array[i];
      if (result ?? !v) {
        result ?? (result = array.slice(0, i));
        if (v) {
          result.push(v);
        }
      }
    }
  }
  return result ?? array;
}
function relativeComplement(arrayA, arrayB, comparer) {
  if (!arrayB || !arrayA || arrayB.length === 0 || arrayA.length === 0) return arrayB;
  const result = [];
  loopB:
    for (let offsetA = 0, offsetB = 0; offsetB < arrayB.length; offsetB++) {
      if (offsetB > 0) {
        Debug.assertGreaterThanOrEqual(comparer(arrayB[offsetB], arrayB[offsetB - 1]), 0 /* EqualTo */);
      }
      loopA:
        for (const startA = offsetA; offsetA < arrayA.length; offsetA++) {
          if (offsetA > startA) {
            Debug.assertGreaterThanOrEqual(comparer(arrayA[offsetA], arrayA[offsetA - 1]), 0 /* EqualTo */);
          }
          switch (comparer(arrayB[offsetB], arrayA[offsetA])) {
            case -1 /* LessThan */:
              result.push(arrayB[offsetB]);
              continue loopB;
            case 0 /* EqualTo */:
              continue loopB;
            case 1 /* GreaterThan */:
              continue loopA;
          }
        }
    }
  return result;
}
function append(to, value) {
  if (value === void 0) return to;
  if (to === void 0) return [value];
  to.push(value);
  return to;
}
function toOffset(array, offset) {
  return offset < 0 ? array.length + offset : offset;
}
function addRange(to, from, start, end) {
  if (from === void 0 || from.length === 0) return to;
  if (to === void 0) return from.slice(start, end);
  start = start === void 0 ? 0 : toOffset(from, start);
  end = end === void 0 ? from.length : toOffset(from, end);
  for (let i = start; i < end && i < from.length; i++) {
    if (from[i] !== void 0) {
      to.push(from[i]);
    }
  }
  return to;
}
function pushIfUnique(array, toAdd, equalityComparer) {
  if (contains(array, toAdd, equalityComparer)) {
    return false;
  } else {
    array.push(toAdd);
    return true;
  }
}
function appendIfUnique(array, toAdd, equalityComparer) {
  if (array !== void 0) {
    pushIfUnique(array, toAdd, equalityComparer);
    return array;
  } else {
    return [toAdd];
  }
}
function stableSortIndices(array, indices, comparer) {
  indices.sort((x, y) => comparer(array[x], array[y]) || compareValues(x, y));
}
function toSorted(array, comparer) {
  return array.length === 0 ? emptyArray : array.slice().sort(comparer);
}
function rangeEquals(array1, array2, pos, end) {
  while (pos < end) {
    if (array1[pos] !== array2[pos]) {
      return false;
    }
    pos++;
  }
  return true;
}
var elementAt = !!Array.prototype.at ? (array, offset) => array == null ? void 0 : array.at(offset) : (array, offset) => {
  if (array !== void 0) {
    offset = toOffset(array, offset);
    if (offset < array.length) {
      return array[offset];
    }
  }
  return void 0;
};
function firstOrUndefined(array) {
  return array === void 0 || array.length === 0 ? void 0 : array[0];
}
function firstOrUndefinedIterator(iter) {
  if (iter !== void 0) {
    for (const value of iter) {
      return value;
    }
  }
  return void 0;
}
function first(array) {
  Debug.assert(array.length !== 0);
  return array[0];
}
function firstIterator(iter) {
  for (const value of iter) {
    return value;
  }
  Debug.fail("iterator is empty");
}
function lastOrUndefined(array) {
  return array === void 0 || array.length === 0 ? void 0 : array[array.length - 1];
}
function last(array) {
  Debug.assert(array.length !== 0);
  return array[array.length - 1];
}
function singleOrUndefined(array) {
  return array !== void 0 && array.length === 1 ? array[0] : void 0;
}
function singleOrMany(array) {
  return array !== void 0 && array.length === 1 ? array[0] : array;
}
function replaceElement(array, index, value) {
  const result = array.slice(0);
  result[index] = value;
  return result;
}
function binarySearch(array, value, keySelector, keyComparer, offset) {
  return binarySearchKey(array, keySelector(value), keySelector, keyComparer, offset);
}
function binarySearchKey(array, key, keySelector, keyComparer, offset) {
  if (!some(array)) {
    return -1;
  }
  let low = offset ?? 0;
  let high = array.length - 1;
  while (low <= high) {
    const middle = low + (high - low >> 1);
    const midKey = keySelector(array[middle], middle);
    switch (keyComparer(midKey, key)) {
      case -1 /* LessThan */:
        low = middle + 1;
        break;
      case 0 /* EqualTo */:
        return middle;
      case 1 /* GreaterThan */:
        high = middle - 1;
        break;
    }
  }
  return ~low;
}
function reduceLeft(array, f, initial, start, count) {
  if (array && array.length > 0) {
    const size = array.length;
    if (size > 0) {
      let pos = start === void 0 || start < 0 ? 0 : start;
      const end = count === void 0 || pos + count > size - 1 ? size - 1 : pos + count;
      let result;
      if (arguments.length <= 2) {
        result = array[pos];
        pos++;
      } else {
        result = initial;
      }
      while (pos <= end) {
        result = f(result, array[pos], pos);
        pos++;
      }
      return result;
    }
  }
  return initial;
}
var hasOwnProperty = Object.prototype.hasOwnProperty;
function hasProperty(map2, key) {
  return hasOwnProperty.call(map2, key);
}
function getOwnKeys(map2) {
  const keys = [];
  for (const key in map2) {
    if (hasOwnProperty.call(map2, key)) {
      keys.push(key);
    }
  }
  return keys;
}
function getOwnValues(collection) {
  const values = [];
  for (const key in collection) {
    if (hasOwnProperty.call(collection, key)) {
      values.push(collection[key]);
    }
  }
  return values;
}
function arrayOf(count, f) {
  const result = new Array(count);
  for (let i = 0; i < count; i++) {
    result[i] = f(i);
  }
  return result;
}
function arrayFrom(iterator, map2) {
  const result = [];
  for (const value of iterator) {
    result.push(map2 ? map2(value) : value);
  }
  return result;
}
function assign(t, ...args) {
  for (const arg of args) {
    if (arg === void 0) continue;
    for (const p in arg) {
      if (hasProperty(arg, p)) {
        t[p] = arg[p];
      }
    }
  }
  return t;
}
function equalOwnProperties(left, right, equalityComparer = equateValues) {
  if (left === right) return true;
  if (!left || !right) return false;
  for (const key in left) {
    if (hasOwnProperty.call(left, key)) {
      if (!hasOwnProperty.call(right, key)) return false;
      if (!equalityComparer(left[key], right[key])) return false;
    }
  }
  for (const key in right) {
    if (hasOwnProperty.call(right, key)) {
      if (!hasOwnProperty.call(left, key)) return false;
    }
  }
  return true;
}
function arrayToMap(array, makeKey, makeValue = identity) {
  const result = /* @__PURE__ */ new Map();
  for (let i = 0; i < array.length; i++) {
    const value = array[i];
    const key = makeKey(value);
    if (key !== void 0) result.set(key, makeValue(value));
  }
  return result;
}
function arrayToMultiMap(values, makeKey, makeValue = identity) {
  const result = createMultiMap();
  for (let i = 0; i < values.length; i++) {
    const value = values[i];
    result.add(makeKey(value), makeValue(value));
  }
  return result;
}
function group(values, getGroupId, resultSelector = identity) {
  return arrayFrom(arrayToMultiMap(values, getGroupId).values(), resultSelector);
}
function groupBy(values, keySelector) {
  const result = {};
  if (values !== void 0) {
    for (let i = 0; i < values.length; i++) {
      const value = values[i];
      const key = `${keySelector(value)}`;
      const array = result[key] ?? (result[key] = []);
      array.push(value);
    }
  }
  return result;
}
function extend(first2, second) {
  const result = {};
  for (const id in second) {
    if (hasOwnProperty.call(second, id)) {
      result[id] = second[id];
    }
  }
  for (const id in first2) {
    if (hasOwnProperty.call(first2, id)) {
      result[id] = first2[id];
    }
  }
  return result;
}
function copyProperties(first2, second) {
  for (const id in second) {
    if (hasOwnProperty.call(second, id)) {
      first2[id] = second[id];
    }
  }
}
function maybeBind(obj, fn) {
  return fn == null ? void 0 : fn.bind(obj);
}
function createMultiMap() {
  const map2 = /* @__PURE__ */ new Map();
  map2.add = multiMapAdd;
  map2.remove = multiMapRemove;
  return map2;
}
function multiMapAdd(key, value) {
  let values = this.get(key);
  if (values !== void 0) {
    values.push(value);
  } else {
    this.set(key, values = [value]);
  }
  return values;
}
function multiMapRemove(key, value) {
  const values = this.get(key);
  if (values !== void 0) {
    unorderedRemoveItem(values, value);
    if (!values.length) {
      this.delete(key);
    }
  }
}
function createQueue(items) {
  const elements = (items == null ? void 0 : items.slice()) ?? [];
  let headIndex = 0;
  function isEmpty() {
    return headIndex === elements.length;
  }
  function enqueue(...items2) {
    elements.push(...items2);
  }
  function dequeue() {
    if (isEmpty()) {
      throw new Error("Queue is empty");
    }
    const result = elements[headIndex];
    elements[headIndex] = void 0;
    headIndex++;
    if (headIndex > 100 && headIndex > elements.length >> 1) {
      const newLength = elements.length - headIndex;
      elements.copyWithin(
        /*target*/
        0,
        /*start*/
        headIndex
      );
      elements.length = newLength;
      headIndex = 0;
    }
    return result;
  }
  return {
    enqueue,
    dequeue,
    isEmpty
  };
}
function isArray(value) {
  return Array.isArray(value);
}
function toArray(value) {
  return isArray(value) ? value : [value];
}
function isString(text) {
  return typeof text === "string";
}
function isNumber(x) {
  return typeof x === "number";
}
function tryCast(value, test) {
  return value !== void 0 && test(value) ? value : void 0;
}
function cast(value, test) {
  if (value !== void 0 && test(value)) return value;
  return Debug.fail(`Invalid cast. The supplied value ${value} did not pass the test '${Debug.getFunctionName(test)}'.`);
}
function noop(_) {
}
function returnFalse() {
  return false;
}
function returnTrue() {
  return true;
}
function returnUndefined() {
  return void 0;
}
function identity(x) {
  return x;
}
function toLowerCase(x) {
  return x.toLowerCase();
}
var fileNameLowerCaseRegExp = /[^\u0130\u0131\u00DFa-z0-9\\/:\-_. ]+/g;
function toFileNameLowerCase(x) {
  return fileNameLowerCaseRegExp.test(x) ? x.replace(fileNameLowerCaseRegExp, toLowerCase) : x;
}
function notImplemented() {
  throw new Error("Not implemented");
}
function memoize(callback) {
  let value;
  return () => {
    if (callback) {
      value = callback();
      callback = void 0;
    }
    return value;
  };
}
function memoizeOne(callback) {
  const map2 = /* @__PURE__ */ new Map();
  return (arg) => {
    const key = `${typeof arg}:${arg}`;
    let value = map2.get(key);
    if (value === void 0 && !map2.has(key)) {
      value = callback(arg);
      map2.set(key, value);
    }
    return value;
  };
}
function equateValues(a, b) {
  return a === b;
}
function equateStringsCaseInsensitive(a, b) {
  return a === b || a !== void 0 && b !== void 0 && a.toUpperCase() === b.toUpperCase();
}
function equateStringsCaseSensitive(a, b) {
  return equateValues(a, b);
}
function compareComparableValues(a, b) {
  return a === b ? 0 /* EqualTo */ : a === void 0 ? -1 /* LessThan */ : b === void 0 ? 1 /* GreaterThan */ : a < b ? -1 /* LessThan */ : 1 /* GreaterThan */;
}
function compareValues(a, b) {
  return compareComparableValues(a, b);
}
function maxBy(arr, init, mapper) {
  for (let i = 0; i < arr.length; i++) {
    init = Math.max(init, mapper(arr[i]));
  }
  return init;
}
function min(items, compare) {
  return reduceLeft(items, (x, y) => compare(x, y) === -1 /* LessThan */ ? x : y);
}
function compareStringsCaseInsensitive(a, b) {
  if (a === b) return 0 /* EqualTo */;
  if (a === void 0) return -1 /* LessThan */;
  if (b === void 0) return 1 /* GreaterThan */;
  a = a.toUpperCase();
  b = b.toUpperCase();
  return a < b ? -1 /* LessThan */ : a > b ? 1 /* GreaterThan */ : 0 /* EqualTo */;
}
function compareStringsCaseSensitive(a, b) {
  return compareComparableValues(a, b);
}
function getStringComparer(ignoreCase) {
  return ignoreCase ? compareStringsCaseInsensitive : compareStringsCaseSensitive;
}
var uiComparerCaseSensitive;
var uiLocale;
function setUILocale(value) {
  if (uiLocale !== value) {
    uiLocale = value;
    uiComparerCaseSensitive = void 0;
  }
}
function compareBooleans(a, b) {
  return compareValues(a ? 1 : 0, b ? 1 : 0);
}
function getSpellingSuggestion(name, candidates, getName) {
  const maximumLengthDifference = Math.max(2, Math.floor(name.length * 0.34));
  let bestDistance = Math.floor(name.length * 0.4) + 1;
  let bestCandidate;
  for (const candidate of candidates) {
    const candidateName = getName(candidate);
    if (candidateName !== void 0 && Math.abs(candidateName.length - name.length) <= maximumLengthDifference) {
      if (candidateName === name) {
        continue;
      }
      if (candidateName.length < 3 && candidateName.toLowerCase() !== name.toLowerCase()) {
        continue;
      }
      const distance = levenshteinWithMax(name, candidateName, bestDistance - 0.1);
      if (distance === void 0) {
        continue;
      }
      Debug.assert(distance < bestDistance);
      bestDistance = distance;
      bestCandidate = candidate;
    }
  }
  return bestCandidate;
}
function levenshteinWithMax(s1, s2, max) {
  let previous = new Array(s2.length + 1);
  let current = new Array(s2.length + 1);
  const big = max + 0.01;
  for (let i = 0; i <= s2.length; i++) {
    previous[i] = i;
  }
  for (let i = 1; i <= s1.length; i++) {
    const c1 = s1.charCodeAt(i - 1);
    const minJ = Math.ceil(i > max ? i - max : 1);
    const maxJ = Math.floor(s2.length > max + i ? max + i : s2.length);
    current[0] = i;
    let colMin = i;
    for (let j = 1; j < minJ; j++) {
      current[j] = big;
    }
    for (let j = minJ; j <= maxJ; j++) {
      const substitutionDistance = s1[i - 1].toLowerCase() === s2[j - 1].toLowerCase() ? previous[j - 1] + 0.1 : previous[j - 1] + 2;
      const dist = c1 === s2.charCodeAt(j - 1) ? previous[j - 1] : Math.min(
        /*delete*/
        previous[j] + 1,
        /*insert*/
        current[j - 1] + 1,
        /*substitute*/
        substitutionDistance
      );
      current[j] = dist;
      colMin = Math.min(colMin, dist);
    }
    for (let j = maxJ + 1; j <= s2.length; j++) {
      current[j] = big;
    }
    if (colMin > max) {
      return void 0;
    }
    const temp = previous;
    previous = current;
    current = temp;
  }
  const res = previous[s2.length];
  return res > max ? void 0 : res;
}
function endsWith(str, suffix, ignoreCase) {
  const expectedPos = str.length - suffix.length;
  return expectedPos >= 0 && (ignoreCase ? equateStringsCaseInsensitive(str.slice(expectedPos), suffix) : str.indexOf(suffix, expectedPos) === expectedPos);
}
function removeSuffix(str, suffix) {
  return endsWith(str, suffix) ? str.slice(0, str.length - suffix.length) : str;
}
function orderedRemoveItem(array, item) {
  for (let i = 0; i < array.length; i++) {
    if (array[i] === item) {
      orderedRemoveItemAt(array, i);
      return true;
    }
  }
  return false;
}
function orderedRemoveItemAt(array, index) {
  for (let i = index; i < array.length - 1; i++) {
    array[i] = array[i + 1];
  }
  array.pop();
}
function unorderedRemoveItemAt(array, index) {
  array[index] = array[array.length - 1];
  array.pop();
}
function unorderedRemoveItem(array, item) {
  return unorderedRemoveFirstItemWhere(array, (element) => element === item);
}
function unorderedRemoveFirstItemWhere(array, predicate) {
  for (let i = 0; i < array.length; i++) {
    if (predicate(array[i])) {
      unorderedRemoveItemAt(array, i);
      return true;
    }
  }
  return false;
}
function createGetCanonicalFileName(useCaseSensitiveFileNames2) {
  return useCaseSensitiveFileNames2 ? identity : toFileNameLowerCase;
}
function patternText({ prefix, suffix }) {
  return `${prefix}*${suffix}`;
}
function matchedText(pattern, candidate) {
  Debug.assert(isPatternMatch(pattern, candidate));
  return candidate.substring(pattern.prefix.length, candidate.length - pattern.suffix.length);
}
function findBestPatternMatch(values, getPattern, candidate) {
  let matchedValue;
  let longestMatchPrefixLength = -1;
  for (let i = 0; i < values.length; i++) {
    const v = values[i];
    const pattern = getPattern(v);
    if (pattern.prefix.length > longestMatchPrefixLength && isPatternMatch(pattern, candidate)) {
      longestMatchPrefixLength = pattern.prefix.length;
      matchedValue = v;
    }
  }
  return matchedValue;
}
function startsWith(str, prefix, ignoreCase) {
  return ignoreCase ? equateStringsCaseInsensitive(str.slice(0, prefix.length), prefix) : str.lastIndexOf(prefix, 0) === 0;
}
function removePrefix(str, prefix) {
  return startsWith(str, prefix) ? str.substr(prefix.length) : str;
}
function isPatternMatch({ prefix, suffix }, candidate) {
  return candidate.length >= prefix.length + suffix.length && startsWith(candidate, prefix) && endsWith(candidate, suffix);
}
function and(f, g) {
  return (arg) => f(arg) && g(arg);
}
function or(...fs) {
  return (...args) => {
    let lastResult;
    for (const f of fs) {
      lastResult = f(...args);
      if (lastResult) {
        return lastResult;
      }
    }
    return lastResult;
  };
}
function not(fn) {
  return (...args) => !fn(...args);
}
function assertType(_) {
}
function singleElementArray(t) {
  return t === void 0 ? void 0 : [t];
}
function enumerateInsertsAndDeletes(newItems, oldItems, comparer, inserted, deleted, unchanged) {
  unchanged ?? (unchanged = noop);
  let newIndex = 0;
  let oldIndex = 0;
  const newLen = newItems.length;
  const oldLen = oldItems.length;
  let hasChanges = false;
  while (newIndex < newLen && oldIndex < oldLen) {
    const newItem = newItems[newIndex];
    const oldItem = oldItems[oldIndex];
    const compareResult = comparer(newItem, oldItem);
    if (compareResult === -1 /* LessThan */) {
      inserted(newItem);
      newIndex++;
      hasChanges = true;
    } else if (compareResult === 1 /* GreaterThan */) {
      deleted(oldItem);
      oldIndex++;
      hasChanges = true;
    } else {
      unchanged(oldItem, newItem);
      newIndex++;
      oldIndex++;
    }
  }
  while (newIndex < newLen) {
    inserted(newItems[newIndex++]);
    hasChanges = true;
  }
  while (oldIndex < oldLen) {
    deleted(oldItems[oldIndex++]);
    hasChanges = true;
  }
  return hasChanges;
}
function cartesianProduct(arrays) {
  const result = [];
  cartesianProductWorker(
    arrays,
    result,
    /*outer*/
    void 0,
    0
  );
  return result;
}
function cartesianProductWorker(arrays, result, outer, index) {
  for (const element of arrays[index]) {
    let inner;
    if (outer) {
      inner = outer.slice();
      inner.push(element);
    } else {
      inner = [element];
    }
    if (index === arrays.length - 1) {
      result.push(inner);
    } else {
      cartesianProductWorker(arrays, result, inner, index + 1);
    }
  }
}
function takeWhile(array, predicate) {
  if (array !== void 0) {
    const len = array.length;
    let index = 0;
    while (index < len && predicate(array[index])) {
      index++;
    }
    return array.slice(0, index);
  }
}
function skipWhile(array, predicate) {
  if (array !== void 0) {
    const len = array.length;
    let index = 0;
    while (index < len && predicate(array[index])) {
      index++;
    }
    return array.slice(index);
  }
}
function isNodeLikeSystem() {
  return typeof process !== "undefined" && !!process.nextTick && !process.browser && typeof require !== "undefined";
}

// src/compiler/debug.ts
var Debug;
((Debug2) => {
  let currentAssertionLevel = 0 /* None */;
  Debug2.currentLogLevel = 2 /* Warning */;
  Debug2.isDebugging = false;
  function shouldLog(level) {
    return Debug2.currentLogLevel <= level;
  }
  Debug2.shouldLog = shouldLog;
  function logMessage(level, s) {
    if (Debug2.loggingHost && shouldLog(level)) {
      Debug2.loggingHost.log(level, s);
    }
  }
  function log(s) {
    logMessage(3 /* Info */, s);
  }
  Debug2.log = log;
  ((_log) => {
    function error(s) {
      logMessage(1 /* Error */, s);
    }
    _log.error = error;
    function warn(s) {
      logMessage(2 /* Warning */, s);
    }
    _log.warn = warn;
    function log2(s) {
      logMessage(3 /* Info */, s);
    }
    _log.log = log2;
    function trace2(s) {
      logMessage(4 /* Verbose */, s);
    }
    _log.trace = trace2;
  })(log = Debug2.log || (Debug2.log = {}));
  const assertionCache = {};
  function getAssertionLevel() {
    return currentAssertionLevel;
  }
  Debug2.getAssertionLevel = getAssertionLevel;
  function setAssertionLevel(level) {
    const prevAssertionLevel = currentAssertionLevel;
    currentAssertionLevel = level;
    if (level > prevAssertionLevel) {
      for (const key of getOwnKeys(assertionCache)) {
        const cachedFunc = assertionCache[key];
        if (cachedFunc !== void 0 && Debug2[key] !== cachedFunc.assertion && level >= cachedFunc.level) {
          Debug2[key] = cachedFunc;
          assertionCache[key] = void 0;
        }
      }
    }
  }
  Debug2.setAssertionLevel = setAssertionLevel;
  function shouldAssert(level) {
    return currentAssertionLevel >= level;
  }
  Debug2.shouldAssert = shouldAssert;
  function shouldAssertFunction(level, name) {
    if (!shouldAssert(level)) {
      assertionCache[name] = { level, assertion: Debug2[name] };
      Debug2[name] = noop;
      return false;
    }
    return true;
  }
  function fail(message, stackCrawlMark) {
    debugger;
    const e = new Error(message ? `Debug Failure. ${message}` : "Debug Failure.");
    if (Error.captureStackTrace) {
      Error.captureStackTrace(e, stackCrawlMark || fail);
    }
    throw e;
  }
  Debug2.fail = fail;
  function failBadSyntaxKind(node, message, stackCrawlMark) {
    return fail(
      `${message || "Unexpected node."}\r
Node ${formatSyntaxKind(node.kind)} was unexpected.`,
      stackCrawlMark || failBadSyntaxKind
    );
  }
  Debug2.failBadSyntaxKind = failBadSyntaxKind;
  function assert(expression, message, verboseDebugInfo, stackCrawlMark) {
    if (!expression) {
      message = message ? `False expression: ${message}` : "False expression.";
      if (verboseDebugInfo) {
        message += "\r\nVerbose Debug Information: " + (typeof verboseDebugInfo === "string" ? verboseDebugInfo : verboseDebugInfo());
      }
      fail(message, stackCrawlMark || assert);
    }
  }
  Debug2.assert = assert;
  function assertEqual(a, b, msg, msg2, stackCrawlMark) {
    if (a !== b) {
      const message = msg ? msg2 ? `${msg} ${msg2}` : msg : "";
      fail(`Expected ${a} === ${b}. ${message}`, stackCrawlMark || assertEqual);
    }
  }
  Debug2.assertEqual = assertEqual;
  function assertLessThan(a, b, msg, stackCrawlMark) {
    if (a >= b) {
      fail(`Expected ${a} < ${b}. ${msg || ""}`, stackCrawlMark || assertLessThan);
    }
  }
  Debug2.assertLessThan = assertLessThan;
  function assertLessThanOrEqual(a, b, stackCrawlMark) {
    if (a > b) {
      fail(`Expected ${a} <= ${b}`, stackCrawlMark || assertLessThanOrEqual);
    }
  }
  Debug2.assertLessThanOrEqual = assertLessThanOrEqual;
  function assertGreaterThanOrEqual(a, b, stackCrawlMark) {
    if (a < b) {
      fail(`Expected ${a} >= ${b}`, stackCrawlMark || assertGreaterThanOrEqual);
    }
  }
  Debug2.assertGreaterThanOrEqual = assertGreaterThanOrEqual;
  function assertIsDefined(value, message, stackCrawlMark) {
    if (value === void 0 || value === null) {
      fail(message, stackCrawlMark || assertIsDefined);
    }
  }
  Debug2.assertIsDefined = assertIsDefined;
  function checkDefined(value, message, stackCrawlMark) {
    assertIsDefined(value, message, stackCrawlMark || checkDefined);
    return value;
  }
  Debug2.checkDefined = checkDefined;
  function assertEachIsDefined(value, message, stackCrawlMark) {
    for (const v of value) {
      assertIsDefined(v, message, stackCrawlMark || assertEachIsDefined);
    }
  }
  Debug2.assertEachIsDefined = assertEachIsDefined;
  function checkEachDefined(value, message, stackCrawlMark) {
    assertEachIsDefined(value, message, stackCrawlMark || checkEachDefined);
    return value;
  }
  Debug2.checkEachDefined = checkEachDefined;
  function assertNever(member, message = "Illegal value:", stackCrawlMark) {
    const detail = typeof member === "object" && hasProperty(member, "kind") && hasProperty(member, "pos") ? "SyntaxKind: " + formatSyntaxKind(member.kind) : JSON.stringify(member);
    return fail(`${message} ${detail}`, stackCrawlMark || assertNever);
  }
  Debug2.assertNever = assertNever;
  function assertEachNode(nodes, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertEachNode")) {
      assert(
        test === void 0 || every(nodes, test),
        message || "Unexpected node.",
        () => `Node array did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertEachNode
      );
    }
  }
  Debug2.assertEachNode = assertEachNode;
  function assertNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertNode")) {
      assert(
        node !== void 0 && (test === void 0 || test(node)),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertNode
      );
    }
  }
  Debug2.assertNode = assertNode;
  function assertNotNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertNotNode")) {
      assert(
        node === void 0 || test === void 0 || !test(node),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node.kind)} should not have passed test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertNotNode
      );
    }
  }
  Debug2.assertNotNode = assertNotNode;
  function assertOptionalNode(node, test, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertOptionalNode")) {
      assert(
        test === void 0 || node === void 0 || test(node),
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} did not pass test '${getFunctionName(test)}'.`,
        stackCrawlMark || assertOptionalNode
      );
    }
  }
  Debug2.assertOptionalNode = assertOptionalNode;
  function assertOptionalToken(node, kind, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertOptionalToken")) {
      assert(
        kind === void 0 || node === void 0 || node.kind === kind,
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node == null ? void 0 : node.kind)} was not a '${formatSyntaxKind(kind)}' token.`,
        stackCrawlMark || assertOptionalToken
      );
    }
  }
  Debug2.assertOptionalToken = assertOptionalToken;
  function assertMissingNode(node, message, stackCrawlMark) {
    if (shouldAssertFunction(1 /* Normal */, "assertMissingNode")) {
      assert(
        node === void 0,
        message || "Unexpected node.",
        () => `Node ${formatSyntaxKind(node.kind)} was unexpected'.`,
        stackCrawlMark || assertMissingNode
      );
    }
  }
  Debug2.assertMissingNode = assertMissingNode;
  function type(_value) {
  }
  Debug2.type = type;
  function getFunctionName(func) {
    if (typeof func !== "function") {
      return "";
    } else if (hasProperty(func, "name")) {
      return func.name;
    } else {
      const text = Function.prototype.toString.call(func);
      const match = /^function\s+([\w$]+)\s*\(/.exec(text);
      return match ? match[1] : "";
    }
  }
  Debug2.getFunctionName = getFunctionName;
  function formatSymbol(symbol) {
    return `{ name: ${unescapeLeadingUnderscores(symbol.escapedName)}; flags: ${formatSymbolFlags(symbol.flags)}; declarations: ${map(symbol.declarations, (node) => formatSyntaxKind(node.kind))} }`;
  }
  Debug2.formatSymbol = formatSymbol;
  function formatEnum(value = 0, enumObject, isFlags) {
    const members = getEnumMembers(enumObject);
    if (value === 0) {
      return members.length > 0 && members[0][0] === 0 ? members[0][1] : "0";
    }
    if (isFlags) {
      const result = [];
      let remainingFlags = value;
      for (const [enumValue, enumName] of members) {
        if (enumValue > value) {
          break;
        }
        if (enumValue !== 0 && enumValue & value) {
          result.push(enumName);
          remainingFlags &= ~enumValue;
        }
      }
      if (remainingFlags === 0) {
        return result.join("|");
      }
    } else {
      for (const [enumValue, enumName] of members) {
        if (enumValue === value) {
          return enumName;
        }
      }
    }
    return value.toString();
  }
  Debug2.formatEnum = formatEnum;
  const enumMemberCache = /* @__PURE__ */ new Map();
  function getEnumMembers(enumObject) {
    const existing = enumMemberCache.get(enumObject);
    if (existing) {
      return existing;
    }
    const result = [];
    for (const name in enumObject) {
      const value = enumObject[name];
      if (typeof value === "number") {
        result.push([value, name]);
      }
    }
    const sorted = toSorted(result, (x, y) => compareValues(x[0], y[0]));
    enumMemberCache.set(enumObject, sorted);
    return sorted;
  }
  function formatSyntaxKind(kind) {
    return formatEnum(
      kind,
      SyntaxKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatSyntaxKind = formatSyntaxKind;
  function formatSnippetKind(kind) {
    return formatEnum(
      kind,
      SnippetKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatSnippetKind = formatSnippetKind;
  function formatScriptKind(kind) {
    return formatEnum(
      kind,
      ScriptKind,
      /*isFlags*/
      false
    );
  }
  Debug2.formatScriptKind = formatScriptKind;
  function formatNodeFlags(flags) {
    return formatEnum(
      flags,
      NodeFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatNodeFlags = formatNodeFlags;
  function formatNodeCheckFlags(flags) {
    return formatEnum(
      flags,
      NodeCheckFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatNodeCheckFlags = formatNodeCheckFlags;
  function formatModifierFlags(flags) {
    return formatEnum(
      flags,
      ModifierFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatModifierFlags = formatModifierFlags;
  function formatTransformFlags(flags) {
    return formatEnum(
      flags,
      TransformFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTransformFlags = formatTransformFlags;
  function formatEmitFlags(flags) {
    return formatEnum(
      flags,
      EmitFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatEmitFlags = formatEmitFlags;
  function formatSymbolFlags(flags) {
    return formatEnum(
      flags,
      SymbolFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSymbolFlags = formatSymbolFlags;
  function formatTypeFlags(flags) {
    return formatEnum(
      flags,
      TypeFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTypeFlags = formatTypeFlags;
  function formatSignatureFlags(flags) {
    return formatEnum(
      flags,
      SignatureFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSignatureFlags = formatSignatureFlags;
  function formatObjectFlags(flags) {
    return formatEnum(
      flags,
      ObjectFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatObjectFlags = formatObjectFlags;
  function formatFlowFlags(flags) {
    return formatEnum(
      flags,
      FlowFlags,
      /*isFlags*/
      true
    );
  }
  Debug2.formatFlowFlags = formatFlowFlags;
  function formatRelationComparisonResult(result) {
    return formatEnum(
      result,
      RelationComparisonResult,
      /*isFlags*/
      true
    );
  }
  Debug2.formatRelationComparisonResult = formatRelationComparisonResult;
  function formatCheckMode(mode) {
    return formatEnum(
      mode,
      CheckMode,
      /*isFlags*/
      true
    );
  }
  Debug2.formatCheckMode = formatCheckMode;
  function formatSignatureCheckMode(mode) {
    return formatEnum(
      mode,
      SignatureCheckMode,
      /*isFlags*/
      true
    );
  }
  Debug2.formatSignatureCheckMode = formatSignatureCheckMode;
  function formatTypeFacts(facts) {
    return formatEnum(
      facts,
      TypeFacts,
      /*isFlags*/
      true
    );
  }
  Debug2.formatTypeFacts = formatTypeFacts;
  let isDebugInfoEnabled = false;
  let flowNodeProto;
  function attachFlowNodeDebugInfoWorker(flowNode) {
    if (!("__debugFlowFlags" in flowNode)) {
      Object.defineProperties(flowNode, {
        // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
        __tsDebuggerDisplay: {
          value() {
            const flowHeader = this.flags & 2 /* Start */ ? "FlowStart" : this.flags & 4 /* BranchLabel */ ? "FlowBranchLabel" : this.flags & 8 /* LoopLabel */ ? "FlowLoopLabel" : this.flags & 16 /* Assignment */ ? "FlowAssignment" : this.flags & 32 /* TrueCondition */ ? "FlowTrueCondition" : this.flags & 64 /* FalseCondition */ ? "FlowFalseCondition" : this.flags & 128 /* SwitchClause */ ? "FlowSwitchClause" : this.flags & 256 /* ArrayMutation */ ? "FlowArrayMutation" : this.flags & 512 /* Call */ ? "FlowCall" : this.flags & 1024 /* ReduceLabel */ ? "FlowReduceLabel" : this.flags & 1 /* Unreachable */ ? "FlowUnreachable" : "UnknownFlow";
            const remainingFlags = this.flags & ~(2048 /* Referenced */ - 1);
            return `${flowHeader}${remainingFlags ? ` (${formatFlowFlags(remainingFlags)})` : ""}`;
          }
        },
        __debugFlowFlags: {
          get() {
            return formatEnum(
              this.flags,
              FlowFlags,
              /*isFlags*/
              true
            );
          }
        },
        __debugToString: {
          value() {
            return formatControlFlowGraph(this);
          }
        }
      });
    }
  }
  function attachFlowNodeDebugInfo(flowNode) {
    if (isDebugInfoEnabled) {
      if (typeof Object.setPrototypeOf === "function") {
        if (!flowNodeProto) {
          flowNodeProto = Object.create(Object.prototype);
          attachFlowNodeDebugInfoWorker(flowNodeProto);
        }
        Object.setPrototypeOf(flowNode, flowNodeProto);
      } else {
        attachFlowNodeDebugInfoWorker(flowNode);
      }
    }
    return flowNode;
  }
  Debug2.attachFlowNodeDebugInfo = attachFlowNodeDebugInfo;
  let nodeArrayProto;
  function attachNodeArrayDebugInfoWorker(array) {
    if (!("__tsDebuggerDisplay" in array)) {
      Object.defineProperties(array, {
        __tsDebuggerDisplay: {
          value(defaultValue) {
            defaultValue = String(defaultValue).replace(/(?:,[\s\w]+:[^,]+)+\]$/, "]");
            return `NodeArray ${defaultValue}`;
          }
        }
      });
    }
  }
  function attachNodeArrayDebugInfo(array) {
    if (isDebugInfoEnabled) {
      if (typeof Object.setPrototypeOf === "function") {
        if (!nodeArrayProto) {
          nodeArrayProto = Object.create(Array.prototype);
          attachNodeArrayDebugInfoWorker(nodeArrayProto);
        }
        Object.setPrototypeOf(array, nodeArrayProto);
      } else {
        attachNodeArrayDebugInfoWorker(array);
      }
    }
  }
  Debug2.attachNodeArrayDebugInfo = attachNodeArrayDebugInfo;
  function enableDebugInfo() {
    if (isDebugInfoEnabled) return;
    const weakTypeTextMap = /* @__PURE__ */ new WeakMap();
    const weakNodeTextMap = /* @__PURE__ */ new WeakMap();
    Object.defineProperties(objectAllocator.getSymbolConstructor().prototype, {
      // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
      __tsDebuggerDisplay: {
        value() {
          const symbolHeader = this.flags & 33554432 /* Transient */ ? "TransientSymbol" : "Symbol";
          const remainingSymbolFlags = this.flags & ~33554432 /* Transient */;
          return `${symbolHeader} '${symbolName(this)}'${remainingSymbolFlags ? ` (${formatSymbolFlags(remainingSymbolFlags)})` : ""}`;
        }
      },
      __debugFlags: {
        get() {
          return formatSymbolFlags(this.flags);
        }
      }
    });
    Object.defineProperties(objectAllocator.getTypeConstructor().prototype, {
      // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
      __tsDebuggerDisplay: {
        value() {
          const typeHeader = this.flags & 67359327 /* Intrinsic */ ? `IntrinsicType ${this.intrinsicName}${this.debugIntrinsicName ? ` (${this.debugIntrinsicName})` : ""}` : this.flags & 98304 /* Nullable */ ? "NullableType" : this.flags & 384 /* StringOrNumberLiteral */ ? `LiteralType ${JSON.stringify(this.value)}` : this.flags & 2048 /* BigIntLiteral */ ? `LiteralType ${this.value.negative ? "-" : ""}${this.value.base10Value}n` : this.flags & 8192 /* UniqueESSymbol */ ? "UniqueESSymbolType" : this.flags & 32 /* Enum */ ? "EnumType" : this.flags & 1048576 /* Union */ ? "UnionType" : this.flags & 2097152 /* Intersection */ ? "IntersectionType" : this.flags & 4194304 /* Index */ ? "IndexType" : this.flags & 8388608 /* IndexedAccess */ ? "IndexedAccessType" : this.flags & 16777216 /* Conditional */ ? "ConditionalType" : this.flags & 33554432 /* Substitution */ ? "SubstitutionType" : this.flags & 262144 /* TypeParameter */ ? "TypeParameter" : this.flags & 524288 /* Object */ ? this.objectFlags & 3 /* ClassOrInterface */ ? "InterfaceType" : this.objectFlags & 4 /* Reference */ ? "TypeReference" : this.objectFlags & 8 /* Tuple */ ? "TupleType" : this.objectFlags & 16 /* Anonymous */ ? "AnonymousType" : this.objectFlags & 32 /* Mapped */ ? "MappedType" : this.objectFlags & 1024 /* ReverseMapped */ ? "ReverseMappedType" : this.objectFlags & 256 /* EvolvingArray */ ? "EvolvingArrayType" : "ObjectType" : "Type";
          const remainingObjectFlags = this.flags & 524288 /* Object */ ? this.objectFlags & ~1343 /* ObjectTypeKindMask */ : 0;
          return `${typeHeader}${this.symbol ? ` '${symbolName(this.symbol)}'` : ""}${remainingObjectFlags ? ` (${formatObjectFlags(remainingObjectFlags)})` : ""}`;
        }
      },
      __debugFlags: {
        get() {
          return formatTypeFlags(this.flags);
        }
      },
      __debugObjectFlags: {
        get() {
          return this.flags & 524288 /* Object */ ? formatObjectFlags(this.objectFlags) : "";
        }
      },
      __debugTypeToString: {
        value() {
          let text = weakTypeTextMap.get(this);
          if (text === void 0) {
            text = this.checker.typeToString(this);
            weakTypeTextMap.set(this, text);
          }
          return text;
        }
      }
    });
    Object.defineProperties(objectAllocator.getSignatureConstructor().prototype, {
      __debugFlags: {
        get() {
          return formatSignatureFlags(this.flags);
        }
      },
      __debugSignatureToString: {
        value() {
          var _a;
          return (_a = this.checker) == null ? void 0 : _a.signatureToString(this);
        }
      }
    });
    const nodeConstructors = [
      objectAllocator.getNodeConstructor(),
      objectAllocator.getIdentifierConstructor(),
      objectAllocator.getTokenConstructor(),
      objectAllocator.getSourceFileConstructor()
    ];
    for (const ctor of nodeConstructors) {
      if (!hasProperty(ctor.prototype, "__debugKind")) {
        Object.defineProperties(ctor.prototype, {
          // for use with vscode-js-debug's new customDescriptionGenerator in launch.json
          __tsDebuggerDisplay: {
            value() {
              const nodeHeader = isGeneratedIdentifier(this) ? "GeneratedIdentifier" : isIdentifier(this) ? `Identifier '${idText(this)}'` : isPrivateIdentifier(this) ? `PrivateIdentifier '${idText(this)}'` : isStringLiteral(this) ? `StringLiteral ${JSON.stringify(this.text.length < 10 ? this.text : this.text.slice(10) + "...")}` : isNumericLiteral(this) ? `NumericLiteral ${this.text}` : isBigIntLiteral(this) ? `BigIntLiteral ${this.text}n` : isTypeParameterDeclaration(this) ? "TypeParameterDeclaration" : isParameter(this) ? "ParameterDeclaration" : isConstructorDeclaration(this) ? "ConstructorDeclaration" : isGetAccessorDeclaration(this) ? "GetAccessorDeclaration" : isSetAccessorDeclaration(this) ? "SetAccessorDeclaration" : isCallSignatureDeclaration(this) ? "CallSignatureDeclaration" : isConstructSignatureDeclaration(this) ? "ConstructSignatureDeclaration" : isIndexSignatureDeclaration(this) ? "IndexSignatureDeclaration" : isTypePredicateNode(this) ? "TypePredicateNode" : isTypeReferenceNode(this) ? "TypeReferenceNode" : isFunctionTypeNode(this) ? "FunctionTypeNode" : isConstructorTypeNode(this) ? "ConstructorTypeNode" : isTypeQueryNode(this) ? "TypeQueryNode" : isTypeLiteralNode(this) ? "TypeLiteralNode" : isArrayTypeNode(this) ? "ArrayTypeNode" : isTupleTypeNode(this) ? "TupleTypeNode" : isOptionalTypeNode(this) ? "OptionalTypeNode" : isRestTypeNode(this) ? "RestTypeNode" : isUnionTypeNode(this) ? "UnionTypeNode" : isIntersectionTypeNode(this) ? "IntersectionTypeNode" : isConditionalTypeNode(this) ? "ConditionalTypeNode" : isInferTypeNode(this) ? "InferTypeNode" : isParenthesizedTypeNode(this) ? "ParenthesizedTypeNode" : isThisTypeNode(this) ? "ThisTypeNode" : isTypeOperatorNode(this) ? "TypeOperatorNode" : isIndexedAccessTypeNode(this) ? "IndexedAccessTypeNode" : isMappedTypeNode(this) ? "MappedTypeNode" : isLiteralTypeNode(this) ? "LiteralTypeNode" : isNamedTupleMember(this) ? "NamedTupleMember" : isImportTypeNode(this) ? "ImportTypeNode" : formatSyntaxKind(this.kind);
              return `${nodeHeader}${this.flags ? ` (${formatNodeFlags(this.flags)})` : ""}`;
            }
          },
          __debugKind: {
            get() {
              return formatSyntaxKind(this.kind);
            }
          },
          __debugNodeFlags: {
            get() {
              return formatNodeFlags(this.flags);
            }
          },
          __debugModifierFlags: {
            get() {
              return formatModifierFlags(getEffectiveModifierFlagsNoCache(this));
            }
          },
          __debugTransformFlags: {
            get() {
              return formatTransformFlags(this.transformFlags);
            }
          },
          __debugIsParseTreeNode: {
            get() {
              return isParseTreeNode(this);
            }
          },
          __debugEmitFlags: {
            get() {
              return formatEmitFlags(getEmitFlags(this));
            }
          },
          __debugGetText: {
            value(includeTrivia) {
              if (nodeIsSynthesized(this)) return "";
              let text = weakNodeTextMap.get(this);
              if (text === void 0) {
                const parseNode = getParseTreeNode(this);
                const sourceFile = parseNode && getSourceFileOfNode(parseNode);
                text = sourceFile ? getSourceTextOfNodeFromSourceFile(sourceFile, parseNode, includeTrivia) : "";
                weakNodeTextMap.set(this, text);
              }
              return text;
            }
          }
        });
      }
    }
    isDebugInfoEnabled = true;
  }
  Debug2.enableDebugInfo = enableDebugInfo;
  function formatVariance(varianceFlags) {
    const variance = varianceFlags & 7 /* VarianceMask */;
    let result = variance === 0 /* Invariant */ ? "in out" : variance === 3 /* Bivariant */ ? "[bivariant]" : variance === 2 /* Contravariant */ ? "in" : variance === 1 /* Covariant */ ? "out" : variance === 4 /* Independent */ ? "[independent]" : "";
    if (varianceFlags & 8 /* Unmeasurable */) {
      result += " (unmeasurable)";
    } else if (varianceFlags & 16 /* Unreliable */) {
      result += " (unreliable)";
    }
    return result;
  }
  Debug2.formatVariance = formatVariance;
  class DebugTypeMapper {
    __debugToString() {
      var _a;
      type(this);
      switch (this.kind) {
        case 3 /* Function */:
          return ((_a = this.debugInfo) == null ? void 0 : _a.call(this)) || "(function mapper)";
        case 0 /* Simple */:
          return `${this.source.__debugTypeToString()} -> ${this.target.__debugTypeToString()}`;
        case 1 /* Array */:
          return zipWith(
            this.sources,
            this.targets || map(this.sources, () => "any"),
            (s, t) => `${s.__debugTypeToString()} -> ${typeof t === "string" ? t : t.__debugTypeToString()}`
          ).join(", ");
        case 2 /* Deferred */:
          return zipWith(
            this.sources,
            this.targets,
            (s, t) => `${s.__debugTypeToString()} -> ${t().__debugTypeToString()}`
          ).join(", ");
        case 5 /* Merged */:
        case 4 /* Composite */:
          return `m1: ${this.mapper1.__debugToString().split("\n").join("\n    ")}
m2: ${this.mapper2.__debugToString().split("\n").join("\n    ")}`;
        default:
          return assertNever(this);
      }
    }
  }
  Debug2.DebugTypeMapper = DebugTypeMapper;
  function attachDebugPrototypeIfDebug(mapper) {
    if (Debug2.isDebugging) {
      return Object.setPrototypeOf(mapper, DebugTypeMapper.prototype);
    }
    return mapper;
  }
  Debug2.attachDebugPrototypeIfDebug = attachDebugPrototypeIfDebug;
  function printControlFlowGraph(flowNode) {
    return console.log(formatControlFlowGraph(flowNode));
  }
  Debug2.printControlFlowGraph = printControlFlowGraph;
  function formatControlFlowGraph(flowNode) {
    let nextDebugFlowId = -1;
    function getDebugFlowNodeId(f) {
      if (!f.id) {
        f.id = nextDebugFlowId;
        nextDebugFlowId--;
      }
      return f.id;
    }
    let BoxCharacter;
    ((BoxCharacter2) => {
      BoxCharacter2["lr"] = "\u2500";
      BoxCharacter2["ud"] = "\u2502";
      BoxCharacter2["dr"] = "\u256D";
      BoxCharacter2["dl"] = "\u256E";
      BoxCharacter2["ul"] = "\u256F";
      BoxCharacter2["ur"] = "\u2570";
      BoxCharacter2["udr"] = "\u251C";
      BoxCharacter2["udl"] = "\u2524";
      BoxCharacter2["dlr"] = "\u252C";
      BoxCharacter2["ulr"] = "\u2534";
      BoxCharacter2["udlr"] = "\u256B";
    })(BoxCharacter || (BoxCharacter = {}));
    let Connection;
    ((Connection2) => {
      Connection2[Connection2["None"] = 0] = "None";
      Connection2[Connection2["Up"] = 1] = "Up";
      Connection2[Connection2["Down"] = 2] = "Down";
      Connection2[Connection2["Left"] = 4] = "Left";
      Connection2[Connection2["Right"] = 8] = "Right";
      Connection2[Connection2["UpDown"] = 3] = "UpDown";
      Connection2[Connection2["LeftRight"] = 12] = "LeftRight";
      Connection2[Connection2["UpLeft"] = 5] = "UpLeft";
      Connection2[Connection2["UpRight"] = 9] = "UpRight";
      Connection2[Connection2["DownLeft"] = 6] = "DownLeft";
      Connection2[Connection2["DownRight"] = 10] = "DownRight";
      Connection2[Connection2["UpDownLeft"] = 7] = "UpDownLeft";
      Connection2[Connection2["UpDownRight"] = 11] = "UpDownRight";
      Connection2[Connection2["UpLeftRight"] = 13] = "UpLeftRight";
      Connection2[Connection2["DownLeftRight"] = 14] = "DownLeftRight";
      Connection2[Connection2["UpDownLeftRight"] = 15] = "UpDownLeftRight";
      Connection2[Connection2["NoChildren"] = 16] = "NoChildren";
    })(Connection || (Connection = {}));
    const hasAntecedentFlags = 16 /* Assignment */ | 96 /* Condition */ | 128 /* SwitchClause */ | 256 /* ArrayMutation */ | 512 /* Call */ | 1024 /* ReduceLabel */;
    const hasNodeFlags = 2 /* Start */ | 16 /* Assignment */ | 512 /* Call */ | 96 /* Condition */ | 256 /* ArrayMutation */;
    const links = /* @__PURE__ */ Object.create(
      /*o*/
      null
    );
    const nodes = [];
    const edges = [];
    const root = buildGraphNode(flowNode, /* @__PURE__ */ new Set());
    for (const node of nodes) {
      node.text = renderFlowNode(node.flowNode, node.circular);
      computeLevel(node);
    }
    const height = computeHeight(root);
    const columnWidths = computeColumnWidths(height);
    computeLanes(root, 0);
    return renderGraph();
    function isFlowSwitchClause(f) {
      return !!(f.flags & 128 /* SwitchClause */);
    }
    function hasAntecedents(f) {
      return !!(f.flags & 12 /* Label */) && !!f.antecedent;
    }
    function hasAntecedent(f) {
      return !!(f.flags & hasAntecedentFlags);
    }
    function hasNode(f) {
      return !!(f.flags & hasNodeFlags);
    }
    function getChildren(node) {
      const children = [];
      for (const edge of node.edges) {
        if (edge.source === node) {
          children.push(edge.target);
        }
      }
      return children;
    }
    function getParents(node) {
      const parents = [];
      for (const edge of node.edges) {
        if (edge.target === node) {
          parents.push(edge.source);
        }
      }
      return parents;
    }
    function buildGraphNode(flowNode2, seen) {
      const id = getDebugFlowNodeId(flowNode2);
      let graphNode = links[id];
      if (graphNode && seen.has(flowNode2)) {
        graphNode.circular = true;
        graphNode = {
          id: -1,
          flowNode: flowNode2,
          edges: [],
          text: "",
          lane: -1,
          endLane: -1,
          level: -1,
          circular: "circularity"
        };
        nodes.push(graphNode);
        return graphNode;
      }
      seen.add(flowNode2);
      if (!graphNode) {
        links[id] = graphNode = { id, flowNode: flowNode2, edges: [], text: "", lane: -1, endLane: -1, level: -1, circular: false };
        nodes.push(graphNode);
        if (hasAntecedents(flowNode2)) {
          for (const antecedent of flowNode2.antecedent) {
            buildGraphEdge(graphNode, antecedent, seen);
          }
        } else if (hasAntecedent(flowNode2)) {
          buildGraphEdge(graphNode, flowNode2.antecedent, seen);
        }
      }
      seen.delete(flowNode2);
      return graphNode;
    }
    function buildGraphEdge(source, antecedent, seen) {
      const target = buildGraphNode(antecedent, seen);
      const edge = { source, target };
      edges.push(edge);
      source.edges.push(edge);
      target.edges.push(edge);
    }
    function computeLevel(node) {
      if (node.level !== -1) {
        return node.level;
      }
      let level = 0;
      for (const parent of getParents(node)) {
        level = Math.max(level, computeLevel(parent) + 1);
      }
      return node.level = level;
    }
    function computeHeight(node) {
      let height2 = 0;
      for (const child of getChildren(node)) {
        height2 = Math.max(height2, computeHeight(child));
      }
      return height2 + 1;
    }
    function computeColumnWidths(height2) {
      const columns = fill(Array(height2), 0);
      for (const node of nodes) {
        columns[node.level] = Math.max(columns[node.level], node.text.length);
      }
      return columns;
    }
    function computeLanes(node, lane) {
      if (node.lane === -1) {
        node.lane = lane;
        node.endLane = lane;
        const children = getChildren(node);
        for (let i = 0; i < children.length; i++) {
          if (i > 0) lane++;
          const child = children[i];
          computeLanes(child, lane);
          if (child.endLane > node.endLane) {
            lane = child.endLane;
          }
        }
        node.endLane = lane;
      }
    }
    function getHeader2(flags) {
      if (flags & 2 /* Start */) return "Start";
      if (flags & 4 /* BranchLabel */) return "Branch";
      if (flags & 8 /* LoopLabel */) return "Loop";
      if (flags & 16 /* Assignment */) return "Assignment";
      if (flags & 32 /* TrueCondition */) return "True";
      if (flags & 64 /* FalseCondition */) return "False";
      if (flags & 128 /* SwitchClause */) return "SwitchClause";
      if (flags & 256 /* ArrayMutation */) return "ArrayMutation";
      if (flags & 512 /* Call */) return "Call";
      if (flags & 1024 /* ReduceLabel */) return "ReduceLabel";
      if (flags & 1 /* Unreachable */) return "Unreachable";
      throw new Error();
    }
    function getNodeText(node) {
      const sourceFile = getSourceFileOfNode(node);
      return getSourceTextOfNodeFromSourceFile(
        sourceFile,
        node,
        /*includeTrivia*/
        false
      );
    }
    function renderFlowNode(flowNode2, circular) {
      let text = getHeader2(flowNode2.flags);
      if (circular) {
        text = `${text}#${getDebugFlowNodeId(flowNode2)}`;
      }
      if (isFlowSwitchClause(flowNode2)) {
        const clauses = [];
        const { switchStatement, clauseStart, clauseEnd } = flowNode2.node;
        for (let i = clauseStart; i < clauseEnd; i++) {
          const clause = switchStatement.caseBlock.clauses[i];
          if (isDefaultClause(clause)) {
            clauses.push("default");
          } else {
            clauses.push(getNodeText(clause.expression));
          }
        }
        text += ` (${clauses.join(", ")})`;
      } else if (hasNode(flowNode2)) {
        if (flowNode2.node) {
          text += ` (${getNodeText(flowNode2.node)})`;
        }
      }
      return circular === "circularity" ? `Circular(${text})` : text;
    }
    function renderGraph() {
      const columnCount = columnWidths.length;
      const laneCount = maxBy(nodes, 0, (n) => n.lane) + 1;
      const lanes = fill(Array(laneCount), "");
      const grid = columnWidths.map(() => Array(laneCount));
      const connectors = columnWidths.map(() => fill(Array(laneCount), 0));
      for (const node of nodes) {
        grid[node.level][node.lane] = node;
        const children = getChildren(node);
        for (let i = 0; i < children.length; i++) {
          const child = children[i];
          let connector = 8 /* Right */;
          if (child.lane === node.lane) connector |= 4 /* Left */;
          if (i > 0) connector |= 1 /* Up */;
          if (i < children.length - 1) connector |= 2 /* Down */;
          connectors[node.level][child.lane] |= connector;
        }
        if (children.length === 0) {
          connectors[node.level][node.lane] |= 16 /* NoChildren */;
        }
        const parents = getParents(node);
        for (let i = 0; i < parents.length; i++) {
          const parent = parents[i];
          let connector = 4 /* Left */;
          if (i > 0) connector |= 1 /* Up */;
          if (i < parents.length - 1) connector |= 2 /* Down */;
          connectors[node.level - 1][parent.lane] |= connector;
        }
      }
      for (let column = 0; column < columnCount; column++) {
        for (let lane = 0; lane < laneCount; lane++) {
          const left = column > 0 ? connectors[column - 1][lane] : 0;
          const above = lane > 0 ? connectors[column][lane - 1] : 0;
          let connector = connectors[column][lane];
          if (!connector) {
            if (left & 8 /* Right */) connector |= 12 /* LeftRight */;
            if (above & 2 /* Down */) connector |= 3 /* UpDown */;
            connectors[column][lane] = connector;
          }
        }
      }
      for (let column = 0; column < columnCount; column++) {
        for (let lane = 0; lane < lanes.length; lane++) {
          const connector = connectors[column][lane];
          const fill2 = connector & 4 /* Left */ ? "\u2500" /* lr */ : " ";
          const node = grid[column][lane];
          if (!node) {
            if (column < columnCount - 1) {
              writeLane(lane, repeat(fill2, columnWidths[column] + 1));
            }
          } else {
            writeLane(lane, node.text);
            if (column < columnCount - 1) {
              writeLane(lane, " ");
              writeLane(lane, repeat(fill2, columnWidths[column] - node.text.length));
            }
          }
          writeLane(lane, getBoxCharacter(connector));
          writeLane(lane, connector & 8 /* Right */ && column < columnCount - 1 && !grid[column + 1][lane] ? "\u2500" /* lr */ : " ");
        }
      }
      return `
${lanes.join("\n")}
`;
      function writeLane(lane, text) {
        lanes[lane] += text;
      }
    }
    function getBoxCharacter(connector) {
      switch (connector) {
        case 3 /* UpDown */:
          return "\u2502" /* ud */;
        case 12 /* LeftRight */:
          return "\u2500" /* lr */;
        case 5 /* UpLeft */:
          return "\u256F" /* ul */;
        case 9 /* UpRight */:
          return "\u2570" /* ur */;
        case 6 /* DownLeft */:
          return "\u256E" /* dl */;
        case 10 /* DownRight */:
          return "\u256D" /* dr */;
        case 7 /* UpDownLeft */:
          return "\u2524" /* udl */;
        case 11 /* UpDownRight */:
          return "\u251C" /* udr */;
        case 13 /* UpLeftRight */:
          return "\u2534" /* ulr */;
        case 14 /* DownLeftRight */:
          return "\u252C" /* dlr */;
        case 15 /* UpDownLeftRight */:
          return "\u256B" /* udlr */;
      }
      return " ";
    }
    function fill(array, value) {
      if (array.fill) {
        array.fill(value);
      } else {
        for (let i = 0; i < array.length; i++) {
          array[i] = value;
        }
      }
      return array;
    }
    function repeat(ch, length2) {
      if (ch.repeat) {
        return length2 > 0 ? ch.repeat(length2) : "";
      }
      let s = "";
      while (s.length < length2) {
        s += ch;
      }
      return s;
    }
  }
  Debug2.formatControlFlowGraph = formatControlFlowGraph;
})(Debug || (Debug = {}));

// src/compiler/semver.ts
var versionRegExp = /^(0|[1-9]\d*)(?:\.(0|[1-9]\d*)(?:\.(0|[1-9]\d*)(?:-([a-z0-9-.]+))?(?:\+([a-z0-9-.]+))?)?)?$/i;
var prereleaseRegExp = /^(?:0|[1-9]\d*|[a-z-][a-z0-9-]*)(?:\.(?:0|[1-9]\d*|[a-z-][a-z0-9-]*))*$/i;
var prereleasePartRegExp = /^(?:0|[1-9]\d*|[a-z-][a-z0-9-]*)$/i;
var buildRegExp = /^[a-z0-9-]+(?:\.[a-z0-9-]+)*$/i;
var buildPartRegExp = /^[a-z0-9-]+$/i;
var numericIdentifierRegExp = /^(?:0|[1-9]\d*)$/;
var _Version = class _Version {
  constructor(major, minor = 0, patch = 0, prerelease = "", build2 = "") {
    if (typeof major === "string") {
      const result = Debug.checkDefined(tryParseComponents(major), "Invalid version");
      ({ major, minor, patch, prerelease, build: build2 } = result);
    }
    Debug.assert(major >= 0, "Invalid argument: major");
    Debug.assert(minor >= 0, "Invalid argument: minor");
    Debug.assert(patch >= 0, "Invalid argument: patch");
    const prereleaseArray = prerelease ? isArray(prerelease) ? prerelease : prerelease.split(".") : emptyArray;
    const buildArray = build2 ? isArray(build2) ? build2 : build2.split(".") : emptyArray;
    Debug.assert(every(prereleaseArray, (s) => prereleasePartRegExp.test(s)), "Invalid argument: prerelease");
    Debug.assert(every(buildArray, (s) => buildPartRegExp.test(s)), "Invalid argument: build");
    this.major = major;
    this.minor = minor;
    this.patch = patch;
    this.prerelease = prereleaseArray;
    this.build = buildArray;
  }
  static tryParse(text) {
    const result = tryParseComponents(text);
    if (!result) return void 0;
    const { major, minor, patch, prerelease, build: build2 } = result;
    return new _Version(major, minor, patch, prerelease, build2);
  }
  compareTo(other) {
    if (this === other) return 0 /* EqualTo */;
    if (other === void 0) return 1 /* GreaterThan */;
    return compareValues(this.major, other.major) || compareValues(this.minor, other.minor) || compareValues(this.patch, other.patch) || comparePrereleaseIdentifiers(this.prerelease, other.prerelease);
  }
  increment(field) {
    switch (field) {
      case "major":
        return new _Version(this.major + 1, 0, 0);
      case "minor":
        return new _Version(this.major, this.minor + 1, 0);
      case "patch":
        return new _Version(this.major, this.minor, this.patch + 1);
      default:
        return Debug.assertNever(field);
    }
  }
  with(fields) {
    const {
      major = this.major,
      minor = this.minor,
      patch = this.patch,
      prerelease = this.prerelease,
      build: build2 = this.build
    } = fields;
    return new _Version(major, minor, patch, prerelease, build2);
  }
  toString() {
    let result = `${this.major}.${this.minor}.${this.patch}`;
    if (some(this.prerelease)) result += `-${this.prerelease.join(".")}`;
    if (some(this.build)) result += `+${this.build.join(".")}`;
    return result;
  }
};
_Version.zero = new _Version(0, 0, 0, ["0"]);
var Version = _Version;
function tryParseComponents(text) {
  const match = versionRegExp.exec(text);
  if (!match) return void 0;
  const [, major, minor = "0", patch = "0", prerelease = "", build2 = ""] = match;
  if (prerelease && !prereleaseRegExp.test(prerelease)) return void 0;
  if (build2 && !buildRegExp.test(build2)) return void 0;
  return {
    major: parseInt(major, 10),
    minor: parseInt(minor, 10),
    patch: parseInt(patch, 10),
    prerelease,
    build: build2
  };
}
function comparePrereleaseIdentifiers(left, right) {
  if (left === right) return 0 /* EqualTo */;
  if (left.length === 0) return right.length === 0 ? 0 /* EqualTo */ : 1 /* GreaterThan */;
  if (right.length === 0) return -1 /* LessThan */;
  const length2 = Math.min(left.length, right.length);
  for (let i = 0; i < length2; i++) {
    const leftIdentifier = left[i];
    const rightIdentifier = right[i];
    if (leftIdentifier === rightIdentifier) continue;
    const leftIsNumeric = numericIdentifierRegExp.test(leftIdentifier);
    const rightIsNumeric = numericIdentifierRegExp.test(rightIdentifier);
    if (leftIsNumeric || rightIsNumeric) {
      if (leftIsNumeric !== rightIsNumeric) return leftIsNumeric ? -1 /* LessThan */ : 1 /* GreaterThan */;
      const result = compareValues(+leftIdentifier, +rightIdentifier);
      if (result) return result;
    } else {
      const result = compareStringsCaseSensitive(leftIdentifier, rightIdentifier);
      if (result) return result;
    }
  }
  return compareValues(left.length, right.length);
}
var VersionRange = class _VersionRange {
  constructor(spec) {
    this._alternatives = spec ? Debug.checkDefined(parseRange(spec), "Invalid range spec.") : emptyArray;
  }
  static tryParse(text) {
    const sets = parseRange(text);
    if (sets) {
      const range = new _VersionRange("");
      range._alternatives = sets;
      return range;
    }
    return void 0;
  }
  /**
   * Tests whether a version matches the range. This is equivalent to `satisfies(version, range, { includePrerelease: true })`.
   * in `node-semver`.
   */
  test(version2) {
    if (typeof version2 === "string") version2 = new Version(version2);
    return testDisjunction(version2, this._alternatives);
  }
  toString() {
    return formatDisjunction(this._alternatives);
  }
};
var logicalOrRegExp = /\|\|/;
var whitespaceRegExp = /\s+/;
var partialRegExp = /^([x*0]|[1-9]\d*)(?:\.([x*0]|[1-9]\d*)(?:\.([x*0]|[1-9]\d*)(?:-([a-z0-9-.]+))?(?:\+([a-z0-9-.]+))?)?)?$/i;
var hyphenRegExp = /^\s*([a-z0-9-+.*]+)\s+-\s+([a-z0-9-+.*]+)\s*$/i;
var rangeRegExp = /^([~^<>=]|<=|>=)?\s*([a-z0-9-+.*]+)$/i;
function parseRange(text) {
  const alternatives = [];
  for (let range of text.trim().split(logicalOrRegExp)) {
    if (!range) continue;
    const comparators = [];
    range = range.trim();
    const match = hyphenRegExp.exec(range);
    if (match) {
      if (!parseHyphen(match[1], match[2], comparators)) return void 0;
    } else {
      for (const simple of range.split(whitespaceRegExp)) {
        const match2 = rangeRegExp.exec(simple.trim());
        if (!match2 || !parseComparator(match2[1], match2[2], comparators)) return void 0;
      }
    }
    alternatives.push(comparators);
  }
  return alternatives;
}
function parsePartial(text) {
  const match = partialRegExp.exec(text);
  if (!match) return void 0;
  const [, major, minor = "*", patch = "*", prerelease, build2] = match;
  const version2 = new Version(
    isWildcard(major) ? 0 : parseInt(major, 10),
    isWildcard(major) || isWildcard(minor) ? 0 : parseInt(minor, 10),
    isWildcard(major) || isWildcard(minor) || isWildcard(patch) ? 0 : parseInt(patch, 10),
    prerelease,
    build2
  );
  return { version: version2, major, minor, patch };
}
function parseHyphen(left, right, comparators) {
  const leftResult = parsePartial(left);
  if (!leftResult) return false;
  const rightResult = parsePartial(right);
  if (!rightResult) return false;
  if (!isWildcard(leftResult.major)) {
    comparators.push(createComparator(">=", leftResult.version));
  }
  if (!isWildcard(rightResult.major)) {
    comparators.push(
      isWildcard(rightResult.minor) ? createComparator("<", rightResult.version.increment("major")) : isWildcard(rightResult.patch) ? createComparator("<", rightResult.version.increment("minor")) : createComparator("<=", rightResult.version)
    );
  }
  return true;
}
function parseComparator(operator, text, comparators) {
  const result = parsePartial(text);
  if (!result) return false;
  const { version: version2, major, minor, patch } = result;
  if (!isWildcard(major)) {
    switch (operator) {
      case "~":
        comparators.push(createComparator(">=", version2));
        comparators.push(createComparator(
          "<",
          version2.increment(
            isWildcard(minor) ? "major" : "minor"
          )
        ));
        break;
      case "^":
        comparators.push(createComparator(">=", version2));
        comparators.push(createComparator(
          "<",
          version2.increment(
            version2.major > 0 || isWildcard(minor) ? "major" : version2.minor > 0 || isWildcard(patch) ? "minor" : "patch"
          )
        ));
        break;
      case "<":
      case ">=":
        comparators.push(
          isWildcard(minor) || isWildcard(patch) ? createComparator(operator, version2.with({ prerelease: "0" })) : createComparator(operator, version2)
        );
        break;
      case "<=":
      case ">":
        comparators.push(
          isWildcard(minor) ? createComparator(operator === "<=" ? "<" : ">=", version2.increment("major").with({ prerelease: "0" })) : isWildcard(patch) ? createComparator(operator === "<=" ? "<" : ">=", version2.increment("minor").with({ prerelease: "0" })) : createComparator(operator, version2)
        );
        break;
      case "=":
      case void 0:
        if (isWildcard(minor) || isWildcard(patch)) {
          comparators.push(createComparator(">=", version2.with({ prerelease: "0" })));
          comparators.push(createComparator("<", version2.increment(isWildcard(minor) ? "major" : "minor").with({ prerelease: "0" })));
        } else {
          comparators.push(createComparator("=", version2));
        }
        break;
      default:
        return false;
    }
  } else if (operator === "<" || operator === ">") {
    comparators.push(createComparator("<", Version.zero));
  }
  return true;
}
function isWildcard(part) {
  return part === "*" || part === "x" || part === "X";
}
function createComparator(operator, operand) {
  return { operator, operand };
}
function testDisjunction(version2, alternatives) {
  if (alternatives.length === 0) return true;
  for (const alternative of alternatives) {
    if (testAlternative(version2, alternative)) return true;
  }
  return false;
}
function testAlternative(version2, comparators) {
  for (const comparator of comparators) {
    if (!testComparator(version2, comparator.operator, comparator.operand)) return false;
  }
  return true;
}
function testComparator(version2, operator, operand) {
  const cmp = version2.compareTo(operand);
  switch (operator) {
    case "<":
      return cmp < 0;
    case "<=":
      return cmp <= 0;
    case ">":
      return cmp > 0;
    case ">=":
      return cmp >= 0;
    case "=":
      return cmp === 0;
    default:
      return Debug.assertNever(operator);
  }
}
function formatDisjunction(alternatives) {
  return map(alternatives, formatAlternative).join(" || ") || "*";
}
function formatAlternative(comparators) {
  return map(comparators, formatComparator).join(" ");
}
function formatComparator(comparator) {
  return `${comparator.operator}${comparator.operand}`;
}

// src/compiler/performanceCore.ts
function tryGetPerformance() {
  if (isNodeLikeSystem()) {
    try {
      const { performance: performance2 } = require("perf_hooks");
      if (performance2) {
        return {
          shouldWriteNativeEvents: false,
          performance: performance2
        };
      }
    } catch {
    }
  }
  if (typeof performance === "object") {
    return {
      shouldWriteNativeEvents: true,
      performance
    };
  }
  return void 0;
}
function tryGetPerformanceHooks() {
  const p = tryGetPerformance();
  if (!p) return void 0;
  const { shouldWriteNativeEvents, performance: performance2 } = p;
  const hooks = {
    shouldWriteNativeEvents,
    performance: void 0,
    performanceTime: void 0
  };
  if (typeof performance2.timeOrigin === "number" && typeof performance2.now === "function") {
    hooks.performanceTime = performance2;
  }
  if (hooks.performanceTime && typeof performance2.mark === "function" && typeof performance2.measure === "function" && typeof performance2.clearMarks === "function" && typeof performance2.clearMeasures === "function") {
    hooks.performance = performance2;
  }
  return hooks;
}
var nativePerformanceHooks = tryGetPerformanceHooks();
var nativePerformanceTime = nativePerformanceHooks == null ? void 0 : nativePerformanceHooks.performanceTime;
function tryGetNativePerformanceHooks() {
  return nativePerformanceHooks;
}
var timestamp = nativePerformanceTime ? () => nativePerformanceTime.now() : Date.now;

// src/compiler/performance.ts
var perfHooks;
var performanceImpl;
function createTimerIf(condition, measureName, startMarkName, endMarkName) {
  return condition ? createTimer(measureName, startMarkName, endMarkName) : nullTimer;
}
function createTimer(measureName, startMarkName, endMarkName) {
  let enterCount = 0;
  return {
    enter,
    exit
  };
  function enter() {
    if (++enterCount === 1) {
      mark(startMarkName);
    }
  }
  function exit() {
    if (--enterCount === 0) {
      mark(endMarkName);
      measure(measureName, startMarkName, endMarkName);
    } else if (enterCount < 0) {
      Debug.fail("enter/exit count does not match.");
    }
  }
}
var nullTimer = { enter: noop, exit: noop };
var enabled = false;
var timeorigin = timestamp();
var marks = /* @__PURE__ */ new Map();
var counts = /* @__PURE__ */ new Map();
var durations = /* @__PURE__ */ new Map();
function mark(markName) {
  if (enabled) {
    const count = counts.get(markName) ?? 0;
    counts.set(markName, count + 1);
    marks.set(markName, timestamp());
    performanceImpl == null ? void 0 : performanceImpl.mark(markName);
    if (typeof onProfilerEvent === "function") {
      onProfilerEvent(markName);
    }
  }
}
function measure(measureName, startMarkName, endMarkName) {
  if (enabled) {
    const end = (endMarkName !== void 0 ? marks.get(endMarkName) : void 0) ?? timestamp();
    const start = (startMarkName !== void 0 ? marks.get(startMarkName) : void 0) ?? timeorigin;
    const previousDuration = durations.get(measureName) || 0;
    durations.set(measureName, previousDuration + (end - start));
    performanceImpl == null ? void 0 : performanceImpl.measure(measureName, startMarkName, endMarkName);
  }
}
function getCount(markName) {
  return counts.get(markName) || 0;
}
function getDuration(measureName) {
  return durations.get(measureName) || 0;
}
function forEachMeasure(cb) {
  durations.forEach((duration, measureName) => cb(measureName, duration));
}
function forEachMark(cb) {
  marks.forEach((_time, markName) => cb(markName));
}
function clearMeasures(name) {
  if (name !== void 0) durations.delete(name);
  else durations.clear();
  performanceImpl == null ? void 0 : performanceImpl.clearMeasures(name);
}
function clearMarks(name) {
  if (name !== void 0) {
    counts.delete(name);
    marks.delete(name);
  } else {
    counts.clear();
    marks.clear();
  }
  performanceImpl == null ? void 0 : performanceImpl.clearMarks(name);
}
function isEnabled() {
  return enabled;
}
function enable(system = sys) {
  var _a;
  if (!enabled) {
    enabled = true;
    perfHooks || (perfHooks = tryGetNativePerformanceHooks());
    if (perfHooks == null ? void 0 : perfHooks.performance) {
      timeorigin = perfHooks.performance.timeOrigin;
      if (perfHooks.shouldWriteNativeEvents || ((_a = system == null ? void 0 : system.cpuProfilingEnabled) == null ? void 0 : _a.call(system)) || (system == null ? void 0 : system.debugMode)) {
        performanceImpl = perfHooks.performance;
      }
    }
  }
  return true;
}
function disable() {
  if (enabled) {
    marks.clear();
    counts.clear();
    durations.clear();
    performanceImpl = void 0;
    enabled = false;
  }
}

// src/compiler/tracing.ts
var tracing;
var tracingEnabled;
((tracingEnabled2) => {
  let fs;
  let traceCount = 0;
  let traceFd = 0;
  let mode;
  const typeCatalog = [];
  let legendPath;
  const legend = [];
  function startTracing2(tracingMode, traceDir, configFilePath) {
    Debug.assert(!tracing, "Tracing already started");
    if (fs === void 0) {
      try {
        fs = require("fs");
      } catch (e) {
        throw new Error(`tracing requires having fs
(original error: ${e.message || e})`);
      }
    }
    mode = tracingMode;
    typeCatalog.length = 0;
    if (legendPath === void 0) {
      legendPath = combinePaths(traceDir, "legend.json");
    }
    if (!fs.existsSync(traceDir)) {
      fs.mkdirSync(traceDir, { recursive: true });
    }
    const countPart = mode === "build" ? `.${process.pid}-${++traceCount}` : mode === "server" ? `.${process.pid}` : ``;
    const tracePath = combinePaths(traceDir, `trace${countPart}.json`);
    const typesPath = combinePaths(traceDir, `types${countPart}.json`);
    legend.push({
      configFilePath,
      tracePath,
      typesPath
    });
    traceFd = fs.openSync(tracePath, "w");
    tracing = tracingEnabled2;
    const meta = { cat: "__metadata", ph: "M", ts: 1e3 * timestamp(), pid: 1, tid: 1 };
    fs.writeSync(
      traceFd,
      "[\n" + [{ name: "process_name", args: { name: "tsc" }, ...meta }, { name: "thread_name", args: { name: "Main" }, ...meta }, { name: "TracingStartedInBrowser", ...meta, cat: "disabled-by-default-devtools.timeline" }].map((v) => JSON.stringify(v)).join(",\n")
    );
  }
  tracingEnabled2.startTracing = startTracing2;
  function stopTracing() {
    Debug.assert(tracing, "Tracing is not in progress");
    Debug.assert(!!typeCatalog.length === (mode !== "server"));
    fs.writeSync(traceFd, `
]
`);
    fs.closeSync(traceFd);
    tracing = void 0;
    if (typeCatalog.length) {
      dumpTypes(typeCatalog);
    } else {
      legend[legend.length - 1].typesPath = void 0;
    }
  }
  tracingEnabled2.stopTracing = stopTracing;
  function recordType(type) {
    if (mode !== "server") {
      typeCatalog.push(type);
    }
  }
  tracingEnabled2.recordType = recordType;
  let Phase;
  ((Phase2) => {
    Phase2["Parse"] = "parse";
    Phase2["Program"] = "program";
    Phase2["Bind"] = "bind";
    Phase2["Check"] = "check";
    Phase2["CheckTypes"] = "checkTypes";
    Phase2["Emit"] = "emit";
    Phase2["Session"] = "session";
  })(Phase = tracingEnabled2.Phase || (tracingEnabled2.Phase = {}));
  function instant(phase, name, args) {
    writeEvent("I", phase, name, args, `"s":"g"`);
  }
  tracingEnabled2.instant = instant;
  const eventStack = [];
  function push(phase, name, args, separateBeginAndEnd = false) {
    if (separateBeginAndEnd) {
      writeEvent("B", phase, name, args);
    }
    eventStack.push({ phase, name, args, time: 1e3 * timestamp(), separateBeginAndEnd });
  }
  tracingEnabled2.push = push;
  function pop(results) {
    Debug.assert(eventStack.length > 0);
    writeStackEvent(eventStack.length - 1, 1e3 * timestamp(), results);
    eventStack.length--;
  }
  tracingEnabled2.pop = pop;
  function popAll() {
    const endTime = 1e3 * timestamp();
    for (let i = eventStack.length - 1; i >= 0; i--) {
      writeStackEvent(i, endTime);
    }
    eventStack.length = 0;
  }
  tracingEnabled2.popAll = popAll;
  const sampleInterval = 1e3 * 10;
  function writeStackEvent(index, endTime, results) {
    const { phase, name, args, time, separateBeginAndEnd } = eventStack[index];
    if (separateBeginAndEnd) {
      Debug.assert(!results, "`results` are not supported for events with `separateBeginAndEnd`");
      writeEvent(
        "E",
        phase,
        name,
        args,
        /*extras*/
        void 0,
        endTime
      );
    } else if (sampleInterval - time % sampleInterval <= endTime - time) {
      writeEvent("X", phase, name, { ...args, results }, `"dur":${endTime - time}`, time);
    }
  }
  function writeEvent(eventType, phase, name, args, extras, time = 1e3 * timestamp()) {
    if (mode === "server" && phase === "checkTypes" /* CheckTypes */) return;
    mark("beginTracing");
    fs.writeSync(traceFd, `,
{"pid":1,"tid":1,"ph":"${eventType}","cat":"${phase}","ts":${time},"name":"${name}"`);
    if (extras) fs.writeSync(traceFd, `,${extras}`);
    if (args) fs.writeSync(traceFd, `,"args":${JSON.stringify(args)}`);
    fs.writeSync(traceFd, `}`);
    mark("endTracing");
    measure("Tracing", "beginTracing", "endTracing");
  }
  function getLocation(node) {
    const file = getSourceFileOfNode(node);
    return !file ? void 0 : {
      path: file.path,
      start: indexFromOne(getLineAndCharacterOfPosition(file, node.pos)),
      end: indexFromOne(getLineAndCharacterOfPosition(file, node.end))
    };
    function indexFromOne(lc) {
      return {
        line: lc.line + 1,
        character: lc.character + 1
      };
    }
  }
  function dumpTypes(types) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l, _m, _n, _o, _p, _q, _r, _s;
    mark("beginDumpTypes");
    const typesPath = legend[legend.length - 1].typesPath;
    const typesFd = fs.openSync(typesPath, "w");
    const recursionIdentityMap = /* @__PURE__ */ new Map();
    fs.writeSync(typesFd, "[");
    const numTypes = types.length;
    for (let i = 0; i < numTypes; i++) {
      const type = types[i];
      const objectFlags = type.objectFlags;
      const symbol = type.aliasSymbol ?? type.symbol;
      let display;
      if (objectFlags & 16 /* Anonymous */ | type.flags & 2944 /* Literal */) {
        try {
          display = (_a = type.checker) == null ? void 0 : _a.typeToString(type);
        } catch {
          display = void 0;
        }
      }
      let indexedAccessProperties = {};
      if (type.flags & 8388608 /* IndexedAccess */) {
        const indexedAccessType = type;
        indexedAccessProperties = {
          indexedAccessObjectType: (_b = indexedAccessType.objectType) == null ? void 0 : _b.id,
          indexedAccessIndexType: (_c = indexedAccessType.indexType) == null ? void 0 : _c.id
        };
      }
      let referenceProperties = {};
      if (objectFlags & 4 /* Reference */) {
        const referenceType = type;
        referenceProperties = {
          instantiatedType: (_d = referenceType.target) == null ? void 0 : _d.id,
          typeArguments: (_e = referenceType.resolvedTypeArguments) == null ? void 0 : _e.map((t) => t.id),
          referenceLocation: getLocation(referenceType.node)
        };
      }
      let conditionalProperties = {};
      if (type.flags & 16777216 /* Conditional */) {
        const conditionalType = type;
        conditionalProperties = {
          conditionalCheckType: (_f = conditionalType.checkType) == null ? void 0 : _f.id,
          conditionalExtendsType: (_g = conditionalType.extendsType) == null ? void 0 : _g.id,
          conditionalTrueType: ((_h = conditionalType.resolvedTrueType) == null ? void 0 : _h.id) ?? -1,
          conditionalFalseType: ((_i = conditionalType.resolvedFalseType) == null ? void 0 : _i.id) ?? -1
        };
      }
      let substitutionProperties = {};
      if (type.flags & 33554432 /* Substitution */) {
        const substitutionType = type;
        substitutionProperties = {
          substitutionBaseType: (_j = substitutionType.baseType) == null ? void 0 : _j.id,
          constraintType: (_k = substitutionType.constraint) == null ? void 0 : _k.id
        };
      }
      let reverseMappedProperties = {};
      if (objectFlags & 1024 /* ReverseMapped */) {
        const reverseMappedType = type;
        reverseMappedProperties = {
          reverseMappedSourceType: (_l = reverseMappedType.source) == null ? void 0 : _l.id,
          reverseMappedMappedType: (_m = reverseMappedType.mappedType) == null ? void 0 : _m.id,
          reverseMappedConstraintType: (_n = reverseMappedType.constraintType) == null ? void 0 : _n.id
        };
      }
      let evolvingArrayProperties = {};
      if (objectFlags & 256 /* EvolvingArray */) {
        const evolvingArrayType = type;
        evolvingArrayProperties = {
          evolvingArrayElementType: evolvingArrayType.elementType.id,
          evolvingArrayFinalType: (_o = evolvingArrayType.finalArrayType) == null ? void 0 : _o.id
        };
      }
      let recursionToken;
      const recursionIdentity = type.checker.getRecursionIdentity(type);
      if (recursionIdentity) {
        recursionToken = recursionIdentityMap.get(recursionIdentity);
        if (!recursionToken) {
          recursionToken = recursionIdentityMap.size;
          recursionIdentityMap.set(recursionIdentity, recursionToken);
        }
      }
      const descriptor = {
        id: type.id,
        intrinsicName: type.intrinsicName,
        symbolName: (symbol == null ? void 0 : symbol.escapedName) && unescapeLeadingUnderscores(symbol.escapedName),
        recursionId: recursionToken,
        isTuple: objectFlags & 8 /* Tuple */ ? true : void 0,
        unionTypes: type.flags & 1048576 /* Union */ ? (_p = type.types) == null ? void 0 : _p.map((t) => t.id) : void 0,
        intersectionTypes: type.flags & 2097152 /* Intersection */ ? type.types.map((t) => t.id) : void 0,
        aliasTypeArguments: (_q = type.aliasTypeArguments) == null ? void 0 : _q.map((t) => t.id),
        keyofType: type.flags & 4194304 /* Index */ ? (_r = type.type) == null ? void 0 : _r.id : void 0,
        ...indexedAccessProperties,
        ...referenceProperties,
        ...conditionalProperties,
        ...substitutionProperties,
        ...reverseMappedProperties,
        ...evolvingArrayProperties,
        destructuringPattern: getLocation(type.pattern),
        firstDeclaration: getLocation((_s = symbol == null ? void 0 : symbol.declarations) == null ? void 0 : _s[0]),
        flags: Debug.formatTypeFlags(type.flags).split("|"),
        display
      };
      fs.writeSync(typesFd, JSON.stringify(descriptor));
      if (i < numTypes - 1) {
        fs.writeSync(typesFd, ",\n");
      }
    }
    fs.writeSync(typesFd, "]\n");
    fs.closeSync(typesFd);
    mark("endDumpTypes");
    measure("Dump types", "beginDumpTypes", "endDumpTypes");
  }
  function dumpLegend() {
    if (!legendPath) {
      return;
    }
    fs.writeFileSync(legendPath, JSON.stringify(legend));
  }
  tracingEnabled2.dumpLegend = dumpLegend;
})(tracingEnabled || (tracingEnabled = {}));
var startTracing = tracingEnabled.startTracing;
var dumpTracingLegend = tracingEnabled.dumpLegend;

// src/compiler/types.ts
var SyntaxKind = /* @__PURE__ */ ((SyntaxKind4) => {
  SyntaxKind4[SyntaxKind4["Unknown"] = 0] = "Unknown";
  SyntaxKind4[SyntaxKind4["EndOfFileToken"] = 1] = "EndOfFileToken";
  SyntaxKind4[SyntaxKind4["SingleLineCommentTrivia"] = 2] = "SingleLineCommentTrivia";
  SyntaxKind4[SyntaxKind4["MultiLineCommentTrivia"] = 3] = "MultiLineCommentTrivia";
  SyntaxKind4[SyntaxKind4["NewLineTrivia"] = 4] = "NewLineTrivia";
  SyntaxKind4[SyntaxKind4["WhitespaceTrivia"] = 5] = "WhitespaceTrivia";
  SyntaxKind4[SyntaxKind4["ShebangTrivia"] = 6] = "ShebangTrivia";
  SyntaxKind4[SyntaxKind4["ConflictMarkerTrivia"] = 7] = "ConflictMarkerTrivia";
  SyntaxKind4[SyntaxKind4["NonTextFileMarkerTrivia"] = 8] = "NonTextFileMarkerTrivia";
  SyntaxKind4[SyntaxKind4["NumericLiteral"] = 9] = "NumericLiteral";
  SyntaxKind4[SyntaxKind4["BigIntLiteral"] = 10] = "BigIntLiteral";
  SyntaxKind4[SyntaxKind4["StringLiteral"] = 11] = "StringLiteral";
  SyntaxKind4[SyntaxKind4["JsxText"] = 12] = "JsxText";
  SyntaxKind4[SyntaxKind4["JsxTextAllWhiteSpaces"] = 13] = "JsxTextAllWhiteSpaces";
  SyntaxKind4[SyntaxKind4["RegularExpressionLiteral"] = 14] = "RegularExpressionLiteral";
  SyntaxKind4[SyntaxKind4["NoSubstitutionTemplateLiteral"] = 15] = "NoSubstitutionTemplateLiteral";
  SyntaxKind4[SyntaxKind4["TemplateHead"] = 16] = "TemplateHead";
  SyntaxKind4[SyntaxKind4["TemplateMiddle"] = 17] = "TemplateMiddle";
  SyntaxKind4[SyntaxKind4["TemplateTail"] = 18] = "TemplateTail";
  SyntaxKind4[SyntaxKind4["OpenBraceToken"] = 19] = "OpenBraceToken";
  SyntaxKind4[SyntaxKind4["CloseBraceToken"] = 20] = "CloseBraceToken";
  SyntaxKind4[SyntaxKind4["OpenParenToken"] = 21] = "OpenParenToken";
  SyntaxKind4[SyntaxKind4["CloseParenToken"] = 22] = "CloseParenToken";
  SyntaxKind4[SyntaxKind4["OpenBracketToken"] = 23] = "OpenBracketToken";
  SyntaxKind4[SyntaxKind4["CloseBracketToken"] = 24] = "CloseBracketToken";
  SyntaxKind4[SyntaxKind4["DotToken"] = 25] = "DotToken";
  SyntaxKind4[SyntaxKind4["DotDotDotToken"] = 26] = "DotDotDotToken";
  SyntaxKind4[SyntaxKind4["SemicolonToken"] = 27] = "SemicolonToken";
  SyntaxKind4[SyntaxKind4["CommaToken"] = 28] = "CommaToken";
  SyntaxKind4[SyntaxKind4["QuestionDotToken"] = 29] = "QuestionDotToken";
  SyntaxKind4[SyntaxKind4["LessThanToken"] = 30] = "LessThanToken";
  SyntaxKind4[SyntaxKind4["LessThanSlashToken"] = 31] = "LessThanSlashToken";
  SyntaxKind4[SyntaxKind4["GreaterThanToken"] = 32] = "GreaterThanToken";
  SyntaxKind4[SyntaxKind4["LessThanEqualsToken"] = 33] = "LessThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanEqualsToken"] = 34] = "GreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsEqualsToken"] = 35] = "EqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["ExclamationEqualsToken"] = 36] = "ExclamationEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsEqualsEqualsToken"] = 37] = "EqualsEqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["ExclamationEqualsEqualsToken"] = 38] = "ExclamationEqualsEqualsToken";
  SyntaxKind4[SyntaxKind4["EqualsGreaterThanToken"] = 39] = "EqualsGreaterThanToken";
  SyntaxKind4[SyntaxKind4["PlusToken"] = 40] = "PlusToken";
  SyntaxKind4[SyntaxKind4["MinusToken"] = 41] = "MinusToken";
  SyntaxKind4[SyntaxKind4["AsteriskToken"] = 42] = "AsteriskToken";
  SyntaxKind4[SyntaxKind4["AsteriskAsteriskToken"] = 43] = "AsteriskAsteriskToken";
  SyntaxKind4[SyntaxKind4["SlashToken"] = 44] = "SlashToken";
  SyntaxKind4[SyntaxKind4["PercentToken"] = 45] = "PercentToken";
  SyntaxKind4[SyntaxKind4["PlusPlusToken"] = 46] = "PlusPlusToken";
  SyntaxKind4[SyntaxKind4["MinusMinusToken"] = 47] = "MinusMinusToken";
  SyntaxKind4[SyntaxKind4["LessThanLessThanToken"] = 48] = "LessThanLessThanToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanToken"] = 49] = "GreaterThanGreaterThanToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanGreaterThanToken"] = 50] = "GreaterThanGreaterThanGreaterThanToken";
  SyntaxKind4[SyntaxKind4["AmpersandToken"] = 51] = "AmpersandToken";
  SyntaxKind4[SyntaxKind4["BarToken"] = 52] = "BarToken";
  SyntaxKind4[SyntaxKind4["CaretToken"] = 53] = "CaretToken";
  SyntaxKind4[SyntaxKind4["ExclamationToken"] = 54] = "ExclamationToken";
  SyntaxKind4[SyntaxKind4["TildeToken"] = 55] = "TildeToken";
  SyntaxKind4[SyntaxKind4["AmpersandAmpersandToken"] = 56] = "AmpersandAmpersandToken";
  SyntaxKind4[SyntaxKind4["BarBarToken"] = 57] = "BarBarToken";
  SyntaxKind4[SyntaxKind4["QuestionToken"] = 58] = "QuestionToken";
  SyntaxKind4[SyntaxKind4["ColonToken"] = 59] = "ColonToken";
  SyntaxKind4[SyntaxKind4["AtToken"] = 60] = "AtToken";
  SyntaxKind4[SyntaxKind4["QuestionQuestionToken"] = 61] = "QuestionQuestionToken";
  SyntaxKind4[SyntaxKind4["BacktickToken"] = 62] = "BacktickToken";
  SyntaxKind4[SyntaxKind4["HashToken"] = 63] = "HashToken";
  SyntaxKind4[SyntaxKind4["EqualsToken"] = 64] = "EqualsToken";
  SyntaxKind4[SyntaxKind4["PlusEqualsToken"] = 65] = "PlusEqualsToken";
  SyntaxKind4[SyntaxKind4["MinusEqualsToken"] = 66] = "MinusEqualsToken";
  SyntaxKind4[SyntaxKind4["AsteriskEqualsToken"] = 67] = "AsteriskEqualsToken";
  SyntaxKind4[SyntaxKind4["AsteriskAsteriskEqualsToken"] = 68] = "AsteriskAsteriskEqualsToken";
  SyntaxKind4[SyntaxKind4["SlashEqualsToken"] = 69] = "SlashEqualsToken";
  SyntaxKind4[SyntaxKind4["PercentEqualsToken"] = 70] = "PercentEqualsToken";
  SyntaxKind4[SyntaxKind4["LessThanLessThanEqualsToken"] = 71] = "LessThanLessThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanEqualsToken"] = 72] = "GreaterThanGreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["GreaterThanGreaterThanGreaterThanEqualsToken"] = 73] = "GreaterThanGreaterThanGreaterThanEqualsToken";
  SyntaxKind4[SyntaxKind4["AmpersandEqualsToken"] = 74] = "AmpersandEqualsToken";
  SyntaxKind4[SyntaxKind4["BarEqualsToken"] = 75] = "BarEqualsToken";
  SyntaxKind4[SyntaxKind4["BarBarEqualsToken"] = 76] = "BarBarEqualsToken";
  SyntaxKind4[SyntaxKind4["AmpersandAmpersandEqualsToken"] = 77] = "AmpersandAmpersandEqualsToken";
  SyntaxKind4[SyntaxKind4["QuestionQuestionEqualsToken"] = 78] = "QuestionQuestionEqualsToken";
  SyntaxKind4[SyntaxKind4["CaretEqualsToken"] = 79] = "CaretEqualsToken";
  SyntaxKind4[SyntaxKind4["Identifier"] = 80] = "Identifier";
  SyntaxKind4[SyntaxKind4["PrivateIdentifier"] = 81] = "PrivateIdentifier";
  SyntaxKind4[SyntaxKind4["JSDocCommentTextToken"] = 82] = "JSDocCommentTextToken";
  SyntaxKind4[SyntaxKind4["BreakKeyword"] = 83] = "BreakKeyword";
  SyntaxKind4[SyntaxKind4["CaseKeyword"] = 84] = "CaseKeyword";
  SyntaxKind4[SyntaxKind4["CatchKeyword"] = 85] = "CatchKeyword";
  SyntaxKind4[SyntaxKind4["ClassKeyword"] = 86] = "ClassKeyword";
  SyntaxKind4[SyntaxKind4["ConstKeyword"] = 87] = "ConstKeyword";
  SyntaxKind4[SyntaxKind4["ContinueKeyword"] = 88] = "ContinueKeyword";
  SyntaxKind4[SyntaxKind4["DebuggerKeyword"] = 89] = "DebuggerKeyword";
  SyntaxKind4[SyntaxKind4["DefaultKeyword"] = 90] = "DefaultKeyword";
  SyntaxKind4[SyntaxKind4["DeleteKeyword"] = 91] = "DeleteKeyword";
  SyntaxKind4[SyntaxKind4["DoKeyword"] = 92] = "DoKeyword";
  SyntaxKind4[SyntaxKind4["ElseKeyword"] = 93] = "ElseKeyword";
  SyntaxKind4[SyntaxKind4["EnumKeyword"] = 94] = "EnumKeyword";
  SyntaxKind4[SyntaxKind4["ExportKeyword"] = 95] = "ExportKeyword";
  SyntaxKind4[SyntaxKind4["ExtendsKeyword"] = 96] = "ExtendsKeyword";
  SyntaxKind4[SyntaxKind4["FalseKeyword"] = 97] = "FalseKeyword";
  SyntaxKind4[SyntaxKind4["FinallyKeyword"] = 98] = "FinallyKeyword";
  SyntaxKind4[SyntaxKind4["ForKeyword"] = 99] = "ForKeyword";
  SyntaxKind4[SyntaxKind4["FunctionKeyword"] = 100] = "FunctionKeyword";
  SyntaxKind4[SyntaxKind4["IfKeyword"] = 101] = "IfKeyword";
  SyntaxKind4[SyntaxKind4["ImportKeyword"] = 102] = "ImportKeyword";
  SyntaxKind4[SyntaxKind4["InKeyword"] = 103] = "InKeyword";
  SyntaxKind4[SyntaxKind4["InstanceOfKeyword"] = 104] = "InstanceOfKeyword";
  SyntaxKind4[SyntaxKind4["NewKeyword"] = 105] = "NewKeyword";
  SyntaxKind4[SyntaxKind4["NullKeyword"] = 106] = "NullKeyword";
  SyntaxKind4[SyntaxKind4["ReturnKeyword"] = 107] = "ReturnKeyword";
  SyntaxKind4[SyntaxKind4["SuperKeyword"] = 108] = "SuperKeyword";
  SyntaxKind4[SyntaxKind4["SwitchKeyword"] = 109] = "SwitchKeyword";
  SyntaxKind4[SyntaxKind4["ThisKeyword"] = 110] = "ThisKeyword";
  SyntaxKind4[SyntaxKind4["ThrowKeyword"] = 111] = "ThrowKeyword";
  SyntaxKind4[SyntaxKind4["TrueKeyword"] = 112] = "TrueKeyword";
  SyntaxKind4[SyntaxKind4["TryKeyword"] = 113] = "TryKeyword";
  SyntaxKind4[SyntaxKind4["TypeOfKeyword"] = 114] = "TypeOfKeyword";
  SyntaxKind4[SyntaxKind4["VarKeyword"] = 115] = "VarKeyword";
  SyntaxKind4[SyntaxKind4["VoidKeyword"] = 116] = "VoidKeyword";
  SyntaxKind4[SyntaxKind4["WhileKeyword"] = 117] = "WhileKeyword";
  SyntaxKind4[SyntaxKind4["WithKeyword"] = 118] = "WithKeyword";
  SyntaxKind4[SyntaxKind4["ImplementsKeyword"] = 119] = "ImplementsKeyword";
  SyntaxKind4[SyntaxKind4["InterfaceKeyword"] = 120] = "InterfaceKeyword";
  SyntaxKind4[SyntaxKind4["LetKeyword"] = 121] = "LetKeyword";
  SyntaxKind4[SyntaxKind4["PackageKeyword"] = 122] = "PackageKeyword";
  SyntaxKind4[SyntaxKind4["PrivateKeyword"] = 123] = "PrivateKeyword";
  SyntaxKind4[SyntaxKind4["ProtectedKeyword"] = 124] = "ProtectedKeyword";
  SyntaxKind4[SyntaxKind4["PublicKeyword"] = 125] = "PublicKeyword";
  SyntaxKind4[SyntaxKind4["StaticKeyword"] = 126] = "StaticKeyword";
  SyntaxKind4[SyntaxKind4["YieldKeyword"] = 127] = "YieldKeyword";
  SyntaxKind4[SyntaxKind4["AbstractKeyword"] = 128] = "AbstractKeyword";
  SyntaxKind4[SyntaxKind4["AccessorKeyword"] = 129] = "AccessorKeyword";
  SyntaxKind4[SyntaxKind4["AsKeyword"] = 130] = "AsKeyword";
  SyntaxKind4[SyntaxKind4["AssertsKeyword"] = 131] = "AssertsKeyword";
  SyntaxKind4[SyntaxKind4["AssertKeyword"] = 132] = "AssertKeyword";
  SyntaxKind4[SyntaxKind4["AnyKeyword"] = 133] = "AnyKeyword";
  SyntaxKind4[SyntaxKind4["AsyncKeyword"] = 134] = "AsyncKeyword";
  SyntaxKind4[SyntaxKind4["AwaitKeyword"] = 135] = "AwaitKeyword";
  SyntaxKind4[SyntaxKind4["BooleanKeyword"] = 136] = "BooleanKeyword";
  SyntaxKind4[SyntaxKind4["ConstructorKeyword"] = 137] = "ConstructorKeyword";
  SyntaxKind4[SyntaxKind4["DeclareKeyword"] = 138] = "DeclareKeyword";
  SyntaxKind4[SyntaxKind4["GetKeyword"] = 139] = "GetKeyword";
  SyntaxKind4[SyntaxKind4["InferKeyword"] = 140] = "InferKeyword";
  SyntaxKind4[SyntaxKind4["IntrinsicKeyword"] = 141] = "IntrinsicKeyword";
  SyntaxKind4[SyntaxKind4["IsKeyword"] = 142] = "IsKeyword";
  SyntaxKind4[SyntaxKind4["KeyOfKeyword"] = 143] = "KeyOfKeyword";
  SyntaxKind4[SyntaxKind4["ModuleKeyword"] = 144] = "ModuleKeyword";
  SyntaxKind4[SyntaxKind4["NamespaceKeyword"] = 145] = "NamespaceKeyword";
  SyntaxKind4[SyntaxKind4["NeverKeyword"] = 146] = "NeverKeyword";
  SyntaxKind4[SyntaxKind4["OutKeyword"] = 147] = "OutKeyword";
  SyntaxKind4[SyntaxKind4["ReadonlyKeyword"] = 148] = "ReadonlyKeyword";
  SyntaxKind4[SyntaxKind4["RequireKeyword"] = 149] = "RequireKeyword";
  SyntaxKind4[SyntaxKind4["NumberKeyword"] = 150] = "NumberKeyword";
  SyntaxKind4[SyntaxKind4["ObjectKeyword"] = 151] = "ObjectKeyword";
  SyntaxKind4[SyntaxKind4["SatisfiesKeyword"] = 152] = "SatisfiesKeyword";
  SyntaxKind4[SyntaxKind4["SetKeyword"] = 153] = "SetKeyword";
  SyntaxKind4[SyntaxKind4["StringKeyword"] = 154] = "StringKeyword";
  SyntaxKind4[SyntaxKind4["SymbolKeyword"] = 155] = "SymbolKeyword";
  SyntaxKind4[SyntaxKind4["TypeKeyword"] = 156] = "TypeKeyword";
  SyntaxKind4[SyntaxKind4["UndefinedKeyword"] = 157] = "UndefinedKeyword";
  SyntaxKind4[SyntaxKind4["UniqueKeyword"] = 158] = "UniqueKeyword";
  SyntaxKind4[SyntaxKind4["UnknownKeyword"] = 159] = "UnknownKeyword";
  SyntaxKind4[SyntaxKind4["UsingKeyword"] = 160] = "UsingKeyword";
  SyntaxKind4[SyntaxKind4["FromKeyword"] = 161] = "FromKeyword";
  SyntaxKind4[SyntaxKind4["GlobalKeyword"] = 162] = "GlobalKeyword";
  SyntaxKind4[SyntaxKind4["BigIntKeyword"] = 163] = "BigIntKeyword";
  SyntaxKind4[SyntaxKind4["OverrideKeyword"] = 164] = "OverrideKeyword";
  SyntaxKind4[SyntaxKind4["OfKeyword"] = 165] = "OfKeyword";
  SyntaxKind4[SyntaxKind4["QualifiedName"] = 166] = "QualifiedName";
  SyntaxKind4[SyntaxKind4["ComputedPropertyName"] = 167] = "ComputedPropertyName";
  SyntaxKind4[SyntaxKind4["TypeParameter"] = 168] = "TypeParameter";
  SyntaxKind4[SyntaxKind4["Parameter"] = 169] = "Parameter";
  SyntaxKind4[SyntaxKind4["Decorator"] = 170] = "Decorator";
  SyntaxKind4[SyntaxKind4["PropertySignature"] = 171] = "PropertySignature";
  SyntaxKind4[SyntaxKind4["PropertyDeclaration"] = 172] = "PropertyDeclaration";
  SyntaxKind4[SyntaxKind4["MethodSignature"] = 173] = "MethodSignature";
  SyntaxKind4[SyntaxKind4["MethodDeclaration"] = 174] = "MethodDeclaration";
  SyntaxKind4[SyntaxKind4["ClassStaticBlockDeclaration"] = 175] = "ClassStaticBlockDeclaration";
  SyntaxKind4[SyntaxKind4["Constructor"] = 176] = "Constructor";
  SyntaxKind4[SyntaxKind4["GetAccessor"] = 177] = "GetAccessor";
  SyntaxKind4[SyntaxKind4["SetAccessor"] = 178] = "SetAccessor";
  SyntaxKind4[SyntaxKind4["CallSignature"] = 179] = "CallSignature";
  SyntaxKind4[SyntaxKind4["ConstructSignature"] = 180] = "ConstructSignature";
  SyntaxKind4[SyntaxKind4["IndexSignature"] = 181] = "IndexSignature";
  SyntaxKind4[SyntaxKind4["TypePredicate"] = 182] = "TypePredicate";
  SyntaxKind4[SyntaxKind4["TypeReference"] = 183] = "TypeReference";
  SyntaxKind4[SyntaxKind4["FunctionType"] = 184] = "FunctionType";
  SyntaxKind4[SyntaxKind4["ConstructorType"] = 185] = "ConstructorType";
  SyntaxKind4[SyntaxKind4["TypeQuery"] = 186] = "TypeQuery";
  SyntaxKind4[SyntaxKind4["TypeLiteral"] = 187] = "TypeLiteral";
  SyntaxKind4[SyntaxKind4["ArrayType"] = 188] = "ArrayType";
  SyntaxKind4[SyntaxKind4["TupleType"] = 189] = "TupleType";
  SyntaxKind4[SyntaxKind4["OptionalType"] = 190] = "OptionalType";
  SyntaxKind4[SyntaxKind4["RestType"] = 191] = "RestType";
  SyntaxKind4[SyntaxKind4["UnionType"] = 192] = "UnionType";
  SyntaxKind4[SyntaxKind4["IntersectionType"] = 193] = "IntersectionType";
  SyntaxKind4[SyntaxKind4["ConditionalType"] = 194] = "ConditionalType";
  SyntaxKind4[SyntaxKind4["InferType"] = 195] = "InferType";
  SyntaxKind4[SyntaxKind4["ParenthesizedType"] = 196] = "ParenthesizedType";
  SyntaxKind4[SyntaxKind4["ThisType"] = 197] = "ThisType";
  SyntaxKind4[SyntaxKind4["TypeOperator"] = 198] = "TypeOperator";
  SyntaxKind4[SyntaxKind4["IndexedAccessType"] = 199] = "IndexedAccessType";
  SyntaxKind4[SyntaxKind4["MappedType"] = 200] = "MappedType";
  SyntaxKind4[SyntaxKind4["LiteralType"] = 201] = "LiteralType";
  SyntaxKind4[SyntaxKind4["NamedTupleMember"] = 202] = "NamedTupleMember";
  SyntaxKind4[SyntaxKind4["TemplateLiteralType"] = 203] = "TemplateLiteralType";
  SyntaxKind4[SyntaxKind4["TemplateLiteralTypeSpan"] = 204] = "TemplateLiteralTypeSpan";
  SyntaxKind4[SyntaxKind4["ImportType"] = 205] = "ImportType";
  SyntaxKind4[SyntaxKind4["ObjectBindingPattern"] = 206] = "ObjectBindingPattern";
  SyntaxKind4[SyntaxKind4["ArrayBindingPattern"] = 207] = "ArrayBindingPattern";
  SyntaxKind4[SyntaxKind4["BindingElement"] = 208] = "BindingElement";
  SyntaxKind4[SyntaxKind4["ArrayLiteralExpression"] = 209] = "ArrayLiteralExpression";
  SyntaxKind4[SyntaxKind4["ObjectLiteralExpression"] = 210] = "ObjectLiteralExpression";
  SyntaxKind4[SyntaxKind4["PropertyAccessExpression"] = 211] = "PropertyAccessExpression";
  SyntaxKind4[SyntaxKind4["ElementAccessExpression"] = 212] = "ElementAccessExpression";
  SyntaxKind4[SyntaxKind4["CallExpression"] = 213] = "CallExpression";
  SyntaxKind4[SyntaxKind4["NewExpression"] = 214] = "NewExpression";
  SyntaxKind4[SyntaxKind4["TaggedTemplateExpression"] = 215] = "TaggedTemplateExpression";
  SyntaxKind4[SyntaxKind4["TypeAssertionExpression"] = 216] = "TypeAssertionExpression";
  SyntaxKind4[SyntaxKind4["ParenthesizedExpression"] = 217] = "ParenthesizedExpression";
  SyntaxKind4[SyntaxKind4["FunctionExpression"] = 218] = "FunctionExpression";
  SyntaxKind4[SyntaxKind4["ArrowFunction"] = 219] = "ArrowFunction";
  SyntaxKind4[SyntaxKind4["DeleteExpression"] = 220] = "DeleteExpression";
  SyntaxKind4[SyntaxKind4["TypeOfExpression"] = 221] = "TypeOfExpression";
  SyntaxKind4[SyntaxKind4["VoidExpression"] = 222] = "VoidExpression";
  SyntaxKind4[SyntaxKind4["AwaitExpression"] = 223] = "AwaitExpression";
  SyntaxKind4[SyntaxKind4["PrefixUnaryExpression"] = 224] = "PrefixUnaryExpression";
  SyntaxKind4[SyntaxKind4["PostfixUnaryExpression"] = 225] = "PostfixUnaryExpression";
  SyntaxKind4[SyntaxKind4["BinaryExpression"] = 226] = "BinaryExpression";
  SyntaxKind4[SyntaxKind4["ConditionalExpression"] = 227] = "ConditionalExpression";
  SyntaxKind4[SyntaxKind4["TemplateExpression"] = 228] = "TemplateExpression";
  SyntaxKind4[SyntaxKind4["YieldExpression"] = 229] = "YieldExpression";
  SyntaxKind4[SyntaxKind4["SpreadElement"] = 230] = "SpreadElement";
  SyntaxKind4[SyntaxKind4["ClassExpression"] = 231] = "ClassExpression";
  SyntaxKind4[SyntaxKind4["OmittedExpression"] = 232] = "OmittedExpression";
  SyntaxKind4[SyntaxKind4["ExpressionWithTypeArguments"] = 233] = "ExpressionWithTypeArguments";
  SyntaxKind4[SyntaxKind4["AsExpression"] = 234] = "AsExpression";
  SyntaxKind4[SyntaxKind4["NonNullExpression"] = 235] = "NonNullExpression";
  SyntaxKind4[SyntaxKind4["MetaProperty"] = 236] = "MetaProperty";
  SyntaxKind4[SyntaxKind4["SyntheticExpression"] = 237] = "SyntheticExpression";
  SyntaxKind4[SyntaxKind4["SatisfiesExpression"] = 238] = "SatisfiesExpression";
  SyntaxKind4[SyntaxKind4["TemplateSpan"] = 239] = "TemplateSpan";
  SyntaxKind4[SyntaxKind4["SemicolonClassElement"] = 240] = "SemicolonClassElement";
  SyntaxKind4[SyntaxKind4["Block"] = 241] = "Block";
  SyntaxKind4[SyntaxKind4["EmptyStatement"] = 242] = "EmptyStatement";
  SyntaxKind4[SyntaxKind4["VariableStatement"] = 243] = "VariableStatement";
  SyntaxKind4[SyntaxKind4["ExpressionStatement"] = 244] = "ExpressionStatement";
  SyntaxKind4[SyntaxKind4["IfStatement"] = 245] = "IfStatement";
  SyntaxKind4[SyntaxKind4["DoStatement"] = 246] = "DoStatement";
  SyntaxKind4[SyntaxKind4["WhileStatement"] = 247] = "WhileStatement";
  SyntaxKind4[SyntaxKind4["ForStatement"] = 248] = "ForStatement";
  SyntaxKind4[SyntaxKind4["ForInStatement"] = 249] = "ForInStatement";
  SyntaxKind4[SyntaxKind4["ForOfStatement"] = 250] = "ForOfStatement";
  SyntaxKind4[SyntaxKind4["ContinueStatement"] = 251] = "ContinueStatement";
  SyntaxKind4[SyntaxKind4["BreakStatement"] = 252] = "BreakStatement";
  SyntaxKind4[SyntaxKind4["ReturnStatement"] = 253] = "ReturnStatement";
  SyntaxKind4[SyntaxKind4["WithStatement"] = 254] = "WithStatement";
  SyntaxKind4[SyntaxKind4["SwitchStatement"] = 255] = "SwitchStatement";
  SyntaxKind4[SyntaxKind4["LabeledStatement"] = 256] = "LabeledStatement";
  SyntaxKind4[SyntaxKind4["ThrowStatement"] = 257] = "ThrowStatement";
  SyntaxKind4[SyntaxKind4["TryStatement"] = 258] = "TryStatement";
  SyntaxKind4[SyntaxKind4["DebuggerStatement"] = 259] = "DebuggerStatement";
  SyntaxKind4[SyntaxKind4["VariableDeclaration"] = 260] = "VariableDeclaration";
  SyntaxKind4[SyntaxKind4["VariableDeclarationList"] = 261] = "VariableDeclarationList";
  SyntaxKind4[SyntaxKind4["FunctionDeclaration"] = 262] = "FunctionDeclaration";
  SyntaxKind4[SyntaxKind4["ClassDeclaration"] = 263] = "ClassDeclaration";
  SyntaxKind4[SyntaxKind4["InterfaceDeclaration"] = 264] = "InterfaceDeclaration";
  SyntaxKind4[SyntaxKind4["TypeAliasDeclaration"] = 265] = "TypeAliasDeclaration";
  SyntaxKind4[SyntaxKind4["EnumDeclaration"] = 266] = "EnumDeclaration";
  SyntaxKind4[SyntaxKind4["ModuleDeclaration"] = 267] = "ModuleDeclaration";
  SyntaxKind4[SyntaxKind4["ModuleBlock"] = 268] = "ModuleBlock";
  SyntaxKind4[SyntaxKind4["CaseBlock"] = 269] = "CaseBlock";
  SyntaxKind4[SyntaxKind4["NamespaceExportDeclaration"] = 270] = "NamespaceExportDeclaration";
  SyntaxKind4[SyntaxKind4["ImportEqualsDeclaration"] = 271] = "ImportEqualsDeclaration";
  SyntaxKind4[SyntaxKind4["ImportDeclaration"] = 272] = "ImportDeclaration";
  SyntaxKind4[SyntaxKind4["ImportClause"] = 273] = "ImportClause";
  SyntaxKind4[SyntaxKind4["NamespaceImport"] = 274] = "NamespaceImport";
  SyntaxKind4[SyntaxKind4["NamedImports"] = 275] = "NamedImports";
  SyntaxKind4[SyntaxKind4["ImportSpecifier"] = 276] = "ImportSpecifier";
  SyntaxKind4[SyntaxKind4["ExportAssignment"] = 277] = "ExportAssignment";
  SyntaxKind4[SyntaxKind4["ExportDeclaration"] = 278] = "ExportDeclaration";
  SyntaxKind4[SyntaxKind4["NamedExports"] = 279] = "NamedExports";
  SyntaxKind4[SyntaxKind4["NamespaceExport"] = 280] = "NamespaceExport";
  SyntaxKind4[SyntaxKind4["ExportSpecifier"] = 281] = "ExportSpecifier";
  SyntaxKind4[SyntaxKind4["MissingDeclaration"] = 282] = "MissingDeclaration";
  SyntaxKind4[SyntaxKind4["ExternalModuleReference"] = 283] = "ExternalModuleReference";
  SyntaxKind4[SyntaxKind4["JsxElement"] = 284] = "JsxElement";
  SyntaxKind4[SyntaxKind4["JsxSelfClosingElement"] = 285] = "JsxSelfClosingElement";
  SyntaxKind4[SyntaxKind4["JsxOpeningElement"] = 286] = "JsxOpeningElement";
  SyntaxKind4[SyntaxKind4["JsxClosingElement"] = 287] = "JsxClosingElement";
  SyntaxKind4[SyntaxKind4["JsxFragment"] = 288] = "JsxFragment";
  SyntaxKind4[SyntaxKind4["JsxOpeningFragment"] = 289] = "JsxOpeningFragment";
  SyntaxKind4[SyntaxKind4["JsxClosingFragment"] = 290] = "JsxClosingFragment";
  SyntaxKind4[SyntaxKind4["JsxAttribute"] = 291] = "JsxAttribute";
  SyntaxKind4[SyntaxKind4["JsxAttributes"] = 292] = "JsxAttributes";
  SyntaxKind4[SyntaxKind4["JsxSpreadAttribute"] = 293] = "JsxSpreadAttribute";
  SyntaxKind4[SyntaxKind4["JsxExpression"] = 294] = "JsxExpression";
  SyntaxKind4[SyntaxKind4["JsxNamespacedName"] = 295] = "JsxNamespacedName";
  SyntaxKind4[SyntaxKind4["CaseClause"] = 296] = "CaseClause";
  SyntaxKind4[SyntaxKind4["DefaultClause"] = 297] = "DefaultClause";
  SyntaxKind4[SyntaxKind4["HeritageClause"] = 298] = "HeritageClause";
  SyntaxKind4[SyntaxKind4["CatchClause"] = 299] = "CatchClause";
  SyntaxKind4[SyntaxKind4["ImportAttributes"] = 300] = "ImportAttributes";
  SyntaxKind4[SyntaxKind4["ImportAttribute"] = 301] = "ImportAttribute";
  SyntaxKind4[SyntaxKind4["AssertClause"] = 300 /* ImportAttributes */] = "AssertClause";
  SyntaxKind4[SyntaxKind4["AssertEntry"] = 301 /* ImportAttribute */] = "AssertEntry";
  SyntaxKind4[SyntaxKind4["ImportTypeAssertionContainer"] = 302] = "ImportTypeAssertionContainer";
  SyntaxKind4[SyntaxKind4["PropertyAssignment"] = 303] = "PropertyAssignment";
  SyntaxKind4[SyntaxKind4["ShorthandPropertyAssignment"] = 304] = "ShorthandPropertyAssignment";
  SyntaxKind4[SyntaxKind4["SpreadAssignment"] = 305] = "SpreadAssignment";
  SyntaxKind4[SyntaxKind4["EnumMember"] = 306] = "EnumMember";
  SyntaxKind4[SyntaxKind4["SourceFile"] = 307] = "SourceFile";
  SyntaxKind4[SyntaxKind4["Bundle"] = 308] = "Bundle";
  SyntaxKind4[SyntaxKind4["JSDocTypeExpression"] = 309] = "JSDocTypeExpression";
  SyntaxKind4[SyntaxKind4["JSDocNameReference"] = 310] = "JSDocNameReference";
  SyntaxKind4[SyntaxKind4["JSDocMemberName"] = 311] = "JSDocMemberName";
  SyntaxKind4[SyntaxKind4["JSDocAllType"] = 312] = "JSDocAllType";
  SyntaxKind4[SyntaxKind4["JSDocUnknownType"] = 313] = "JSDocUnknownType";
  SyntaxKind4[SyntaxKind4["JSDocNullableType"] = 314] = "JSDocNullableType";
  SyntaxKind4[SyntaxKind4["JSDocNonNullableType"] = 315] = "JSDocNonNullableType";
  SyntaxKind4[SyntaxKind4["JSDocOptionalType"] = 316] = "JSDocOptionalType";
  SyntaxKind4[SyntaxKind4["JSDocFunctionType"] = 317] = "JSDocFunctionType";
  SyntaxKind4[SyntaxKind4["JSDocVariadicType"] = 318] = "JSDocVariadicType";
  SyntaxKind4[SyntaxKind4["JSDocNamepathType"] = 319] = "JSDocNamepathType";
  SyntaxKind4[SyntaxKind4["JSDoc"] = 320] = "JSDoc";
  SyntaxKind4[SyntaxKind4["JSDocComment"] = 320 /* JSDoc */] = "JSDocComment";
  SyntaxKind4[SyntaxKind4["JSDocText"] = 321] = "JSDocText";
  SyntaxKind4[SyntaxKind4["JSDocTypeLiteral"] = 322] = "JSDocTypeLiteral";
  SyntaxKind4[SyntaxKind4["JSDocSignature"] = 323] = "JSDocSignature";
  SyntaxKind4[SyntaxKind4["JSDocLink"] = 324] = "JSDocLink";
  SyntaxKind4[SyntaxKind4["JSDocLinkCode"] = 325] = "JSDocLinkCode";
  SyntaxKind4[SyntaxKind4["JSDocLinkPlain"] = 326] = "JSDocLinkPlain";
  SyntaxKind4[SyntaxKind4["JSDocTag"] = 327] = "JSDocTag";
  SyntaxKind4[SyntaxKind4["JSDocAugmentsTag"] = 328] = "JSDocAugmentsTag";
  SyntaxKind4[SyntaxKind4["JSDocImplementsTag"] = 329] = "JSDocImplementsTag";
  SyntaxKind4[SyntaxKind4["JSDocAuthorTag"] = 330] = "JSDocAuthorTag";
  SyntaxKind4[SyntaxKind4["JSDocDeprecatedTag"] = 331] = "JSDocDeprecatedTag";
  SyntaxKind4[SyntaxKind4["JSDocClassTag"] = 332] = "JSDocClassTag";
  SyntaxKind4[SyntaxKind4["JSDocPublicTag"] = 333] = "JSDocPublicTag";
  SyntaxKind4[SyntaxKind4["JSDocPrivateTag"] = 334] = "JSDocPrivateTag";
  SyntaxKind4[SyntaxKind4["JSDocProtectedTag"] = 335] = "JSDocProtectedTag";
  SyntaxKind4[SyntaxKind4["JSDocReadonlyTag"] = 336] = "JSDocReadonlyTag";
  SyntaxKind4[SyntaxKind4["JSDocOverrideTag"] = 337] = "JSDocOverrideTag";
  SyntaxKind4[SyntaxKind4["JSDocCallbackTag"] = 338] = "JSDocCallbackTag";
  SyntaxKind4[SyntaxKind4["JSDocOverloadTag"] = 339] = "JSDocOverloadTag";
  SyntaxKind4[SyntaxKind4["JSDocEnumTag"] = 340] = "JSDocEnumTag";
  SyntaxKind4[SyntaxKind4["JSDocParameterTag"] = 341] = "JSDocParameterTag";
  SyntaxKind4[SyntaxKind4["JSDocReturnTag"] = 342] = "JSDocReturnTag";
  SyntaxKind4[SyntaxKind4["JSDocThisTag"] = 343] = "JSDocThisTag";
  SyntaxKind4[SyntaxKind4["JSDocTypeTag"] = 344] = "JSDocTypeTag";
  SyntaxKind4[SyntaxKind4["JSDocTemplateTag"] = 345] = "JSDocTemplateTag";
  SyntaxKind4[SyntaxKind4["JSDocTypedefTag"] = 346] = "JSDocTypedefTag";
  SyntaxKind4[SyntaxKind4["JSDocSeeTag"] = 347] = "JSDocSeeTag";
  SyntaxKind4[SyntaxKind4["JSDocPropertyTag"] = 348] = "JSDocPropertyTag";
  SyntaxKind4[SyntaxKind4["JSDocThrowsTag"] = 349] = "JSDocThrowsTag";
  SyntaxKind4[SyntaxKind4["JSDocSatisfiesTag"] = 350] = "JSDocSatisfiesTag";
  SyntaxKind4[SyntaxKind4["JSDocImportTag"] = 351] = "JSDocImportTag";
  SyntaxKind4[SyntaxKind4["SyntaxList"] = 352] = "SyntaxList";
  SyntaxKind4[SyntaxKind4["NotEmittedStatement"] = 353] = "NotEmittedStatement";
  SyntaxKind4[SyntaxKind4["NotEmittedTypeElement"] = 354] = "NotEmittedTypeElement";
  SyntaxKind4[SyntaxKind4["PartiallyEmittedExpression"] = 355] = "PartiallyEmittedExpression";
  SyntaxKind4[SyntaxKind4["CommaListExpression"] = 356] = "CommaListExpression";
  SyntaxKind4[SyntaxKind4["SyntheticReferenceExpression"] = 357] = "SyntheticReferenceExpression";
  SyntaxKind4[SyntaxKind4["Count"] = 358] = "Count";
  SyntaxKind4[SyntaxKind4["FirstAssignment"] = 64 /* EqualsToken */] = "FirstAssignment";
  SyntaxKind4[SyntaxKind4["LastAssignment"] = 79 /* CaretEqualsToken */] = "LastAssignment";
  SyntaxKind4[SyntaxKind4["FirstCompoundAssignment"] = 65 /* PlusEqualsToken */] = "FirstCompoundAssignment";
  SyntaxKind4[SyntaxKind4["LastCompoundAssignment"] = 79 /* CaretEqualsToken */] = "LastCompoundAssignment";
  SyntaxKind4[SyntaxKind4["FirstReservedWord"] = 83 /* BreakKeyword */] = "FirstReservedWord";
  SyntaxKind4[SyntaxKind4["LastReservedWord"] = 118 /* WithKeyword */] = "LastReservedWord";
  SyntaxKind4[SyntaxKind4["FirstKeyword"] = 83 /* BreakKeyword */] = "FirstKeyword";
  SyntaxKind4[SyntaxKind4["LastKeyword"] = 165 /* OfKeyword */] = "LastKeyword";
  SyntaxKind4[SyntaxKind4["FirstFutureReservedWord"] = 119 /* ImplementsKeyword */] = "FirstFutureReservedWord";
  SyntaxKind4[SyntaxKind4["LastFutureReservedWord"] = 127 /* YieldKeyword */] = "LastFutureReservedWord";
  SyntaxKind4[SyntaxKind4["FirstTypeNode"] = 182 /* TypePredicate */] = "FirstTypeNode";
  SyntaxKind4[SyntaxKind4["LastTypeNode"] = 205 /* ImportType */] = "LastTypeNode";
  SyntaxKind4[SyntaxKind4["FirstPunctuation"] = 19 /* OpenBraceToken */] = "FirstPunctuation";
  SyntaxKind4[SyntaxKind4["LastPunctuation"] = 79 /* CaretEqualsToken */] = "LastPunctuation";
  SyntaxKind4[SyntaxKind4["FirstToken"] = 0 /* Unknown */] = "FirstToken";
  SyntaxKind4[SyntaxKind4["LastToken"] = 165 /* LastKeyword */] = "LastToken";
  SyntaxKind4[SyntaxKind4["FirstTriviaToken"] = 2 /* SingleLineCommentTrivia */] = "FirstTriviaToken";
  SyntaxKind4[SyntaxKind4["LastTriviaToken"] = 7 /* ConflictMarkerTrivia */] = "LastTriviaToken";
  SyntaxKind4[SyntaxKind4["FirstLiteralToken"] = 9 /* NumericLiteral */] = "FirstLiteralToken";
  SyntaxKind4[SyntaxKind4["LastLiteralToken"] = 15 /* NoSubstitutionTemplateLiteral */] = "LastLiteralToken";
  SyntaxKind4[SyntaxKind4["FirstTemplateToken"] = 15 /* NoSubstitutionTemplateLiteral */] = "FirstTemplateToken";
  SyntaxKind4[SyntaxKind4["LastTemplateToken"] = 18 /* TemplateTail */] = "LastTemplateToken";
  SyntaxKind4[SyntaxKind4["FirstBinaryOperator"] = 30 /* LessThanToken */] = "FirstBinaryOperator";
  SyntaxKind4[SyntaxKind4["LastBinaryOperator"] = 79 /* CaretEqualsToken */] = "LastBinaryOperator";
  SyntaxKind4[SyntaxKind4["FirstStatement"] = 243 /* VariableStatement */] = "FirstStatement";
  SyntaxKind4[SyntaxKind4["LastStatement"] = 259 /* DebuggerStatement */] = "LastStatement";
  SyntaxKind4[SyntaxKind4["FirstNode"] = 166 /* QualifiedName */] = "FirstNode";
  SyntaxKind4[SyntaxKind4["FirstJSDocNode"] = 309 /* JSDocTypeExpression */] = "FirstJSDocNode";
  SyntaxKind4[SyntaxKind4["LastJSDocNode"] = 351 /* JSDocImportTag */] = "LastJSDocNode";
  SyntaxKind4[SyntaxKind4["FirstJSDocTagNode"] = 327 /* JSDocTag */] = "FirstJSDocTagNode";
  SyntaxKind4[SyntaxKind4["LastJSDocTagNode"] = 351 /* JSDocImportTag */] = "LastJSDocTagNode";
  SyntaxKind4[SyntaxKind4["FirstContextualKeyword"] = 128 /* AbstractKeyword */] = "FirstContextualKeyword";
  SyntaxKind4[SyntaxKind4["LastContextualKeyword"] = 165 /* OfKeyword */] = "LastContextualKeyword";
  return SyntaxKind4;
})(SyntaxKind || {});
var NodeFlags = /* @__PURE__ */ ((NodeFlags3) => {
  NodeFlags3[NodeFlags3["None"] = 0] = "None";
  NodeFlags3[NodeFlags3["Let"] = 1] = "Let";
  NodeFlags3[NodeFlags3["Const"] = 2] = "Const";
  NodeFlags3[NodeFlags3["Using"] = 4] = "Using";
  NodeFlags3[NodeFlags3["AwaitUsing"] = 6] = "AwaitUsing";
  NodeFlags3[NodeFlags3["NestedNamespace"] = 8] = "NestedNamespace";
  NodeFlags3[NodeFlags3["Synthesized"] = 16] = "Synthesized";
  NodeFlags3[NodeFlags3["Namespace"] = 32] = "Namespace";
  NodeFlags3[NodeFlags3["OptionalChain"] = 64] = "OptionalChain";
  NodeFlags3[NodeFlags3["ExportContext"] = 128] = "ExportContext";
  NodeFlags3[NodeFlags3["ContainsThis"] = 256] = "ContainsThis";
  NodeFlags3[NodeFlags3["HasImplicitReturn"] = 512] = "HasImplicitReturn";
  NodeFlags3[NodeFlags3["HasExplicitReturn"] = 1024] = "HasExplicitReturn";
  NodeFlags3[NodeFlags3["GlobalAugmentation"] = 2048] = "GlobalAugmentation";
  NodeFlags3[NodeFlags3["HasAsyncFunctions"] = 4096] = "HasAsyncFunctions";
  NodeFlags3[NodeFlags3["DisallowInContext"] = 8192] = "DisallowInContext";
  NodeFlags3[NodeFlags3["YieldContext"] = 16384] = "YieldContext";
  NodeFlags3[NodeFlags3["DecoratorContext"] = 32768] = "DecoratorContext";
  NodeFlags3[NodeFlags3["AwaitContext"] = 65536] = "AwaitContext";
  NodeFlags3[NodeFlags3["DisallowConditionalTypesContext"] = 131072] = "DisallowConditionalTypesContext";
  NodeFlags3[NodeFlags3["ThisNodeHasError"] = 262144] = "ThisNodeHasError";
  NodeFlags3[NodeFlags3["JavaScriptFile"] = 524288] = "JavaScriptFile";
  NodeFlags3[NodeFlags3["ThisNodeOrAnySubNodesHasError"] = 1048576] = "ThisNodeOrAnySubNodesHasError";
  NodeFlags3[NodeFlags3["HasAggregatedChildData"] = 2097152] = "HasAggregatedChildData";
  NodeFlags3[NodeFlags3["PossiblyContainsDynamicImport"] = 4194304] = "PossiblyContainsDynamicImport";
  NodeFlags3[NodeFlags3["PossiblyContainsImportMeta"] = 8388608] = "PossiblyContainsImportMeta";
  NodeFlags3[NodeFlags3["JSDoc"] = 16777216] = "JSDoc";
  NodeFlags3[NodeFlags3["Ambient"] = 33554432] = "Ambient";
  NodeFlags3[NodeFlags3["InWithStatement"] = 67108864] = "InWithStatement";
  NodeFlags3[NodeFlags3["JsonFile"] = 134217728] = "JsonFile";
  NodeFlags3[NodeFlags3["TypeCached"] = 268435456] = "TypeCached";
  NodeFlags3[NodeFlags3["Deprecated"] = 536870912] = "Deprecated";
  NodeFlags3[NodeFlags3["BlockScoped"] = 7] = "BlockScoped";
  NodeFlags3[NodeFlags3["Constant"] = 6] = "Constant";
  NodeFlags3[NodeFlags3["ReachabilityCheckFlags"] = 1536] = "ReachabilityCheckFlags";
  NodeFlags3[NodeFlags3["ReachabilityAndEmitFlags"] = 5632] = "ReachabilityAndEmitFlags";
  NodeFlags3[NodeFlags3["ContextFlags"] = 101441536] = "ContextFlags";
  NodeFlags3[NodeFlags3["TypeExcludesFlags"] = 81920] = "TypeExcludesFlags";
  NodeFlags3[NodeFlags3["PermanentlySetIncrementalFlags"] = 12582912] = "PermanentlySetIncrementalFlags";
  NodeFlags3[NodeFlags3["IdentifierHasExtendedUnicodeEscape"] = 256 /* ContainsThis */] = "IdentifierHasExtendedUnicodeEscape";
  NodeFlags3[NodeFlags3["IdentifierIsInJSDocNamespace"] = 4096 /* HasAsyncFunctions */] = "IdentifierIsInJSDocNamespace";
  return NodeFlags3;
})(NodeFlags || {});
var ModifierFlags = /* @__PURE__ */ ((ModifierFlags3) => {
  ModifierFlags3[ModifierFlags3["None"] = 0] = "None";
  ModifierFlags3[ModifierFlags3["Public"] = 1] = "Public";
  ModifierFlags3[ModifierFlags3["Private"] = 2] = "Private";
  ModifierFlags3[ModifierFlags3["Protected"] = 4] = "Protected";
  ModifierFlags3[ModifierFlags3["Readonly"] = 8] = "Readonly";
  ModifierFlags3[ModifierFlags3["Override"] = 16] = "Override";
  ModifierFlags3[ModifierFlags3["Export"] = 32] = "Export";
  ModifierFlags3[ModifierFlags3["Abstract"] = 64] = "Abstract";
  ModifierFlags3[ModifierFlags3["Ambient"] = 128] = "Ambient";
  ModifierFlags3[ModifierFlags3["Static"] = 256] = "Static";
  ModifierFlags3[ModifierFlags3["Accessor"] = 512] = "Accessor";
  ModifierFlags3[ModifierFlags3["Async"] = 1024] = "Async";
  ModifierFlags3[ModifierFlags3["Default"] = 2048] = "Default";
  ModifierFlags3[ModifierFlags3["Const"] = 4096] = "Const";
  ModifierFlags3[ModifierFlags3["In"] = 8192] = "In";
  ModifierFlags3[ModifierFlags3["Out"] = 16384] = "Out";
  ModifierFlags3[ModifierFlags3["Decorator"] = 32768] = "Decorator";
  ModifierFlags3[ModifierFlags3["Deprecated"] = 65536] = "Deprecated";
  ModifierFlags3[ModifierFlags3["JSDocPublic"] = 8388608] = "JSDocPublic";
  ModifierFlags3[ModifierFlags3["JSDocPrivate"] = 16777216] = "JSDocPrivate";
  ModifierFlags3[ModifierFlags3["JSDocProtected"] = 33554432] = "JSDocProtected";
  ModifierFlags3[ModifierFlags3["JSDocReadonly"] = 67108864] = "JSDocReadonly";
  ModifierFlags3[ModifierFlags3["JSDocOverride"] = 134217728] = "JSDocOverride";
  ModifierFlags3[ModifierFlags3["SyntacticOrJSDocModifiers"] = 31] = "SyntacticOrJSDocModifiers";
  ModifierFlags3[ModifierFlags3["SyntacticOnlyModifiers"] = 65504] = "SyntacticOnlyModifiers";
  ModifierFlags3[ModifierFlags3["SyntacticModifiers"] = 65535] = "SyntacticModifiers";
  ModifierFlags3[ModifierFlags3["JSDocCacheOnlyModifiers"] = 260046848] = "JSDocCacheOnlyModifiers";
  ModifierFlags3[ModifierFlags3["JSDocOnlyModifiers"] = 65536 /* Deprecated */] = "JSDocOnlyModifiers";
  ModifierFlags3[ModifierFlags3["NonCacheOnlyModifiers"] = 131071] = "NonCacheOnlyModifiers";
  ModifierFlags3[ModifierFlags3["HasComputedJSDocModifiers"] = 268435456] = "HasComputedJSDocModifiers";
  ModifierFlags3[ModifierFlags3["HasComputedFlags"] = 536870912] = "HasComputedFlags";
  ModifierFlags3[ModifierFlags3["AccessibilityModifier"] = 7] = "AccessibilityModifier";
  ModifierFlags3[ModifierFlags3["ParameterPropertyModifier"] = 31] = "ParameterPropertyModifier";
  ModifierFlags3[ModifierFlags3["NonPublicAccessibilityModifier"] = 6] = "NonPublicAccessibilityModifier";
  ModifierFlags3[ModifierFlags3["TypeScriptModifier"] = 28895] = "TypeScriptModifier";
  ModifierFlags3[ModifierFlags3["ExportDefault"] = 2080] = "ExportDefault";
  ModifierFlags3[ModifierFlags3["All"] = 131071] = "All";
  ModifierFlags3[ModifierFlags3["Modifier"] = 98303] = "Modifier";
  return ModifierFlags3;
})(ModifierFlags || {});
var RelationComparisonResult = /* @__PURE__ */ ((RelationComparisonResult3) => {
  RelationComparisonResult3[RelationComparisonResult3["None"] = 0] = "None";
  RelationComparisonResult3[RelationComparisonResult3["Succeeded"] = 1] = "Succeeded";
  RelationComparisonResult3[RelationComparisonResult3["Failed"] = 2] = "Failed";
  RelationComparisonResult3[RelationComparisonResult3["ReportsUnmeasurable"] = 8] = "ReportsUnmeasurable";
  RelationComparisonResult3[RelationComparisonResult3["ReportsUnreliable"] = 16] = "ReportsUnreliable";
  RelationComparisonResult3[RelationComparisonResult3["ReportsMask"] = 24] = "ReportsMask";
  RelationComparisonResult3[RelationComparisonResult3["ComplexityOverflow"] = 32] = "ComplexityOverflow";
  RelationComparisonResult3[RelationComparisonResult3["StackDepthOverflow"] = 64] = "StackDepthOverflow";
  RelationComparisonResult3[RelationComparisonResult3["Overflow"] = 96] = "Overflow";
  return RelationComparisonResult3;
})(RelationComparisonResult || {});
var GeneratedIdentifierFlags = /* @__PURE__ */ ((GeneratedIdentifierFlags2) => {
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["None"] = 0] = "None";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["Auto"] = 1] = "Auto";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["Loop"] = 2] = "Loop";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["Unique"] = 3] = "Unique";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["Node"] = 4] = "Node";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["KindMask"] = 7] = "KindMask";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["ReservedInNestedScopes"] = 8] = "ReservedInNestedScopes";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["Optimistic"] = 16] = "Optimistic";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["FileLevel"] = 32] = "FileLevel";
  GeneratedIdentifierFlags2[GeneratedIdentifierFlags2["AllowNameSubstitution"] = 64] = "AllowNameSubstitution";
  return GeneratedIdentifierFlags2;
})(GeneratedIdentifierFlags || {});
var FlowFlags = /* @__PURE__ */ ((FlowFlags2) => {
  FlowFlags2[FlowFlags2["Unreachable"] = 1] = "Unreachable";
  FlowFlags2[FlowFlags2["Start"] = 2] = "Start";
  FlowFlags2[FlowFlags2["BranchLabel"] = 4] = "BranchLabel";
  FlowFlags2[FlowFlags2["LoopLabel"] = 8] = "LoopLabel";
  FlowFlags2[FlowFlags2["Assignment"] = 16] = "Assignment";
  FlowFlags2[FlowFlags2["TrueCondition"] = 32] = "TrueCondition";
  FlowFlags2[FlowFlags2["FalseCondition"] = 64] = "FalseCondition";
  FlowFlags2[FlowFlags2["SwitchClause"] = 128] = "SwitchClause";
  FlowFlags2[FlowFlags2["ArrayMutation"] = 256] = "ArrayMutation";
  FlowFlags2[FlowFlags2["Call"] = 512] = "Call";
  FlowFlags2[FlowFlags2["ReduceLabel"] = 1024] = "ReduceLabel";
  FlowFlags2[FlowFlags2["Referenced"] = 2048] = "Referenced";
  FlowFlags2[FlowFlags2["Shared"] = 4096] = "Shared";
  FlowFlags2[FlowFlags2["Label"] = 12] = "Label";
  FlowFlags2[FlowFlags2["Condition"] = 96] = "Condition";
  return FlowFlags2;
})(FlowFlags || {});
var OperationCanceledException = class {
};
var FileIncludeKind = /* @__PURE__ */ ((FileIncludeKind2) => {
  FileIncludeKind2[FileIncludeKind2["RootFile"] = 0] = "RootFile";
  FileIncludeKind2[FileIncludeKind2["SourceFromProjectReference"] = 1] = "SourceFromProjectReference";
  FileIncludeKind2[FileIncludeKind2["OutputFromProjectReference"] = 2] = "OutputFromProjectReference";
  FileIncludeKind2[FileIncludeKind2["Import"] = 3] = "Import";
  FileIncludeKind2[FileIncludeKind2["ReferenceFile"] = 4] = "ReferenceFile";
  FileIncludeKind2[FileIncludeKind2["TypeReferenceDirective"] = 5] = "TypeReferenceDirective";
  FileIncludeKind2[FileIncludeKind2["LibFile"] = 6] = "LibFile";
  FileIncludeKind2[FileIncludeKind2["LibReferenceDirective"] = 7] = "LibReferenceDirective";
  FileIncludeKind2[FileIncludeKind2["AutomaticTypeDirectiveFile"] = 8] = "AutomaticTypeDirectiveFile";
  return FileIncludeKind2;
})(FileIncludeKind || {});
var SymbolFlags = /* @__PURE__ */ ((SymbolFlags2) => {
  SymbolFlags2[SymbolFlags2["None"] = 0] = "None";
  SymbolFlags2[SymbolFlags2["FunctionScopedVariable"] = 1] = "FunctionScopedVariable";
  SymbolFlags2[SymbolFlags2["BlockScopedVariable"] = 2] = "BlockScopedVariable";
  SymbolFlags2[SymbolFlags2["Property"] = 4] = "Property";
  SymbolFlags2[SymbolFlags2["EnumMember"] = 8] = "EnumMember";
  SymbolFlags2[SymbolFlags2["Function"] = 16] = "Function";
  SymbolFlags2[SymbolFlags2["Class"] = 32] = "Class";
  SymbolFlags2[SymbolFlags2["Interface"] = 64] = "Interface";
  SymbolFlags2[SymbolFlags2["ConstEnum"] = 128] = "ConstEnum";
  SymbolFlags2[SymbolFlags2["RegularEnum"] = 256] = "RegularEnum";
  SymbolFlags2[SymbolFlags2["ValueModule"] = 512] = "ValueModule";
  SymbolFlags2[SymbolFlags2["NamespaceModule"] = 1024] = "NamespaceModule";
  SymbolFlags2[SymbolFlags2["TypeLiteral"] = 2048] = "TypeLiteral";
  SymbolFlags2[SymbolFlags2["ObjectLiteral"] = 4096] = "ObjectLiteral";
  SymbolFlags2[SymbolFlags2["Method"] = 8192] = "Method";
  SymbolFlags2[SymbolFlags2["Constructor"] = 16384] = "Constructor";
  SymbolFlags2[SymbolFlags2["GetAccessor"] = 32768] = "GetAccessor";
  SymbolFlags2[SymbolFlags2["SetAccessor"] = 65536] = "SetAccessor";
  SymbolFlags2[SymbolFlags2["Signature"] = 131072] = "Signature";
  SymbolFlags2[SymbolFlags2["TypeParameter"] = 262144] = "TypeParameter";
  SymbolFlags2[SymbolFlags2["TypeAlias"] = 524288] = "TypeAlias";
  SymbolFlags2[SymbolFlags2["ExportValue"] = 1048576] = "ExportValue";
  SymbolFlags2[SymbolFlags2["Alias"] = 2097152] = "Alias";
  SymbolFlags2[SymbolFlags2["Prototype"] = 4194304] = "Prototype";
  SymbolFlags2[SymbolFlags2["ExportStar"] = 8388608] = "ExportStar";
  SymbolFlags2[SymbolFlags2["Optional"] = 16777216] = "Optional";
  SymbolFlags2[SymbolFlags2["Transient"] = 33554432] = "Transient";
  SymbolFlags2[SymbolFlags2["Assignment"] = 67108864] = "Assignment";
  SymbolFlags2[SymbolFlags2["ModuleExports"] = 134217728] = "ModuleExports";
  SymbolFlags2[SymbolFlags2["All"] = -1] = "All";
  SymbolFlags2[SymbolFlags2["Enum"] = 384] = "Enum";
  SymbolFlags2[SymbolFlags2["Variable"] = 3] = "Variable";
  SymbolFlags2[SymbolFlags2["Value"] = 111551] = "Value";
  SymbolFlags2[SymbolFlags2["Type"] = 788968] = "Type";
  SymbolFlags2[SymbolFlags2["Namespace"] = 1920] = "Namespace";
  SymbolFlags2[SymbolFlags2["Module"] = 1536] = "Module";
  SymbolFlags2[SymbolFlags2["Accessor"] = 98304] = "Accessor";
  SymbolFlags2[SymbolFlags2["FunctionScopedVariableExcludes"] = 111550] = "FunctionScopedVariableExcludes";
  SymbolFlags2[SymbolFlags2["BlockScopedVariableExcludes"] = 111551 /* Value */] = "BlockScopedVariableExcludes";
  SymbolFlags2[SymbolFlags2["ParameterExcludes"] = 111551 /* Value */] = "ParameterExcludes";
  SymbolFlags2[SymbolFlags2["PropertyExcludes"] = 0 /* None */] = "PropertyExcludes";
  SymbolFlags2[SymbolFlags2["EnumMemberExcludes"] = 900095] = "EnumMemberExcludes";
  SymbolFlags2[SymbolFlags2["FunctionExcludes"] = 110991] = "FunctionExcludes";
  SymbolFlags2[SymbolFlags2["ClassExcludes"] = 899503] = "ClassExcludes";
  SymbolFlags2[SymbolFlags2["InterfaceExcludes"] = 788872] = "InterfaceExcludes";
  SymbolFlags2[SymbolFlags2["RegularEnumExcludes"] = 899327] = "RegularEnumExcludes";
  SymbolFlags2[SymbolFlags2["ConstEnumExcludes"] = 899967] = "ConstEnumExcludes";
  SymbolFlags2[SymbolFlags2["ValueModuleExcludes"] = 110735] = "ValueModuleExcludes";
  SymbolFlags2[SymbolFlags2["NamespaceModuleExcludes"] = 0] = "NamespaceModuleExcludes";
  SymbolFlags2[SymbolFlags2["MethodExcludes"] = 103359] = "MethodExcludes";
  SymbolFlags2[SymbolFlags2["GetAccessorExcludes"] = 46015] = "GetAccessorExcludes";
  SymbolFlags2[SymbolFlags2["SetAccessorExcludes"] = 78783] = "SetAccessorExcludes";
  SymbolFlags2[SymbolFlags2["AccessorExcludes"] = 13247] = "AccessorExcludes";
  SymbolFlags2[SymbolFlags2["TypeParameterExcludes"] = 526824] = "TypeParameterExcludes";
  SymbolFlags2[SymbolFlags2["TypeAliasExcludes"] = 788968 /* Type */] = "TypeAliasExcludes";
  SymbolFlags2[SymbolFlags2["AliasExcludes"] = 2097152 /* Alias */] = "AliasExcludes";
  SymbolFlags2[SymbolFlags2["ModuleMember"] = 2623475] = "ModuleMember";
  SymbolFlags2[SymbolFlags2["ExportHasLocal"] = 944] = "ExportHasLocal";
  SymbolFlags2[SymbolFlags2["BlockScoped"] = 418] = "BlockScoped";
  SymbolFlags2[SymbolFlags2["PropertyOrAccessor"] = 98308] = "PropertyOrAccessor";
  SymbolFlags2[SymbolFlags2["ClassMember"] = 106500] = "ClassMember";
  SymbolFlags2[SymbolFlags2["ExportSupportsDefaultModifier"] = 112] = "ExportSupportsDefaultModifier";
  SymbolFlags2[SymbolFlags2["ExportDoesNotSupportDefaultModifier"] = -113] = "ExportDoesNotSupportDefaultModifier";
  SymbolFlags2[SymbolFlags2["Classifiable"] = 2885600] = "Classifiable";
  SymbolFlags2[SymbolFlags2["LateBindingContainer"] = 6256] = "LateBindingContainer";
  return SymbolFlags2;
})(SymbolFlags || {});
var NodeCheckFlags = /* @__PURE__ */ ((NodeCheckFlags3) => {
  NodeCheckFlags3[NodeCheckFlags3["None"] = 0] = "None";
  NodeCheckFlags3[NodeCheckFlags3["TypeChecked"] = 1] = "TypeChecked";
  NodeCheckFlags3[NodeCheckFlags3["LexicalThis"] = 2] = "LexicalThis";
  NodeCheckFlags3[NodeCheckFlags3["CaptureThis"] = 4] = "CaptureThis";
  NodeCheckFlags3[NodeCheckFlags3["CaptureNewTarget"] = 8] = "CaptureNewTarget";
  NodeCheckFlags3[NodeCheckFlags3["SuperInstance"] = 16] = "SuperInstance";
  NodeCheckFlags3[NodeCheckFlags3["SuperStatic"] = 32] = "SuperStatic";
  NodeCheckFlags3[NodeCheckFlags3["ContextChecked"] = 64] = "ContextChecked";
  NodeCheckFlags3[NodeCheckFlags3["MethodWithSuperPropertyAccessInAsync"] = 128] = "MethodWithSuperPropertyAccessInAsync";
  NodeCheckFlags3[NodeCheckFlags3["MethodWithSuperPropertyAssignmentInAsync"] = 256] = "MethodWithSuperPropertyAssignmentInAsync";
  NodeCheckFlags3[NodeCheckFlags3["CaptureArguments"] = 512] = "CaptureArguments";
  NodeCheckFlags3[NodeCheckFlags3["EnumValuesComputed"] = 1024] = "EnumValuesComputed";
  NodeCheckFlags3[NodeCheckFlags3["LexicalModuleMergesWithClass"] = 2048] = "LexicalModuleMergesWithClass";
  NodeCheckFlags3[NodeCheckFlags3["LoopWithCapturedBlockScopedBinding"] = 4096] = "LoopWithCapturedBlockScopedBinding";
  NodeCheckFlags3[NodeCheckFlags3["ContainsCapturedBlockScopeBinding"] = 8192] = "ContainsCapturedBlockScopeBinding";
  NodeCheckFlags3[NodeCheckFlags3["CapturedBlockScopedBinding"] = 16384] = "CapturedBlockScopedBinding";
  NodeCheckFlags3[NodeCheckFlags3["BlockScopedBindingInLoop"] = 32768] = "BlockScopedBindingInLoop";
  NodeCheckFlags3[NodeCheckFlags3["NeedsLoopOutParameter"] = 65536] = "NeedsLoopOutParameter";
  NodeCheckFlags3[NodeCheckFlags3["AssignmentsMarked"] = 131072] = "AssignmentsMarked";
  NodeCheckFlags3[NodeCheckFlags3["ContainsConstructorReference"] = 262144] = "ContainsConstructorReference";
  NodeCheckFlags3[NodeCheckFlags3["ConstructorReference"] = 536870912] = "ConstructorReference";
  NodeCheckFlags3[NodeCheckFlags3["ContainsClassWithPrivateIdentifiers"] = 1048576] = "ContainsClassWithPrivateIdentifiers";
  NodeCheckFlags3[NodeCheckFlags3["ContainsSuperPropertyInStaticInitializer"] = 2097152] = "ContainsSuperPropertyInStaticInitializer";
  NodeCheckFlags3[NodeCheckFlags3["InCheckIdentifier"] = 4194304] = "InCheckIdentifier";
  NodeCheckFlags3[NodeCheckFlags3["PartiallyTypeChecked"] = 8388608] = "PartiallyTypeChecked";
  NodeCheckFlags3[NodeCheckFlags3["LazyFlags"] = 539358128] = "LazyFlags";
  return NodeCheckFlags3;
})(NodeCheckFlags || {});
var TypeFlags = /* @__PURE__ */ ((TypeFlags2) => {
  TypeFlags2[TypeFlags2["Any"] = 1] = "Any";
  TypeFlags2[TypeFlags2["Unknown"] = 2] = "Unknown";
  TypeFlags2[TypeFlags2["String"] = 4] = "String";
  TypeFlags2[TypeFlags2["Number"] = 8] = "Number";
  TypeFlags2[TypeFlags2["Boolean"] = 16] = "Boolean";
  TypeFlags2[TypeFlags2["Enum"] = 32] = "Enum";
  TypeFlags2[TypeFlags2["BigInt"] = 64] = "BigInt";
  TypeFlags2[TypeFlags2["StringLiteral"] = 128] = "StringLiteral";
  TypeFlags2[TypeFlags2["NumberLiteral"] = 256] = "NumberLiteral";
  TypeFlags2[TypeFlags2["BooleanLiteral"] = 512] = "BooleanLiteral";
  TypeFlags2[TypeFlags2["EnumLiteral"] = 1024] = "EnumLiteral";
  TypeFlags2[TypeFlags2["BigIntLiteral"] = 2048] = "BigIntLiteral";
  TypeFlags2[TypeFlags2["ESSymbol"] = 4096] = "ESSymbol";
  TypeFlags2[TypeFlags2["UniqueESSymbol"] = 8192] = "UniqueESSymbol";
  TypeFlags2[TypeFlags2["Void"] = 16384] = "Void";
  TypeFlags2[TypeFlags2["Undefined"] = 32768] = "Undefined";
  TypeFlags2[TypeFlags2["Null"] = 65536] = "Null";
  TypeFlags2[TypeFlags2["Never"] = 131072] = "Never";
  TypeFlags2[TypeFlags2["TypeParameter"] = 262144] = "TypeParameter";
  TypeFlags2[TypeFlags2["Object"] = 524288] = "Object";
  TypeFlags2[TypeFlags2["Union"] = 1048576] = "Union";
  TypeFlags2[TypeFlags2["Intersection"] = 2097152] = "Intersection";
  TypeFlags2[TypeFlags2["Index"] = 4194304] = "Index";
  TypeFlags2[TypeFlags2["IndexedAccess"] = 8388608] = "IndexedAccess";
  TypeFlags2[TypeFlags2["Conditional"] = 16777216] = "Conditional";
  TypeFlags2[TypeFlags2["Substitution"] = 33554432] = "Substitution";
  TypeFlags2[TypeFlags2["NonPrimitive"] = 67108864] = "NonPrimitive";
  TypeFlags2[TypeFlags2["TemplateLiteral"] = 134217728] = "TemplateLiteral";
  TypeFlags2[TypeFlags2["StringMapping"] = 268435456] = "StringMapping";
  TypeFlags2[TypeFlags2["Reserved1"] = 536870912] = "Reserved1";
  TypeFlags2[TypeFlags2["Reserved2"] = 1073741824] = "Reserved2";
  TypeFlags2[TypeFlags2["AnyOrUnknown"] = 3] = "AnyOrUnknown";
  TypeFlags2[TypeFlags2["Nullable"] = 98304] = "Nullable";
  TypeFlags2[TypeFlags2["Literal"] = 2944] = "Literal";
  TypeFlags2[TypeFlags2["Unit"] = 109472] = "Unit";
  TypeFlags2[TypeFlags2["Freshable"] = 2976] = "Freshable";
  TypeFlags2[TypeFlags2["StringOrNumberLiteral"] = 384] = "StringOrNumberLiteral";
  TypeFlags2[TypeFlags2["StringOrNumberLiteralOrUnique"] = 8576] = "StringOrNumberLiteralOrUnique";
  TypeFlags2[TypeFlags2["DefinitelyFalsy"] = 117632] = "DefinitelyFalsy";
  TypeFlags2[TypeFlags2["PossiblyFalsy"] = 117724] = "PossiblyFalsy";
  TypeFlags2[TypeFlags2["Intrinsic"] = 67359327] = "Intrinsic";
  TypeFlags2[TypeFlags2["StringLike"] = 402653316] = "StringLike";
  TypeFlags2[TypeFlags2["NumberLike"] = 296] = "NumberLike";
  TypeFlags2[TypeFlags2["BigIntLike"] = 2112] = "BigIntLike";
  TypeFlags2[TypeFlags2["BooleanLike"] = 528] = "BooleanLike";
  TypeFlags2[TypeFlags2["EnumLike"] = 1056] = "EnumLike";
  TypeFlags2[TypeFlags2["ESSymbolLike"] = 12288] = "ESSymbolLike";
  TypeFlags2[TypeFlags2["VoidLike"] = 49152] = "VoidLike";
  TypeFlags2[TypeFlags2["Primitive"] = 402784252] = "Primitive";
  TypeFlags2[TypeFlags2["DefinitelyNonNullable"] = 470302716] = "DefinitelyNonNullable";
  TypeFlags2[TypeFlags2["DisjointDomains"] = 469892092] = "DisjointDomains";
  TypeFlags2[TypeFlags2["UnionOrIntersection"] = 3145728] = "UnionOrIntersection";
  TypeFlags2[TypeFlags2["StructuredType"] = 3670016] = "StructuredType";
  TypeFlags2[TypeFlags2["TypeVariable"] = 8650752] = "TypeVariable";
  TypeFlags2[TypeFlags2["InstantiableNonPrimitive"] = 58982400] = "InstantiableNonPrimitive";
  TypeFlags2[TypeFlags2["InstantiablePrimitive"] = 406847488] = "InstantiablePrimitive";
  TypeFlags2[TypeFlags2["Instantiable"] = 465829888] = "Instantiable";
  TypeFlags2[TypeFlags2["StructuredOrInstantiable"] = 469499904] = "StructuredOrInstantiable";
  TypeFlags2[TypeFlags2["ObjectFlagsType"] = 3899393] = "ObjectFlagsType";
  TypeFlags2[TypeFlags2["Simplifiable"] = 25165824] = "Simplifiable";
  TypeFlags2[TypeFlags2["Singleton"] = 67358815] = "Singleton";
  TypeFlags2[TypeFlags2["Narrowable"] = 536624127] = "Narrowable";
  TypeFlags2[TypeFlags2["IncludesMask"] = 473694207] = "IncludesMask";
  TypeFlags2[TypeFlags2["IncludesMissingType"] = 262144 /* TypeParameter */] = "IncludesMissingType";
  TypeFlags2[TypeFlags2["IncludesNonWideningType"] = 4194304 /* Index */] = "IncludesNonWideningType";
  TypeFlags2[TypeFlags2["IncludesWildcard"] = 8388608 /* IndexedAccess */] = "IncludesWildcard";
  TypeFlags2[TypeFlags2["IncludesEmptyObject"] = 16777216 /* Conditional */] = "IncludesEmptyObject";
  TypeFlags2[TypeFlags2["IncludesInstantiable"] = 33554432 /* Substitution */] = "IncludesInstantiable";
  TypeFlags2[TypeFlags2["IncludesConstrainedTypeVariable"] = 536870912 /* Reserved1 */] = "IncludesConstrainedTypeVariable";
  TypeFlags2[TypeFlags2["IncludesError"] = 1073741824 /* Reserved2 */] = "IncludesError";
  TypeFlags2[TypeFlags2["NotPrimitiveUnion"] = 36323331] = "NotPrimitiveUnion";
  return TypeFlags2;
})(TypeFlags || {});
var ObjectFlags = /* @__PURE__ */ ((ObjectFlags3) => {
  ObjectFlags3[ObjectFlags3["None"] = 0] = "None";
  ObjectFlags3[ObjectFlags3["Class"] = 1] = "Class";
  ObjectFlags3[ObjectFlags3["Interface"] = 2] = "Interface";
  ObjectFlags3[ObjectFlags3["Reference"] = 4] = "Reference";
  ObjectFlags3[ObjectFlags3["Tuple"] = 8] = "Tuple";
  ObjectFlags3[ObjectFlags3["Anonymous"] = 16] = "Anonymous";
  ObjectFlags3[ObjectFlags3["Mapped"] = 32] = "Mapped";
  ObjectFlags3[ObjectFlags3["Instantiated"] = 64] = "Instantiated";
  ObjectFlags3[ObjectFlags3["ObjectLiteral"] = 128] = "ObjectLiteral";
  ObjectFlags3[ObjectFlags3["EvolvingArray"] = 256] = "EvolvingArray";
  ObjectFlags3[ObjectFlags3["ObjectLiteralPatternWithComputedProperties"] = 512] = "ObjectLiteralPatternWithComputedProperties";
  ObjectFlags3[ObjectFlags3["ReverseMapped"] = 1024] = "ReverseMapped";
  ObjectFlags3[ObjectFlags3["JsxAttributes"] = 2048] = "JsxAttributes";
  ObjectFlags3[ObjectFlags3["JSLiteral"] = 4096] = "JSLiteral";
  ObjectFlags3[ObjectFlags3["FreshLiteral"] = 8192] = "FreshLiteral";
  ObjectFlags3[ObjectFlags3["ArrayLiteral"] = 16384] = "ArrayLiteral";
  ObjectFlags3[ObjectFlags3["PrimitiveUnion"] = 32768] = "PrimitiveUnion";
  ObjectFlags3[ObjectFlags3["ContainsWideningType"] = 65536] = "ContainsWideningType";
  ObjectFlags3[ObjectFlags3["ContainsObjectOrArrayLiteral"] = 131072] = "ContainsObjectOrArrayLiteral";
  ObjectFlags3[ObjectFlags3["NonInferrableType"] = 262144] = "NonInferrableType";
  ObjectFlags3[ObjectFlags3["CouldContainTypeVariablesComputed"] = 524288] = "CouldContainTypeVariablesComputed";
  ObjectFlags3[ObjectFlags3["CouldContainTypeVariables"] = 1048576] = "CouldContainTypeVariables";
  ObjectFlags3[ObjectFlags3["ClassOrInterface"] = 3] = "ClassOrInterface";
  ObjectFlags3[ObjectFlags3["RequiresWidening"] = 196608] = "RequiresWidening";
  ObjectFlags3[ObjectFlags3["PropagatingFlags"] = 458752] = "PropagatingFlags";
  ObjectFlags3[ObjectFlags3["InstantiatedMapped"] = 96] = "InstantiatedMapped";
  ObjectFlags3[ObjectFlags3["ObjectTypeKindMask"] = 1343] = "ObjectTypeKindMask";
  ObjectFlags3[ObjectFlags3["ContainsSpread"] = 2097152] = "ContainsSpread";
  ObjectFlags3[ObjectFlags3["ObjectRestType"] = 4194304] = "ObjectRestType";
  ObjectFlags3[ObjectFlags3["InstantiationExpressionType"] = 8388608] = "InstantiationExpressionType";
  ObjectFlags3[ObjectFlags3["SingleSignatureType"] = 134217728] = "SingleSignatureType";
  ObjectFlags3[ObjectFlags3["IsClassInstanceClone"] = 16777216] = "IsClassInstanceClone";
  ObjectFlags3[ObjectFlags3["IdenticalBaseTypeCalculated"] = 33554432] = "IdenticalBaseTypeCalculated";
  ObjectFlags3[ObjectFlags3["IdenticalBaseTypeExists"] = 67108864] = "IdenticalBaseTypeExists";
  ObjectFlags3[ObjectFlags3["IsGenericTypeComputed"] = 2097152] = "IsGenericTypeComputed";
  ObjectFlags3[ObjectFlags3["IsGenericObjectType"] = 4194304] = "IsGenericObjectType";
  ObjectFlags3[ObjectFlags3["IsGenericIndexType"] = 8388608] = "IsGenericIndexType";
  ObjectFlags3[ObjectFlags3["IsGenericType"] = 12582912] = "IsGenericType";
  ObjectFlags3[ObjectFlags3["ContainsIntersections"] = 16777216] = "ContainsIntersections";
  ObjectFlags3[ObjectFlags3["IsUnknownLikeUnionComputed"] = 33554432] = "IsUnknownLikeUnionComputed";
  ObjectFlags3[ObjectFlags3["IsUnknownLikeUnion"] = 67108864] = "IsUnknownLikeUnion";
  ObjectFlags3[ObjectFlags3["IsNeverIntersectionComputed"] = 16777216] = "IsNeverIntersectionComputed";
  ObjectFlags3[ObjectFlags3["IsNeverIntersection"] = 33554432] = "IsNeverIntersection";
  ObjectFlags3[ObjectFlags3["IsConstrainedTypeVariable"] = 67108864] = "IsConstrainedTypeVariable";
  return ObjectFlags3;
})(ObjectFlags || {});
var SignatureFlags = /* @__PURE__ */ ((SignatureFlags4) => {
  SignatureFlags4[SignatureFlags4["None"] = 0] = "None";
  SignatureFlags4[SignatureFlags4["HasRestParameter"] = 1] = "HasRestParameter";
  SignatureFlags4[SignatureFlags4["HasLiteralTypes"] = 2] = "HasLiteralTypes";
  SignatureFlags4[SignatureFlags4["Abstract"] = 4] = "Abstract";
  SignatureFlags4[SignatureFlags4["IsInnerCallChain"] = 8] = "IsInnerCallChain";
  SignatureFlags4[SignatureFlags4["IsOuterCallChain"] = 16] = "IsOuterCallChain";
  SignatureFlags4[SignatureFlags4["IsUntypedSignatureInJSFile"] = 32] = "IsUntypedSignatureInJSFile";
  SignatureFlags4[SignatureFlags4["IsNonInferrable"] = 64] = "IsNonInferrable";
  SignatureFlags4[SignatureFlags4["IsSignatureCandidateForOverloadFailure"] = 128] = "IsSignatureCandidateForOverloadFailure";
  SignatureFlags4[SignatureFlags4["PropagatingFlags"] = 167] = "PropagatingFlags";
  SignatureFlags4[SignatureFlags4["CallChainFlags"] = 24] = "CallChainFlags";
  return SignatureFlags4;
})(SignatureFlags || {});
var DiagnosticCategory = /* @__PURE__ */ ((DiagnosticCategory2) => {
  DiagnosticCategory2[DiagnosticCategory2["Warning"] = 0] = "Warning";
  DiagnosticCategory2[DiagnosticCategory2["Error"] = 1] = "Error";
  DiagnosticCategory2[DiagnosticCategory2["Suggestion"] = 2] = "Suggestion";
  DiagnosticCategory2[DiagnosticCategory2["Message"] = 3] = "Message";
  return DiagnosticCategory2;
})(DiagnosticCategory || {});
function diagnosticCategoryName(d, lowerCase = true) {
  const name = DiagnosticCategory[d.category];
  return lowerCase ? name.toLowerCase() : name;
}
var ModuleResolutionKind = /* @__PURE__ */ ((ModuleResolutionKind2) => {
  ModuleResolutionKind2[ModuleResolutionKind2["Classic"] = 1] = "Classic";
  ModuleResolutionKind2[ModuleResolutionKind2["NodeJs"] = 2] = "NodeJs";
  ModuleResolutionKind2[ModuleResolutionKind2["Node10"] = 2] = "Node10";
  ModuleResolutionKind2[ModuleResolutionKind2["Node16"] = 3] = "Node16";
  ModuleResolutionKind2[ModuleResolutionKind2["NodeNext"] = 99] = "NodeNext";
  ModuleResolutionKind2[ModuleResolutionKind2["Bundler"] = 100] = "Bundler";
  return ModuleResolutionKind2;
})(ModuleResolutionKind || {});
var ModuleKind = /* @__PURE__ */ ((ModuleKind2) => {
  ModuleKind2[ModuleKind2["None"] = 0] = "None";
  ModuleKind2[ModuleKind2["CommonJS"] = 1] = "CommonJS";
  ModuleKind2[ModuleKind2["AMD"] = 2] = "AMD";
  ModuleKind2[ModuleKind2["UMD"] = 3] = "UMD";
  ModuleKind2[ModuleKind2["System"] = 4] = "System";
  ModuleKind2[ModuleKind2["ES2015"] = 5] = "ES2015";
  ModuleKind2[ModuleKind2["ES2020"] = 6] = "ES2020";
  ModuleKind2[ModuleKind2["ES2022"] = 7] = "ES2022";
  ModuleKind2[ModuleKind2["ESNext"] = 99] = "ESNext";
  ModuleKind2[ModuleKind2["Node16"] = 100] = "Node16";
  ModuleKind2[ModuleKind2["Node18"] = 101] = "Node18";
  ModuleKind2[ModuleKind2["NodeNext"] = 199] = "NodeNext";
  ModuleKind2[ModuleKind2["Preserve"] = 200] = "Preserve";
  return ModuleKind2;
})(ModuleKind || {});
var ScriptKind = /* @__PURE__ */ ((ScriptKind3) => {
  ScriptKind3[ScriptKind3["Unknown"] = 0] = "Unknown";
  ScriptKind3[ScriptKind3["JS"] = 1] = "JS";
  ScriptKind3[ScriptKind3["JSX"] = 2] = "JSX";
  ScriptKind3[ScriptKind3["TS"] = 3] = "TS";
  ScriptKind3[ScriptKind3["TSX"] = 4] = "TSX";
  ScriptKind3[ScriptKind3["External"] = 5] = "External";
  ScriptKind3[ScriptKind3["JSON"] = 6] = "JSON";
  ScriptKind3[ScriptKind3["Deferred"] = 7] = "Deferred";
  return ScriptKind3;
})(ScriptKind || {});
var TransformFlags = /* @__PURE__ */ ((TransformFlags3) => {
  TransformFlags3[TransformFlags3["None"] = 0] = "None";
  TransformFlags3[TransformFlags3["ContainsTypeScript"] = 1] = "ContainsTypeScript";
  TransformFlags3[TransformFlags3["ContainsJsx"] = 2] = "ContainsJsx";
  TransformFlags3[TransformFlags3["ContainsESNext"] = 4] = "ContainsESNext";
  TransformFlags3[TransformFlags3["ContainsES2022"] = 8] = "ContainsES2022";
  TransformFlags3[TransformFlags3["ContainsES2021"] = 16] = "ContainsES2021";
  TransformFlags3[TransformFlags3["ContainsES2020"] = 32] = "ContainsES2020";
  TransformFlags3[TransformFlags3["ContainsES2019"] = 64] = "ContainsES2019";
  TransformFlags3[TransformFlags3["ContainsES2018"] = 128] = "ContainsES2018";
  TransformFlags3[TransformFlags3["ContainsES2017"] = 256] = "ContainsES2017";
  TransformFlags3[TransformFlags3["ContainsES2016"] = 512] = "ContainsES2016";
  TransformFlags3[TransformFlags3["ContainsES2015"] = 1024] = "ContainsES2015";
  TransformFlags3[TransformFlags3["ContainsGenerator"] = 2048] = "ContainsGenerator";
  TransformFlags3[TransformFlags3["ContainsDestructuringAssignment"] = 4096] = "ContainsDestructuringAssignment";
  TransformFlags3[TransformFlags3["ContainsTypeScriptClassSyntax"] = 8192] = "ContainsTypeScriptClassSyntax";
  TransformFlags3[TransformFlags3["ContainsLexicalThis"] = 16384] = "ContainsLexicalThis";
  TransformFlags3[TransformFlags3["ContainsRestOrSpread"] = 32768] = "ContainsRestOrSpread";
  TransformFlags3[TransformFlags3["ContainsObjectRestOrSpread"] = 65536] = "ContainsObjectRestOrSpread";
  TransformFlags3[TransformFlags3["ContainsComputedPropertyName"] = 131072] = "ContainsComputedPropertyName";
  TransformFlags3[TransformFlags3["ContainsBlockScopedBinding"] = 262144] = "ContainsBlockScopedBinding";
  TransformFlags3[TransformFlags3["ContainsBindingPattern"] = 524288] = "ContainsBindingPattern";
  TransformFlags3[TransformFlags3["ContainsYield"] = 1048576] = "ContainsYield";
  TransformFlags3[TransformFlags3["ContainsAwait"] = 2097152] = "ContainsAwait";
  TransformFlags3[TransformFlags3["ContainsHoistedDeclarationOrCompletion"] = 4194304] = "ContainsHoistedDeclarationOrCompletion";
  TransformFlags3[TransformFlags3["ContainsDynamicImport"] = 8388608] = "ContainsDynamicImport";
  TransformFlags3[TransformFlags3["ContainsClassFields"] = 16777216] = "ContainsClassFields";
  TransformFlags3[TransformFlags3["ContainsDecorators"] = 33554432] = "ContainsDecorators";
  TransformFlags3[TransformFlags3["ContainsPossibleTopLevelAwait"] = 67108864] = "ContainsPossibleTopLevelAwait";
  TransformFlags3[TransformFlags3["ContainsLexicalSuper"] = 134217728] = "ContainsLexicalSuper";
  TransformFlags3[TransformFlags3["ContainsUpdateExpressionForIdentifier"] = 268435456] = "ContainsUpdateExpressionForIdentifier";
  TransformFlags3[TransformFlags3["ContainsPrivateIdentifierInExpression"] = 536870912] = "ContainsPrivateIdentifierInExpression";
  TransformFlags3[TransformFlags3["HasComputedFlags"] = -2147483648] = "HasComputedFlags";
  TransformFlags3[TransformFlags3["AssertTypeScript"] = 1 /* ContainsTypeScript */] = "AssertTypeScript";
  TransformFlags3[TransformFlags3["AssertJsx"] = 2 /* ContainsJsx */] = "AssertJsx";
  TransformFlags3[TransformFlags3["AssertESNext"] = 4 /* ContainsESNext */] = "AssertESNext";
  TransformFlags3[TransformFlags3["AssertES2022"] = 8 /* ContainsES2022 */] = "AssertES2022";
  TransformFlags3[TransformFlags3["AssertES2021"] = 16 /* ContainsES2021 */] = "AssertES2021";
  TransformFlags3[TransformFlags3["AssertES2020"] = 32 /* ContainsES2020 */] = "AssertES2020";
  TransformFlags3[TransformFlags3["AssertES2019"] = 64 /* ContainsES2019 */] = "AssertES2019";
  TransformFlags3[TransformFlags3["AssertES2018"] = 128 /* ContainsES2018 */] = "AssertES2018";
  TransformFlags3[TransformFlags3["AssertES2017"] = 256 /* ContainsES2017 */] = "AssertES2017";
  TransformFlags3[TransformFlags3["AssertES2016"] = 512 /* ContainsES2016 */] = "AssertES2016";
  TransformFlags3[TransformFlags3["AssertES2015"] = 1024 /* ContainsES2015 */] = "AssertES2015";
  TransformFlags3[TransformFlags3["AssertGenerator"] = 2048 /* ContainsGenerator */] = "AssertGenerator";
  TransformFlags3[TransformFlags3["AssertDestructuringAssignment"] = 4096 /* ContainsDestructuringAssignment */] = "AssertDestructuringAssignment";
  TransformFlags3[TransformFlags3["OuterExpressionExcludes"] = -2147483648 /* HasComputedFlags */] = "OuterExpressionExcludes";
  TransformFlags3[TransformFlags3["PropertyAccessExcludes"] = -2147483648 /* OuterExpressionExcludes */] = "PropertyAccessExcludes";
  TransformFlags3[TransformFlags3["NodeExcludes"] = -2147483648 /* PropertyAccessExcludes */] = "NodeExcludes";
  TransformFlags3[TransformFlags3["ArrowFunctionExcludes"] = -2072174592] = "ArrowFunctionExcludes";
  TransformFlags3[TransformFlags3["FunctionExcludes"] = -1937940480] = "FunctionExcludes";
  TransformFlags3[TransformFlags3["ConstructorExcludes"] = -1937948672] = "ConstructorExcludes";
  TransformFlags3[TransformFlags3["MethodOrAccessorExcludes"] = -2005057536] = "MethodOrAccessorExcludes";
  TransformFlags3[TransformFlags3["PropertyExcludes"] = -2013249536] = "PropertyExcludes";
  TransformFlags3[TransformFlags3["ClassExcludes"] = -2147344384] = "ClassExcludes";
  TransformFlags3[TransformFlags3["ModuleExcludes"] = -1941676032] = "ModuleExcludes";
  TransformFlags3[TransformFlags3["TypeExcludes"] = -2] = "TypeExcludes";
  TransformFlags3[TransformFlags3["ObjectLiteralExcludes"] = -2147278848] = "ObjectLiteralExcludes";
  TransformFlags3[TransformFlags3["ArrayLiteralOrCallOrNewExcludes"] = -2147450880] = "ArrayLiteralOrCallOrNewExcludes";
  TransformFlags3[TransformFlags3["VariableDeclarationListExcludes"] = -2146893824] = "VariableDeclarationListExcludes";
  TransformFlags3[TransformFlags3["ParameterExcludes"] = -2147483648 /* NodeExcludes */] = "ParameterExcludes";
  TransformFlags3[TransformFlags3["CatchClauseExcludes"] = -2147418112] = "CatchClauseExcludes";
  TransformFlags3[TransformFlags3["BindingPatternExcludes"] = -2147450880] = "BindingPatternExcludes";
  TransformFlags3[TransformFlags3["ContainsLexicalThisOrSuper"] = 134234112] = "ContainsLexicalThisOrSuper";
  TransformFlags3[TransformFlags3["PropertyNamePropagatingFlags"] = 134234112] = "PropertyNamePropagatingFlags";
  return TransformFlags3;
})(TransformFlags || {});
var SnippetKind = /* @__PURE__ */ ((SnippetKind3) => {
  SnippetKind3[SnippetKind3["TabStop"] = 0] = "TabStop";
  SnippetKind3[SnippetKind3["Placeholder"] = 1] = "Placeholder";
  SnippetKind3[SnippetKind3["Choice"] = 2] = "Choice";
  SnippetKind3[SnippetKind3["Variable"] = 3] = "Variable";
  return SnippetKind3;
})(SnippetKind || {});
var EmitFlags = /* @__PURE__ */ ((EmitFlags3) => {
  EmitFlags3[EmitFlags3["None"] = 0] = "None";
  EmitFlags3[EmitFlags3["SingleLine"] = 1] = "SingleLine";
  EmitFlags3[EmitFlags3["MultiLine"] = 2] = "MultiLine";
  EmitFlags3[EmitFlags3["AdviseOnEmitNode"] = 4] = "AdviseOnEmitNode";
  EmitFlags3[EmitFlags3["NoSubstitution"] = 8] = "NoSubstitution";
  EmitFlags3[EmitFlags3["CapturesThis"] = 16] = "CapturesThis";
  EmitFlags3[EmitFlags3["NoLeadingSourceMap"] = 32] = "NoLeadingSourceMap";
  EmitFlags3[EmitFlags3["NoTrailingSourceMap"] = 64] = "NoTrailingSourceMap";
  EmitFlags3[EmitFlags3["NoSourceMap"] = 96] = "NoSourceMap";
  EmitFlags3[EmitFlags3["NoNestedSourceMaps"] = 128] = "NoNestedSourceMaps";
  EmitFlags3[EmitFlags3["NoTokenLeadingSourceMaps"] = 256] = "NoTokenLeadingSourceMaps";
  EmitFlags3[EmitFlags3["NoTokenTrailingSourceMaps"] = 512] = "NoTokenTrailingSourceMaps";
  EmitFlags3[EmitFlags3["NoTokenSourceMaps"] = 768] = "NoTokenSourceMaps";
  EmitFlags3[EmitFlags3["NoLeadingComments"] = 1024] = "NoLeadingComments";
  EmitFlags3[EmitFlags3["NoTrailingComments"] = 2048] = "NoTrailingComments";
  EmitFlags3[EmitFlags3["NoComments"] = 3072] = "NoComments";
  EmitFlags3[EmitFlags3["NoNestedComments"] = 4096] = "NoNestedComments";
  EmitFlags3[EmitFlags3["HelperName"] = 8192] = "HelperName";
  EmitFlags3[EmitFlags3["ExportName"] = 16384] = "ExportName";
  EmitFlags3[EmitFlags3["LocalName"] = 32768] = "LocalName";
  EmitFlags3[EmitFlags3["InternalName"] = 65536] = "InternalName";
  EmitFlags3[EmitFlags3["Indented"] = 131072] = "Indented";
  EmitFlags3[EmitFlags3["NoIndentation"] = 262144] = "NoIndentation";
  EmitFlags3[EmitFlags3["AsyncFunctionBody"] = 524288] = "AsyncFunctionBody";
  EmitFlags3[EmitFlags3["ReuseTempVariableScope"] = 1048576] = "ReuseTempVariableScope";
  EmitFlags3[EmitFlags3["CustomPrologue"] = 2097152] = "CustomPrologue";
  EmitFlags3[EmitFlags3["NoHoisting"] = 4194304] = "NoHoisting";
  EmitFlags3[EmitFlags3["Iterator"] = 8388608] = "Iterator";
  EmitFlags3[EmitFlags3["NoAsciiEscaping"] = 16777216] = "NoAsciiEscaping";
  return EmitFlags3;
})(EmitFlags || {});
var LanguageFeatureMinimumTarget = {
  Classes: 2 /* ES2015 */,
  ForOf: 2 /* ES2015 */,
  Generators: 2 /* ES2015 */,
  Iteration: 2 /* ES2015 */,
  SpreadElements: 2 /* ES2015 */,
  RestElements: 2 /* ES2015 */,
  TaggedTemplates: 2 /* ES2015 */,
  DestructuringAssignment: 2 /* ES2015 */,
  BindingPatterns: 2 /* ES2015 */,
  ArrowFunctions: 2 /* ES2015 */,
  BlockScopedVariables: 2 /* ES2015 */,
  ObjectAssign: 2 /* ES2015 */,
  RegularExpressionFlagsUnicode: 2 /* ES2015 */,
  RegularExpressionFlagsSticky: 2 /* ES2015 */,
  Exponentiation: 3 /* ES2016 */,
  AsyncFunctions: 4 /* ES2017 */,
  ForAwaitOf: 5 /* ES2018 */,
  AsyncGenerators: 5 /* ES2018 */,
  AsyncIteration: 5 /* ES2018 */,
  ObjectSpreadRest: 5 /* ES2018 */,
  RegularExpressionFlagsDotAll: 5 /* ES2018 */,
  BindinglessCatch: 6 /* ES2019 */,
  BigInt: 7 /* ES2020 */,
  NullishCoalesce: 7 /* ES2020 */,
  OptionalChaining: 7 /* ES2020 */,
  LogicalAssignment: 8 /* ES2021 */,
  TopLevelAwait: 9 /* ES2022 */,
  ClassFields: 9 /* ES2022 */,
  PrivateNamesAndClassStaticBlocks: 9 /* ES2022 */,
  RegularExpressionFlagsHasIndices: 9 /* ES2022 */,
  ShebangComments: 10 /* ES2023 */,
  RegularExpressionFlagsUnicodeSets: 11 /* ES2024 */,
  UsingAndAwaitUsing: 99 /* ESNext */,
  ClassAndClassElementDecorators: 99 /* ESNext */
};
var commentPragmas = {
  "reference": {
    args: [
      { name: "types", optional: true, captureSpan: true },
      { name: "lib", optional: true, captureSpan: true },
      { name: "path", optional: true, captureSpan: true },
      { name: "no-default-lib", optional: true },
      { name: "resolution-mode", optional: true },
      { name: "preserve", optional: true }
    ],
    kind: 1 /* TripleSlashXML */
  },
  "amd-dependency": {
    args: [{ name: "path" }, { name: "name", optional: true }],
    kind: 1 /* TripleSlashXML */
  },
  "amd-module": {
    args: [{ name: "name" }],
    kind: 1 /* TripleSlashXML */
  },
  "ts-check": {
    kind: 2 /* SingleLine */
  },
  "ts-nocheck": {
    kind: 2 /* SingleLine */
  },
  "jsx": {
    args: [{ name: "factory" }],
    kind: 4 /* MultiLine */
  },
  "jsxfrag": {
    args: [{ name: "factory" }],
    kind: 4 /* MultiLine */
  },
  "jsximportsource": {
    args: [{ name: "factory" }],
    kind: 4 /* MultiLine */
  },
  "jsxruntime": {
    args: [{ name: "factory" }],
    kind: 4 /* MultiLine */
  }
};

// src/compiler/sys.ts
function generateDjb2Hash(data) {
  let acc = 5381;
  for (let i = 0; i < data.length; i++) {
    acc = (acc << 5) + acc + data.charCodeAt(i);
  }
  return acc.toString();
}
var PollingInterval = /* @__PURE__ */ ((PollingInterval3) => {
  PollingInterval3[PollingInterval3["High"] = 2e3] = "High";
  PollingInterval3[PollingInterval3["Medium"] = 500] = "Medium";
  PollingInterval3[PollingInterval3["Low"] = 250] = "Low";
  return PollingInterval3;
})(PollingInterval || {});
var missingFileModifiedTime = /* @__PURE__ */ new Date(0);
function getModifiedTime(host, fileName) {
  return host.getModifiedTime(fileName) || missingFileModifiedTime;
}
function createPollingIntervalBasedLevels(levels) {
  return {
    [250 /* Low */]: levels.Low,
    [500 /* Medium */]: levels.Medium,
    [2e3 /* High */]: levels.High
  };
}
var defaultChunkLevels = { Low: 32, Medium: 64, High: 256 };
var pollingChunkSize = createPollingIntervalBasedLevels(defaultChunkLevels);
var unchangedPollThresholds = createPollingIntervalBasedLevels(defaultChunkLevels);
function setCustomPollingValues(system) {
  if (!system.getEnvironmentVariable) {
    return;
  }
  const pollingIntervalChanged = setCustomLevels("TSC_WATCH_POLLINGINTERVAL", PollingInterval);
  pollingChunkSize = getCustomPollingBasedLevels("TSC_WATCH_POLLINGCHUNKSIZE", defaultChunkLevels) || pollingChunkSize;
  unchangedPollThresholds = getCustomPollingBasedLevels("TSC_WATCH_UNCHANGEDPOLLTHRESHOLDS", defaultChunkLevels) || unchangedPollThresholds;
  function getLevel(envVar, level) {
    return system.getEnvironmentVariable(`${envVar}_${level.toUpperCase()}`);
  }
  function getCustomLevels(baseVariable) {
    let customLevels;
    setCustomLevel("Low");
    setCustomLevel("Medium");
    setCustomLevel("High");
    return customLevels;
    function setCustomLevel(level) {
      const customLevel = getLevel(baseVariable, level);
      if (customLevel) {
        (customLevels || (customLevels = {}))[level] = Number(customLevel);
      }
    }
  }
  function setCustomLevels(baseVariable, levels) {
    const customLevels = getCustomLevels(baseVariable);
    if (customLevels) {
      setLevel("Low");
      setLevel("Medium");
      setLevel("High");
      return true;
    }
    return false;
    function setLevel(level) {
      levels[level] = customLevels[level] || levels[level];
    }
  }
  function getCustomPollingBasedLevels(baseVariable, defaultLevels) {
    const customLevels = getCustomLevels(baseVariable);
    return (pollingIntervalChanged || customLevels) && createPollingIntervalBasedLevels(customLevels ? { ...defaultLevels, ...customLevels } : defaultLevels);
  }
}
function pollWatchedFileQueue(host, queue, pollIndex, chunkSize, callbackOnWatchFileStat) {
  let definedValueCopyToIndex = pollIndex;
  for (let canVisit = queue.length; chunkSize && canVisit; nextPollIndex(), canVisit--) {
    const watchedFile = queue[pollIndex];
    if (!watchedFile) {
      continue;
    } else if (watchedFile.isClosed) {
      queue[pollIndex] = void 0;
      continue;
    }
    chunkSize--;
    const fileChanged = onWatchedFileStat(watchedFile, getModifiedTime(host, watchedFile.fileName));
    if (watchedFile.isClosed) {
      queue[pollIndex] = void 0;
      continue;
    }
    callbackOnWatchFileStat == null ? void 0 : callbackOnWatchFileStat(watchedFile, pollIndex, fileChanged);
    if (queue[pollIndex]) {
      if (definedValueCopyToIndex < pollIndex) {
        queue[definedValueCopyToIndex] = watchedFile;
        queue[pollIndex] = void 0;
      }
      definedValueCopyToIndex++;
    }
  }
  return pollIndex;
  function nextPollIndex() {
    pollIndex++;
    if (pollIndex === queue.length) {
      if (definedValueCopyToIndex < pollIndex) {
        queue.length = definedValueCopyToIndex;
      }
      pollIndex = 0;
      definedValueCopyToIndex = 0;
    }
  }
}
function createDynamicPriorityPollingWatchFile(host) {
  const watchedFiles = [];
  const changedFilesInLastPoll = [];
  const lowPollingIntervalQueue = createPollingIntervalQueue(250 /* Low */);
  const mediumPollingIntervalQueue = createPollingIntervalQueue(500 /* Medium */);
  const highPollingIntervalQueue = createPollingIntervalQueue(2e3 /* High */);
  return watchFile2;
  function watchFile2(fileName, callback, defaultPollingInterval) {
    const file = {
      fileName,
      callback,
      unchangedPolls: 0,
      mtime: getModifiedTime(host, fileName)
    };
    watchedFiles.push(file);
    addToPollingIntervalQueue(file, defaultPollingInterval);
    return {
      close: () => {
        file.isClosed = true;
        unorderedRemoveItem(watchedFiles, file);
      }
    };
  }
  function createPollingIntervalQueue(pollingInterval) {
    const queue = [];
    queue.pollingInterval = pollingInterval;
    queue.pollIndex = 0;
    queue.pollScheduled = false;
    return queue;
  }
  function pollPollingIntervalQueue(_timeoutType, queue) {
    queue.pollIndex = pollQueue(queue, queue.pollingInterval, queue.pollIndex, pollingChunkSize[queue.pollingInterval]);
    if (queue.length) {
      scheduleNextPoll(queue.pollingInterval);
    } else {
      Debug.assert(queue.pollIndex === 0);
      queue.pollScheduled = false;
    }
  }
  function pollLowPollingIntervalQueue(_timeoutType, queue) {
    pollQueue(
      changedFilesInLastPoll,
      250 /* Low */,
      /*pollIndex*/
      0,
      changedFilesInLastPoll.length
    );
    pollPollingIntervalQueue(_timeoutType, queue);
    if (!queue.pollScheduled && changedFilesInLastPoll.length) {
      scheduleNextPoll(250 /* Low */);
    }
  }
  function pollQueue(queue, pollingInterval, pollIndex, chunkSize) {
    return pollWatchedFileQueue(
      host,
      queue,
      pollIndex,
      chunkSize,
      onWatchFileStat
    );
    function onWatchFileStat(watchedFile, pollIndex2, fileChanged) {
      if (fileChanged) {
        watchedFile.unchangedPolls = 0;
        if (queue !== changedFilesInLastPoll) {
          queue[pollIndex2] = void 0;
          addChangedFileToLowPollingIntervalQueue(watchedFile);
        }
      } else if (watchedFile.unchangedPolls !== unchangedPollThresholds[pollingInterval]) {
        watchedFile.unchangedPolls++;
      } else if (queue === changedFilesInLastPoll) {
        watchedFile.unchangedPolls = 1;
        queue[pollIndex2] = void 0;
        addToPollingIntervalQueue(watchedFile, 250 /* Low */);
      } else if (pollingInterval !== 2e3 /* High */) {
        watchedFile.unchangedPolls++;
        queue[pollIndex2] = void 0;
        addToPollingIntervalQueue(watchedFile, pollingInterval === 250 /* Low */ ? 500 /* Medium */ : 2e3 /* High */);
      }
    }
  }
  function pollingIntervalQueue(pollingInterval) {
    switch (pollingInterval) {
      case 250 /* Low */:
        return lowPollingIntervalQueue;
      case 500 /* Medium */:
        return mediumPollingIntervalQueue;
      case 2e3 /* High */:
        return highPollingIntervalQueue;
    }
  }
  function addToPollingIntervalQueue(file, pollingInterval) {
    pollingIntervalQueue(pollingInterval).push(file);
    scheduleNextPollIfNotAlreadyScheduled(pollingInterval);
  }
  function addChangedFileToLowPollingIntervalQueue(file) {
    changedFilesInLastPoll.push(file);
    scheduleNextPollIfNotAlreadyScheduled(250 /* Low */);
  }
  function scheduleNextPollIfNotAlreadyScheduled(pollingInterval) {
    if (!pollingIntervalQueue(pollingInterval).pollScheduled) {
      scheduleNextPoll(pollingInterval);
    }
  }
  function scheduleNextPoll(pollingInterval) {
    pollingIntervalQueue(pollingInterval).pollScheduled = host.setTimeout(pollingInterval === 250 /* Low */ ? pollLowPollingIntervalQueue : pollPollingIntervalQueue, pollingInterval, pollingInterval === 250 /* Low */ ? "pollLowPollingIntervalQueue" : "pollPollingIntervalQueue", pollingIntervalQueue(pollingInterval));
  }
}
function createUseFsEventsOnParentDirectoryWatchFile(fsWatch, useCaseSensitiveFileNames2, getModifiedTime3, fsWatchWithTimestamp) {
  const fileWatcherCallbacks = createMultiMap();
  const fileTimestamps = fsWatchWithTimestamp ? /* @__PURE__ */ new Map() : void 0;
  const dirWatchers = /* @__PURE__ */ new Map();
  const toCanonicalName = createGetCanonicalFileName(useCaseSensitiveFileNames2);
  return nonPollingWatchFile;
  function nonPollingWatchFile(fileName, callback, _pollingInterval, fallbackOptions) {
    const filePath = toCanonicalName(fileName);
    if (fileWatcherCallbacks.add(filePath, callback).length === 1 && fileTimestamps) {
      fileTimestamps.set(filePath, getModifiedTime3(fileName) || missingFileModifiedTime);
    }
    const dirPath = getDirectoryPath(filePath) || ".";
    const watcher = dirWatchers.get(dirPath) || createDirectoryWatcher(getDirectoryPath(fileName) || ".", dirPath, fallbackOptions);
    watcher.referenceCount++;
    return {
      close: () => {
        if (watcher.referenceCount === 1) {
          watcher.close();
          dirWatchers.delete(dirPath);
        } else {
          watcher.referenceCount--;
        }
        fileWatcherCallbacks.remove(filePath, callback);
      }
    };
  }
  function createDirectoryWatcher(dirName, dirPath, fallbackOptions) {
    const watcher = fsWatch(
      dirName,
      1 /* Directory */,
      (eventName, relativeFileName) => {
        if (!isString(relativeFileName)) return;
        const fileName = getNormalizedAbsolutePath(relativeFileName, dirName);
        const filePath = toCanonicalName(fileName);
        const callbacks = fileName && fileWatcherCallbacks.get(filePath);
        if (callbacks) {
          let currentModifiedTime;
          let eventKind = 1 /* Changed */;
          if (fileTimestamps) {
            const existingTime = fileTimestamps.get(filePath);
            if (eventName === "change") {
              currentModifiedTime = getModifiedTime3(fileName) || missingFileModifiedTime;
              if (currentModifiedTime.getTime() === existingTime.getTime()) return;
            }
            currentModifiedTime || (currentModifiedTime = getModifiedTime3(fileName) || missingFileModifiedTime);
            fileTimestamps.set(filePath, currentModifiedTime);
            if (existingTime === missingFileModifiedTime) eventKind = 0 /* Created */;
            else if (currentModifiedTime === missingFileModifiedTime) eventKind = 2 /* Deleted */;
          }
          for (const fileCallback of callbacks) {
            fileCallback(fileName, eventKind, currentModifiedTime);
          }
        }
      },
      /*recursive*/
      false,
      500 /* Medium */,
      fallbackOptions
    );
    watcher.referenceCount = 0;
    dirWatchers.set(dirPath, watcher);
    return watcher;
  }
}
function createFixedChunkSizePollingWatchFile(host) {
  const watchedFiles = [];
  let pollIndex = 0;
  let pollScheduled;
  return watchFile2;
  function watchFile2(fileName, callback) {
    const file = {
      fileName,
      callback,
      mtime: getModifiedTime(host, fileName)
    };
    watchedFiles.push(file);
    scheduleNextPoll();
    return {
      close: () => {
        file.isClosed = true;
        unorderedRemoveItem(watchedFiles, file);
      }
    };
  }
  function pollQueue() {
    pollScheduled = void 0;
    pollIndex = pollWatchedFileQueue(host, watchedFiles, pollIndex, pollingChunkSize[250 /* Low */]);
    scheduleNextPoll();
  }
  function scheduleNextPoll() {
    if (!watchedFiles.length || pollScheduled) return;
    pollScheduled = host.setTimeout(pollQueue, 2e3 /* High */, "pollQueue");
  }
}
function createSingleWatcherPerName(cache, useCaseSensitiveFileNames2, name, callback, createWatcher) {
  const toCanonicalFileName = createGetCanonicalFileName(useCaseSensitiveFileNames2);
  const path = toCanonicalFileName(name);
  const existing = cache.get(path);
  if (existing) {
    existing.callbacks.push(callback);
  } else {
    cache.set(path, {
      watcher: createWatcher(
        // Cant infer types correctly so lets satisfy checker
        (param1, param2, param3) => {
          var _a;
          return (_a = cache.get(path)) == null ? void 0 : _a.callbacks.slice().forEach((cb) => cb(param1, param2, param3));
        }
      ),
      callbacks: [callback]
    });
  }
  return {
    close: () => {
      const watcher = cache.get(path);
      if (!watcher) return;
      if (!orderedRemoveItem(watcher.callbacks, callback) || watcher.callbacks.length) return;
      cache.delete(path);
      closeFileWatcherOf(watcher);
    }
  };
}
function onWatchedFileStat(watchedFile, modifiedTime) {
  const oldTime = watchedFile.mtime.getTime();
  const newTime = modifiedTime.getTime();
  if (oldTime !== newTime) {
    watchedFile.mtime = modifiedTime;
    watchedFile.callback(watchedFile.fileName, getFileWatcherEventKind(oldTime, newTime), modifiedTime);
    return true;
  }
  return false;
}
function getFileWatcherEventKind(oldTime, newTime) {
  return oldTime === 0 ? 0 /* Created */ : newTime === 0 ? 2 /* Deleted */ : 1 /* Changed */;
}
var ignoredPaths = ["/node_modules/.", "/.git", "/.#"];
var curSysLog = noop;
function sysLog(s) {
  return curSysLog(s);
}
function setSysLog(logger) {
  curSysLog = logger;
}
function createDirectoryWatcherSupportingRecursive({
  watchDirectory,
  useCaseSensitiveFileNames: useCaseSensitiveFileNames2,
  getCurrentDirectory,
  getAccessibleSortedChildDirectories,
  fileSystemEntryExists,
  realpath,
  setTimeout: setTimeout2,
  clearTimeout: clearTimeout2
}) {
  const cache = /* @__PURE__ */ new Map();
  const callbackCache = createMultiMap();
  const cacheToUpdateChildWatches = /* @__PURE__ */ new Map();
  let timerToUpdateChildWatches;
  const filePathComparer = getStringComparer(!useCaseSensitiveFileNames2);
  const toCanonicalFilePath = createGetCanonicalFileName(useCaseSensitiveFileNames2);
  return (dirName, callback, recursive, options) => recursive ? createDirectoryWatcher(dirName, options, callback) : watchDirectory(dirName, callback, recursive, options);
  function createDirectoryWatcher(dirName, options, callback, link) {
    const dirPath = toCanonicalFilePath(dirName);
    let directoryWatcher = cache.get(dirPath);
    if (directoryWatcher) {
      directoryWatcher.refCount++;
    } else {
      directoryWatcher = {
        watcher: watchDirectory(
          dirName,
          (fileName) => {
            var _a;
            if (isIgnoredPath(fileName, options)) return;
            if (options == null ? void 0 : options.synchronousWatchDirectory) {
              if (!((_a = cache.get(dirPath)) == null ? void 0 : _a.targetWatcher)) invokeCallbacks(dirName, dirPath, fileName);
              updateChildWatches(dirName, dirPath, options);
            } else {
              nonSyncUpdateChildWatches(dirName, dirPath, fileName, options);
            }
          },
          /*recursive*/
          false,
          options
        ),
        refCount: 1,
        childWatches: emptyArray,
        targetWatcher: void 0,
        links: void 0
      };
      cache.set(dirPath, directoryWatcher);
      updateChildWatches(dirName, dirPath, options);
    }
    if (link) (directoryWatcher.links ?? (directoryWatcher.links = /* @__PURE__ */ new Set())).add(link);
    const callbackToAdd = callback && { dirName, callback };
    if (callbackToAdd) {
      callbackCache.add(dirPath, callbackToAdd);
    }
    return {
      dirName,
      close: () => {
        var _a;
        const directoryWatcher2 = Debug.checkDefined(cache.get(dirPath));
        if (callbackToAdd) callbackCache.remove(dirPath, callbackToAdd);
        if (link) (_a = directoryWatcher2.links) == null ? void 0 : _a.delete(link);
        directoryWatcher2.refCount--;
        if (directoryWatcher2.refCount) return;
        cache.delete(dirPath);
        directoryWatcher2.links = void 0;
        closeFileWatcherOf(directoryWatcher2);
        closeTargetWatcher(directoryWatcher2);
        directoryWatcher2.childWatches.forEach(closeFileWatcher);
      }
    };
  }
  function invokeCallbacks(dirName, dirPath, fileNameOrInvokeMap, fileNames) {
    var _a, _b;
    let fileName;
    let invokeMap;
    if (isString(fileNameOrInvokeMap)) {
      fileName = fileNameOrInvokeMap;
    } else {
      invokeMap = fileNameOrInvokeMap;
    }
    callbackCache.forEach((callbacks, rootDirName) => {
      if (invokeMap && invokeMap.get(rootDirName) === true) return;
      if (rootDirName === dirPath || startsWith(dirPath, rootDirName) && dirPath[rootDirName.length] === directorySeparator) {
        if (invokeMap) {
          if (fileNames) {
            const existing = invokeMap.get(rootDirName);
            if (existing) {
              existing.push(...fileNames);
            } else {
              invokeMap.set(rootDirName, fileNames.slice());
            }
          } else {
            invokeMap.set(rootDirName, true);
          }
        } else {
          callbacks.forEach(({ callback }) => callback(fileName));
        }
      }
    });
    (_b = (_a = cache.get(dirPath)) == null ? void 0 : _a.links) == null ? void 0 : _b.forEach((link) => {
      const toPathInLink = (fileName2) => combinePaths(link, getRelativePathFromDirectory(dirName, fileName2, toCanonicalFilePath));
      if (invokeMap) {
        invokeCallbacks(link, toCanonicalFilePath(link), invokeMap, fileNames == null ? void 0 : fileNames.map(toPathInLink));
      } else {
        invokeCallbacks(link, toCanonicalFilePath(link), toPathInLink(fileName));
      }
    });
  }
  function nonSyncUpdateChildWatches(dirName, dirPath, fileName, options) {
    const parentWatcher = cache.get(dirPath);
    if (parentWatcher && fileSystemEntryExists(dirName, 1 /* Directory */)) {
      scheduleUpdateChildWatches(dirName, dirPath, fileName, options);
      return;
    }
    invokeCallbacks(dirName, dirPath, fileName);
    closeTargetWatcher(parentWatcher);
    removeChildWatches(parentWatcher);
  }
  function scheduleUpdateChildWatches(dirName, dirPath, fileName, options) {
    const existing = cacheToUpdateChildWatches.get(dirPath);
    if (existing) {
      existing.fileNames.push(fileName);
    } else {
      cacheToUpdateChildWatches.set(dirPath, { dirName, options, fileNames: [fileName] });
    }
    if (timerToUpdateChildWatches) {
      clearTimeout2(timerToUpdateChildWatches);
      timerToUpdateChildWatches = void 0;
    }
    timerToUpdateChildWatches = setTimeout2(onTimerToUpdateChildWatches, 1e3, "timerToUpdateChildWatches");
  }
  function onTimerToUpdateChildWatches() {
    var _a;
    timerToUpdateChildWatches = void 0;
    sysLog(`sysLog:: onTimerToUpdateChildWatches:: ${cacheToUpdateChildWatches.size}`);
    const start = timestamp();
    const invokeMap = /* @__PURE__ */ new Map();
    while (!timerToUpdateChildWatches && cacheToUpdateChildWatches.size) {
      const result = cacheToUpdateChildWatches.entries().next();
      Debug.assert(!result.done);
      const { value: [dirPath, { dirName, options, fileNames }] } = result;
      cacheToUpdateChildWatches.delete(dirPath);
      const hasChanges = updateChildWatches(dirName, dirPath, options);
      if (!((_a = cache.get(dirPath)) == null ? void 0 : _a.targetWatcher)) invokeCallbacks(dirName, dirPath, invokeMap, hasChanges ? void 0 : fileNames);
    }
    sysLog(`sysLog:: invokingWatchers:: Elapsed:: ${timestamp() - start}ms:: ${cacheToUpdateChildWatches.size}`);
    callbackCache.forEach((callbacks, rootDirName) => {
      const existing = invokeMap.get(rootDirName);
      if (existing) {
        callbacks.forEach(({ callback, dirName }) => {
          if (isArray(existing)) {
            existing.forEach(callback);
          } else {
            callback(dirName);
          }
        });
      }
    });
    const elapsed = timestamp() - start;
    sysLog(`sysLog:: Elapsed:: ${elapsed}ms:: onTimerToUpdateChildWatches:: ${cacheToUpdateChildWatches.size} ${timerToUpdateChildWatches}`);
  }
  function removeChildWatches(parentWatcher) {
    if (!parentWatcher) return;
    const existingChildWatches = parentWatcher.childWatches;
    parentWatcher.childWatches = emptyArray;
    for (const childWatcher of existingChildWatches) {
      childWatcher.close();
      removeChildWatches(cache.get(toCanonicalFilePath(childWatcher.dirName)));
    }
  }
  function closeTargetWatcher(watcher) {
    if (watcher == null ? void 0 : watcher.targetWatcher) {
      watcher.targetWatcher.close();
      watcher.targetWatcher = void 0;
    }
  }
  function updateChildWatches(parentDir, parentDirPath, options) {
    const parentWatcher = cache.get(parentDirPath);
    if (!parentWatcher) return false;
    const target = normalizePath(realpath(parentDir));
    let hasChanges;
    let newChildWatches;
    if (filePathComparer(target, parentDir) === 0 /* EqualTo */) {
      hasChanges = enumerateInsertsAndDeletes(
        fileSystemEntryExists(parentDir, 1 /* Directory */) ? mapDefined(getAccessibleSortedChildDirectories(parentDir), (child) => {
          const childFullName = getNormalizedAbsolutePath(child, parentDir);
          return !isIgnoredPath(childFullName, options) && filePathComparer(childFullName, normalizePath(realpath(childFullName))) === 0 /* EqualTo */ ? childFullName : void 0;
        }) : emptyArray,
        parentWatcher.childWatches,
        (child, childWatcher) => filePathComparer(child, childWatcher.dirName),
        createAndAddChildDirectoryWatcher,
        closeFileWatcher,
        addChildDirectoryWatcher
      );
    } else if (parentWatcher.targetWatcher && filePathComparer(target, parentWatcher.targetWatcher.dirName) === 0 /* EqualTo */) {
      hasChanges = false;
      Debug.assert(parentWatcher.childWatches === emptyArray);
    } else {
      closeTargetWatcher(parentWatcher);
      parentWatcher.targetWatcher = createDirectoryWatcher(
        target,
        options,
        /*callback*/
        void 0,
        parentDir
      );
      parentWatcher.childWatches.forEach(closeFileWatcher);
      hasChanges = true;
    }
    parentWatcher.childWatches = newChildWatches || emptyArray;
    return hasChanges;
    function createAndAddChildDirectoryWatcher(childName) {
      const result = createDirectoryWatcher(childName, options);
      addChildDirectoryWatcher(result);
    }
    function addChildDirectoryWatcher(childWatcher) {
      (newChildWatches || (newChildWatches = [])).push(childWatcher);
    }
  }
  function isIgnoredPath(path, options) {
    return some(ignoredPaths, (searchPath) => isInPath(path, searchPath)) || isIgnoredByWatchOptions(path, options, useCaseSensitiveFileNames2, getCurrentDirectory);
  }
  function isInPath(path, searchPath) {
    if (path.includes(searchPath)) return true;
    if (useCaseSensitiveFileNames2) return false;
    return toCanonicalFilePath(path).includes(searchPath);
  }
}
function createFileWatcherCallback(callback) {
  return (_fileName, eventKind, modifiedTime) => callback(eventKind === 1 /* Changed */ ? "change" : "rename", "", modifiedTime);
}
function createFsWatchCallbackForFileWatcherCallback(fileName, callback, getModifiedTime3) {
  return (eventName, _relativeFileName, modifiedTime) => {
    if (eventName === "rename") {
      modifiedTime || (modifiedTime = getModifiedTime3(fileName) || missingFileModifiedTime);
      callback(fileName, modifiedTime !== missingFileModifiedTime ? 0 /* Created */ : 2 /* Deleted */, modifiedTime);
    } else {
      callback(fileName, 1 /* Changed */, modifiedTime);
    }
  };
}
function isIgnoredByWatchOptions(pathToCheck, options, useCaseSensitiveFileNames2, getCurrentDirectory) {
  return ((options == null ? void 0 : options.excludeDirectories) || (options == null ? void 0 : options.excludeFiles)) && (matchesExclude(pathToCheck, options == null ? void 0 : options.excludeFiles, useCaseSensitiveFileNames2, getCurrentDirectory()) || matchesExclude(pathToCheck, options == null ? void 0 : options.excludeDirectories, useCaseSensitiveFileNames2, getCurrentDirectory()));
}
function createFsWatchCallbackForDirectoryWatcherCallback(directoryName, callback, options, useCaseSensitiveFileNames2, getCurrentDirectory) {
  return (eventName, relativeFileName) => {
    if (eventName === "rename") {
      const fileName = !relativeFileName ? directoryName : normalizePath(combinePaths(directoryName, relativeFileName));
      if (!relativeFileName || !isIgnoredByWatchOptions(fileName, options, useCaseSensitiveFileNames2, getCurrentDirectory)) {
        callback(fileName);
      }
    }
  };
}
function createSystemWatchFunctions({
  pollingWatchFileWorker,
  getModifiedTime: getModifiedTime3,
  setTimeout: setTimeout2,
  clearTimeout: clearTimeout2,
  fsWatchWorker,
  fileSystemEntryExists,
  useCaseSensitiveFileNames: useCaseSensitiveFileNames2,
  getCurrentDirectory,
  fsSupportsRecursiveFsWatch,
  getAccessibleSortedChildDirectories,
  realpath,
  tscWatchFile,
  useNonPollingWatchers,
  tscWatchDirectory,
  inodeWatching,
  fsWatchWithTimestamp,
  sysLog: sysLog2
}) {
  const pollingWatches = /* @__PURE__ */ new Map();
  const fsWatches = /* @__PURE__ */ new Map();
  const fsWatchesRecursive = /* @__PURE__ */ new Map();
  let dynamicPollingWatchFile;
  let fixedChunkSizePollingWatchFile;
  let nonPollingWatchFile;
  let hostRecursiveDirectoryWatcher;
  let hitSystemWatcherLimit = false;
  return {
    watchFile: watchFile2,
    watchDirectory
  };
  function watchFile2(fileName, callback, pollingInterval, options) {
    options = updateOptionsForWatchFile(options, useNonPollingWatchers);
    const watchFileKind = Debug.checkDefined(options.watchFile);
    switch (watchFileKind) {
      case 0 /* FixedPollingInterval */:
        return pollingWatchFile(
          fileName,
          callback,
          250 /* Low */,
          /*options*/
          void 0
        );
      case 1 /* PriorityPollingInterval */:
        return pollingWatchFile(
          fileName,
          callback,
          pollingInterval,
          /*options*/
          void 0
        );
      case 2 /* DynamicPriorityPolling */:
        return ensureDynamicPollingWatchFile()(
          fileName,
          callback,
          pollingInterval,
          /*options*/
          void 0
        );
      case 3 /* FixedChunkSizePolling */:
        return ensureFixedChunkSizePollingWatchFile()(
          fileName,
          callback,
          /* pollingInterval */
          void 0,
          /*options*/
          void 0
        );
      case 4 /* UseFsEvents */:
        return fsWatch(
          fileName,
          0 /* File */,
          createFsWatchCallbackForFileWatcherCallback(fileName, callback, getModifiedTime3),
          /*recursive*/
          false,
          pollingInterval,
          getFallbackOptions(options)
        );
      case 5 /* UseFsEventsOnParentDirectory */:
        if (!nonPollingWatchFile) {
          nonPollingWatchFile = createUseFsEventsOnParentDirectoryWatchFile(fsWatch, useCaseSensitiveFileNames2, getModifiedTime3, fsWatchWithTimestamp);
        }
        return nonPollingWatchFile(fileName, callback, pollingInterval, getFallbackOptions(options));
      default:
        Debug.assertNever(watchFileKind);
    }
  }
  function ensureDynamicPollingWatchFile() {
    return dynamicPollingWatchFile || (dynamicPollingWatchFile = createDynamicPriorityPollingWatchFile({ getModifiedTime: getModifiedTime3, setTimeout: setTimeout2 }));
  }
  function ensureFixedChunkSizePollingWatchFile() {
    return fixedChunkSizePollingWatchFile || (fixedChunkSizePollingWatchFile = createFixedChunkSizePollingWatchFile({ getModifiedTime: getModifiedTime3, setTimeout: setTimeout2 }));
  }
  function updateOptionsForWatchFile(options, useNonPollingWatchers2) {
    if (options && options.watchFile !== void 0) return options;
    switch (tscWatchFile) {
      case "PriorityPollingInterval":
        return { watchFile: 1 /* PriorityPollingInterval */ };
      case "DynamicPriorityPolling":
        return { watchFile: 2 /* DynamicPriorityPolling */ };
      case "UseFsEvents":
        return generateWatchFileOptions(4 /* UseFsEvents */, 1 /* PriorityInterval */, options);
      case "UseFsEventsWithFallbackDynamicPolling":
        return generateWatchFileOptions(4 /* UseFsEvents */, 2 /* DynamicPriority */, options);
      case "UseFsEventsOnParentDirectory":
        useNonPollingWatchers2 = true;
      // fall through
      default:
        return useNonPollingWatchers2 ? (
          // Use notifications from FS to watch with falling back to fs.watchFile
          generateWatchFileOptions(5 /* UseFsEventsOnParentDirectory */, 1 /* PriorityInterval */, options)
        ) : (
          // Default to using fs events
          { watchFile: 4 /* UseFsEvents */ }
        );
    }
  }
  function generateWatchFileOptions(watchFile3, fallbackPolling, options) {
    const defaultFallbackPolling = options == null ? void 0 : options.fallbackPolling;
    return {
      watchFile: watchFile3,
      fallbackPolling: defaultFallbackPolling === void 0 ? fallbackPolling : defaultFallbackPolling
    };
  }
  function watchDirectory(directoryName, callback, recursive, options) {
    if (fsSupportsRecursiveFsWatch) {
      return fsWatch(
        directoryName,
        1 /* Directory */,
        createFsWatchCallbackForDirectoryWatcherCallback(directoryName, callback, options, useCaseSensitiveFileNames2, getCurrentDirectory),
        recursive,
        500 /* Medium */,
        getFallbackOptions(options)
      );
    }
    if (!hostRecursiveDirectoryWatcher) {
      hostRecursiveDirectoryWatcher = createDirectoryWatcherSupportingRecursive({
        useCaseSensitiveFileNames: useCaseSensitiveFileNames2,
        getCurrentDirectory,
        fileSystemEntryExists,
        getAccessibleSortedChildDirectories,
        watchDirectory: nonRecursiveWatchDirectory,
        realpath,
        setTimeout: setTimeout2,
        clearTimeout: clearTimeout2
      });
    }
    return hostRecursiveDirectoryWatcher(directoryName, callback, recursive, options);
  }
  function nonRecursiveWatchDirectory(directoryName, callback, recursive, options) {
    Debug.assert(!recursive);
    const watchDirectoryOptions = updateOptionsForWatchDirectory(options);
    const watchDirectoryKind = Debug.checkDefined(watchDirectoryOptions.watchDirectory);
    switch (watchDirectoryKind) {
      case 1 /* FixedPollingInterval */:
        return pollingWatchFile(
          directoryName,
          () => callback(directoryName),
          500 /* Medium */,
          /*options*/
          void 0
        );
      case 2 /* DynamicPriorityPolling */:
        return ensureDynamicPollingWatchFile()(
          directoryName,
          () => callback(directoryName),
          500 /* Medium */,
          /*options*/
          void 0
        );
      case 3 /* FixedChunkSizePolling */:
        return ensureFixedChunkSizePollingWatchFile()(
          directoryName,
          () => callback(directoryName),
          /* pollingInterval */
          void 0,
          /*options*/
          void 0
        );
      case 0 /* UseFsEvents */:
        return fsWatch(
          directoryName,
          1 /* Directory */,
          createFsWatchCallbackForDirectoryWatcherCallback(directoryName, callback, options, useCaseSensitiveFileNames2, getCurrentDirectory),
          recursive,
          500 /* Medium */,
          getFallbackOptions(watchDirectoryOptions)
        );
      default:
        Debug.assertNever(watchDirectoryKind);
    }
  }
  function updateOptionsForWatchDirectory(options) {
    if (options && options.watchDirectory !== void 0) return options;
    switch (tscWatchDirectory) {
      case "RecursiveDirectoryUsingFsWatchFile":
        return { watchDirectory: 1 /* FixedPollingInterval */ };
      case "RecursiveDirectoryUsingDynamicPriorityPolling":
        return { watchDirectory: 2 /* DynamicPriorityPolling */ };
      default:
        const defaultFallbackPolling = options == null ? void 0 : options.fallbackPolling;
        return {
          watchDirectory: 0 /* UseFsEvents */,
          fallbackPolling: defaultFallbackPolling !== void 0 ? defaultFallbackPolling : void 0
        };
    }
  }
  function pollingWatchFile(fileName, callback, pollingInterval, options) {
    return createSingleWatcherPerName(
      pollingWatches,
      useCaseSensitiveFileNames2,
      fileName,
      callback,
      (cb) => pollingWatchFileWorker(fileName, cb, pollingInterval, options)
    );
  }
  function fsWatch(fileOrDirectory, entryKind, callback, recursive, fallbackPollingInterval, fallbackOptions) {
    return createSingleWatcherPerName(
      recursive ? fsWatchesRecursive : fsWatches,
      useCaseSensitiveFileNames2,
      fileOrDirectory,
      callback,
      (cb) => fsWatchHandlingExistenceOnHost(fileOrDirectory, entryKind, cb, recursive, fallbackPollingInterval, fallbackOptions)
    );
  }
  function fsWatchHandlingExistenceOnHost(fileOrDirectory, entryKind, callback, recursive, fallbackPollingInterval, fallbackOptions) {
    let lastDirectoryPartWithDirectorySeparator;
    let lastDirectoryPart;
    if (inodeWatching) {
      lastDirectoryPartWithDirectorySeparator = fileOrDirectory.substring(fileOrDirectory.lastIndexOf(directorySeparator));
      lastDirectoryPart = lastDirectoryPartWithDirectorySeparator.slice(directorySeparator.length);
    }
    let watcher = !fileSystemEntryExists(fileOrDirectory, entryKind) ? watchMissingFileSystemEntry() : watchPresentFileSystemEntry();
    return {
      close: () => {
        if (watcher) {
          watcher.close();
          watcher = void 0;
        }
      }
    };
    function updateWatcher(createWatcher) {
      if (watcher) {
        sysLog2(`sysLog:: ${fileOrDirectory}:: Changing watcher to ${createWatcher === watchPresentFileSystemEntry ? "Present" : "Missing"}FileSystemEntryWatcher`);
        watcher.close();
        watcher = createWatcher();
      }
    }
    function watchPresentFileSystemEntry() {
      if (hitSystemWatcherLimit) {
        sysLog2(`sysLog:: ${fileOrDirectory}:: Defaulting to watchFile`);
        return watchPresentFileSystemEntryWithFsWatchFile();
      }
      try {
        const presentWatcher = (entryKind === 1 /* Directory */ || !fsWatchWithTimestamp ? fsWatchWorker : fsWatchWorkerHandlingTimestamp)(
          fileOrDirectory,
          recursive,
          inodeWatching ? callbackChangingToMissingFileSystemEntry : callback
        );
        presentWatcher.on("error", () => {
          callback("rename", "");
          updateWatcher(watchMissingFileSystemEntry);
        });
        return presentWatcher;
      } catch (e) {
        hitSystemWatcherLimit || (hitSystemWatcherLimit = e.code === "ENOSPC");
        sysLog2(`sysLog:: ${fileOrDirectory}:: Changing to watchFile`);
        return watchPresentFileSystemEntryWithFsWatchFile();
      }
    }
    function callbackChangingToMissingFileSystemEntry(event, relativeName) {
      let originalRelativeName;
      if (relativeName && endsWith(relativeName, "~")) {
        originalRelativeName = relativeName;
        relativeName = relativeName.slice(0, relativeName.length - 1);
      }
      if (event === "rename" && (!relativeName || relativeName === lastDirectoryPart || endsWith(relativeName, lastDirectoryPartWithDirectorySeparator))) {
        const modifiedTime = getModifiedTime3(fileOrDirectory) || missingFileModifiedTime;
        if (originalRelativeName) callback(event, originalRelativeName, modifiedTime);
        callback(event, relativeName, modifiedTime);
        if (inodeWatching) {
          updateWatcher(modifiedTime === missingFileModifiedTime ? watchMissingFileSystemEntry : watchPresentFileSystemEntry);
        } else if (modifiedTime === missingFileModifiedTime) {
          updateWatcher(watchMissingFileSystemEntry);
        }
      } else {
        if (originalRelativeName) callback(event, originalRelativeName);
        callback(event, relativeName);
      }
    }
    function watchPresentFileSystemEntryWithFsWatchFile() {
      return watchFile2(
        fileOrDirectory,
        createFileWatcherCallback(callback),
        fallbackPollingInterval,
        fallbackOptions
      );
    }
    function watchMissingFileSystemEntry() {
      return watchFile2(
        fileOrDirectory,
        (_fileName, eventKind, modifiedTime) => {
          if (eventKind === 0 /* Created */) {
            modifiedTime || (modifiedTime = getModifiedTime3(fileOrDirectory) || missingFileModifiedTime);
            if (modifiedTime !== missingFileModifiedTime) {
              callback("rename", "", modifiedTime);
              updateWatcher(watchPresentFileSystemEntry);
            }
          }
        },
        fallbackPollingInterval,
        fallbackOptions
      );
    }
  }
  function fsWatchWorkerHandlingTimestamp(fileOrDirectory, recursive, callback) {
    let modifiedTime = getModifiedTime3(fileOrDirectory) || missingFileModifiedTime;
    return fsWatchWorker(fileOrDirectory, recursive, (eventName, relativeFileName, currentModifiedTime) => {
      if (eventName === "change") {
        currentModifiedTime || (currentModifiedTime = getModifiedTime3(fileOrDirectory) || missingFileModifiedTime);
        if (currentModifiedTime.getTime() === modifiedTime.getTime()) return;
      }
      modifiedTime = currentModifiedTime || getModifiedTime3(fileOrDirectory) || missingFileModifiedTime;
      callback(eventName, relativeFileName, modifiedTime);
    });
  }
}
function patchWriteFileEnsuringDirectory(sys2) {
  const originalWriteFile = sys2.writeFile;
  sys2.writeFile = (path, data, writeBom) => writeFileEnsuringDirectories(
    path,
    data,
    !!writeBom,
    (path2, data2, writeByteOrderMark) => originalWriteFile.call(sys2, path2, data2, writeByteOrderMark),
    (path2) => sys2.createDirectory(path2),
    (path2) => sys2.directoryExists(path2)
  );
}
var sys = (() => {
  const byteOrderMarkIndicator = "\uFEFF";
  function getNodeSystem() {
    const nativePattern = /^native |^\([^)]+\)$|^(?:internal[\\/]|[\w\s]+(?:\.js)?$)/;
    const _fs = require("fs");
    const _path = require("path");
    const _os = require("os");
    let _crypto;
    try {
      _crypto = require("crypto");
    } catch {
      _crypto = void 0;
    }
    let activeSession;
    let profilePath = "./profile.cpuprofile";
    const isMacOs = process.platform === "darwin";
    const isLinuxOrMacOs = process.platform === "linux" || isMacOs;
    const statSyncOptions = { throwIfNoEntry: false };
    const platform = _os.platform();
    const useCaseSensitiveFileNames2 = isFileSystemCaseSensitive();
    const fsRealpath = !!_fs.realpathSync.native ? process.platform === "win32" ? fsRealPathHandlingLongPath : _fs.realpathSync.native : _fs.realpathSync;
    const executingFilePath = __filename.endsWith("sys.js") ? _path.join(_path.dirname(__dirname), "__fake__.js") : __filename;
    const fsSupportsRecursiveFsWatch = process.platform === "win32" || isMacOs;
    const getCurrentDirectory = memoize(() => process.cwd());
    const { watchFile: watchFile2, watchDirectory } = createSystemWatchFunctions({
      pollingWatchFileWorker: fsWatchFileWorker,
      getModifiedTime: getModifiedTime3,
      setTimeout,
      clearTimeout,
      fsWatchWorker,
      useCaseSensitiveFileNames: useCaseSensitiveFileNames2,
      getCurrentDirectory,
      fileSystemEntryExists,
      // Node 4.0 `fs.watch` function supports the "recursive" option on both OSX and Windows
      // (ref: https://github.com/nodejs/node/pull/2649 and https://github.com/Microsoft/TypeScript/issues/4643)
      fsSupportsRecursiveFsWatch,
      getAccessibleSortedChildDirectories: (path) => getAccessibleFileSystemEntries(path).directories,
      realpath,
      tscWatchFile: process.env.TSC_WATCHFILE,
      useNonPollingWatchers: !!process.env.TSC_NONPOLLING_WATCHER,
      tscWatchDirectory: process.env.TSC_WATCHDIRECTORY,
      inodeWatching: isLinuxOrMacOs,
      fsWatchWithTimestamp: isMacOs,
      sysLog
    });
    const nodeSystem = {
      args: process.argv.slice(2),
      newLine: _os.EOL,
      useCaseSensitiveFileNames: useCaseSensitiveFileNames2,
      write(s) {
        process.stdout.write(s);
      },
      getWidthOfTerminal() {
        return process.stdout.columns;
      },
      writeOutputIsTTY() {
        return process.stdout.isTTY;
      },
      readFile,
      writeFile: writeFile2,
      watchFile: watchFile2,
      watchDirectory,
      preferNonRecursiveWatch: !fsSupportsRecursiveFsWatch,
      resolvePath: (path) => _path.resolve(path),
      fileExists,
      directoryExists,
      getAccessibleFileSystemEntries,
      createDirectory(directoryName) {
        if (!nodeSystem.directoryExists(directoryName)) {
          try {
            _fs.mkdirSync(directoryName);
          } catch (e) {
            if (e.code !== "EEXIST") {
              throw e;
            }
          }
        }
      },
      getExecutingFilePath() {
        return executingFilePath;
      },
      getCurrentDirectory,
      getDirectories,
      getEnvironmentVariable(name) {
        return process.env[name] || "";
      },
      readDirectory,
      getModifiedTime: getModifiedTime3,
      setModifiedTime,
      deleteFile,
      createHash: _crypto ? createSHA256Hash : generateDjb2Hash,
      createSHA256Hash: _crypto ? createSHA256Hash : void 0,
      getMemoryUsage() {
        if (global.gc) {
          global.gc();
        }
        return process.memoryUsage().heapUsed;
      },
      getFileSize(path) {
        const stat = statSync(path);
        if (stat == null ? void 0 : stat.isFile()) {
          return stat.size;
        }
        return 0;
      },
      exit(exitCode) {
        disableCPUProfiler(() => process.exit(exitCode));
      },
      enableCPUProfiler,
      disableCPUProfiler,
      cpuProfilingEnabled: () => !!activeSession || contains(process.execArgv, "--cpu-prof") || contains(process.execArgv, "--prof"),
      realpath,
      debugMode: !!process.env.NODE_INSPECTOR_IPC || !!process.env.VSCODE_INSPECTOR_OPTIONS || some(process.execArgv, (arg) => /^--(?:inspect|debug)(?:-brk)?(?:=\d+)?$/i.test(arg)) || !!process.recordreplay,
      tryEnableSourceMapsForHost() {
        try {
          require("source-map-support").install();
        } catch {
        }
      },
      setTimeout,
      clearTimeout,
      clearScreen: () => {
        process.stdout.write("\x1B[2J\x1B[3J\x1B[H");
      },
      setBlocking: () => {
        var _a;
        const handle = (_a = process.stdout) == null ? void 0 : _a._handle;
        if (handle && handle.setBlocking) {
          handle.setBlocking(true);
        }
      },
      base64decode: (input) => Buffer.from(input, "base64").toString("utf8"),
      base64encode: (input) => Buffer.from(input).toString("base64"),
      require: (baseDir, moduleName) => {
        try {
          const modulePath = resolveJSModule(moduleName, baseDir, nodeSystem);
          return { module: require(modulePath), modulePath, error: void 0 };
        } catch (error) {
          return { module: void 0, modulePath: void 0, error };
        }
      }
    };
    return nodeSystem;
    function statSync(path) {
      try {
        return _fs.statSync(path, statSyncOptions);
      } catch {
        return void 0;
      }
    }
    function enableCPUProfiler(path, cb) {
      if (activeSession) {
        cb();
        return false;
      }
      const inspector = require("inspector");
      if (!inspector || !inspector.Session) {
        cb();
        return false;
      }
      const session = new inspector.Session();
      session.connect();
      session.post("Profiler.enable", () => {
        session.post("Profiler.start", () => {
          activeSession = session;
          profilePath = path;
          cb();
        });
      });
      return true;
    }
    function cleanupPaths(profile) {
      let externalFileCounter = 0;
      const remappedPaths = /* @__PURE__ */ new Map();
      const normalizedDir = normalizeSlashes(_path.dirname(executingFilePath));
      const fileUrlRoot = `file://${getRootLength(normalizedDir) === 1 ? "" : "/"}${normalizedDir}`;
      for (const node of profile.nodes) {
        if (node.callFrame.url) {
          const url = normalizeSlashes(node.callFrame.url);
          if (containsPath(fileUrlRoot, url, useCaseSensitiveFileNames2)) {
            node.callFrame.url = getRelativePathToDirectoryOrUrl(
              fileUrlRoot,
              url,
              fileUrlRoot,
              createGetCanonicalFileName(useCaseSensitiveFileNames2),
              /*isAbsolutePathAnUrl*/
              true
            );
          } else if (!nativePattern.test(url)) {
            node.callFrame.url = (remappedPaths.has(url) ? remappedPaths : remappedPaths.set(url, `external${externalFileCounter}.js`)).get(url);
            externalFileCounter++;
          }
        }
      }
      return profile;
    }
    function disableCPUProfiler(cb) {
      if (activeSession && activeSession !== "stopping") {
        const s = activeSession;
        activeSession.post("Profiler.stop", (err, { profile }) => {
          var _a;
          if (!err) {
            if ((_a = statSync(profilePath)) == null ? void 0 : _a.isDirectory()) {
              profilePath = _path.join(profilePath, `${(/* @__PURE__ */ new Date()).toISOString().replace(/:/g, "-")}+P${process.pid}.cpuprofile`);
            }
            try {
              _fs.mkdirSync(_path.dirname(profilePath), { recursive: true });
            } catch {
            }
            _fs.writeFileSync(profilePath, JSON.stringify(cleanupPaths(profile)));
          }
          activeSession = void 0;
          s.disconnect();
          cb();
        });
        activeSession = "stopping";
        return true;
      } else {
        cb();
        return false;
      }
    }
    function isFileSystemCaseSensitive() {
      if (platform === "win32" || platform === "win64") {
        return false;
      }
      return !fileExists(swapCase(__filename));
    }
    function swapCase(s) {
      return s.replace(/\w/g, (ch) => {
        const up = ch.toUpperCase();
        return ch === up ? ch.toLowerCase() : up;
      });
    }
    function fsWatchFileWorker(fileName, callback, pollingInterval) {
      _fs.watchFile(fileName, { persistent: true, interval: pollingInterval }, fileChanged);
      let eventKind;
      return {
        close: () => _fs.unwatchFile(fileName, fileChanged)
      };
      function fileChanged(curr, prev) {
        const isPreviouslyDeleted = +prev.mtime === 0 || eventKind === 2 /* Deleted */;
        if (+curr.mtime === 0) {
          if (isPreviouslyDeleted) {
            return;
          }
          eventKind = 2 /* Deleted */;
        } else if (isPreviouslyDeleted) {
          eventKind = 0 /* Created */;
        } else if (+curr.mtime === +prev.mtime) {
          return;
        } else {
          eventKind = 1 /* Changed */;
        }
        callback(fileName, eventKind, curr.mtime);
      }
    }
    function fsWatchWorker(fileOrDirectory, recursive, callback) {
      return _fs.watch(
        fileOrDirectory,
        fsSupportsRecursiveFsWatch ? { persistent: true, recursive: !!recursive } : { persistent: true },
        callback
      );
    }
    function readFile(fileName, _encoding) {
      let buffer;
      try {
        buffer = _fs.readFileSync(fileName);
      } catch {
        return void 0;
      }
      let len = buffer.length;
      if (len >= 2 && buffer[0] === 254 && buffer[1] === 255) {
        len &= ~1;
        for (let i = 0; i < len; i += 2) {
          const temp = buffer[i];
          buffer[i] = buffer[i + 1];
          buffer[i + 1] = temp;
        }
        return buffer.toString("utf16le", 2);
      }
      if (len >= 2 && buffer[0] === 255 && buffer[1] === 254) {
        return buffer.toString("utf16le", 2);
      }
      if (len >= 3 && buffer[0] === 239 && buffer[1] === 187 && buffer[2] === 191) {
        return buffer.toString("utf8", 3);
      }
      return buffer.toString("utf8");
    }
    function writeFile2(fileName, data, writeByteOrderMark) {
      if (writeByteOrderMark) {
        data = byteOrderMarkIndicator + data;
      }
      let fd;
      try {
        fd = _fs.openSync(fileName, "w");
        _fs.writeSync(
          fd,
          data,
          /*position*/
          void 0,
          "utf8"
        );
      } finally {
        if (fd !== void 0) {
          _fs.closeSync(fd);
        }
      }
    }
    function getAccessibleFileSystemEntries(path) {
      try {
        const entries = _fs.readdirSync(path || ".", { withFileTypes: true });
        const files = [];
        const directories = [];
        for (const dirent of entries) {
          const entry = typeof dirent === "string" ? dirent : dirent.name;
          if (entry === "." || entry === "..") {
            continue;
          }
          let stat;
          if (typeof dirent === "string" || dirent.isSymbolicLink()) {
            const name = combinePaths(path, entry);
            stat = statSync(name);
            if (!stat) {
              continue;
            }
          } else {
            stat = dirent;
          }
          if (stat.isFile()) {
            files.push(entry);
          } else if (stat.isDirectory()) {
            directories.push(entry);
          }
        }
        files.sort();
        directories.sort();
        return { files, directories };
      } catch {
        return emptyFileSystemEntries;
      }
    }
    function readDirectory(path, extensions, excludes, includes, depth) {
      return matchFiles(path, extensions, excludes, includes, useCaseSensitiveFileNames2, process.cwd(), depth, getAccessibleFileSystemEntries, realpath);
    }
    function fileSystemEntryExists(path, entryKind) {
      const stat = statSync(path);
      if (!stat) {
        return false;
      }
      switch (entryKind) {
        case 0 /* File */:
          return stat.isFile();
        case 1 /* Directory */:
          return stat.isDirectory();
        default:
          return false;
      }
    }
    function fileExists(path) {
      return fileSystemEntryExists(path, 0 /* File */);
    }
    function directoryExists(path) {
      return fileSystemEntryExists(path, 1 /* Directory */);
    }
    function getDirectories(path) {
      return getAccessibleFileSystemEntries(path).directories.slice();
    }
    function fsRealPathHandlingLongPath(path) {
      return path.length < 260 ? _fs.realpathSync.native(path) : _fs.realpathSync(path);
    }
    function realpath(path) {
      try {
        return fsRealpath(path);
      } catch {
        return path;
      }
    }
    function getModifiedTime3(path) {
      var _a;
      return (_a = statSync(path)) == null ? void 0 : _a.mtime;
    }
    function setModifiedTime(path, time) {
      try {
        _fs.utimesSync(path, time, time);
      } catch {
        return;
      }
    }
    function deleteFile(path) {
      try {
        return _fs.unlinkSync(path);
      } catch {
        return;
      }
    }
    function createSHA256Hash(data) {
      const hash = _crypto.createHash("sha256");
      hash.update(data);
      return hash.digest("hex");
    }
  }
  let sys2;
  if (isNodeLikeSystem()) {
    sys2 = getNodeSystem();
  }
  if (sys2) {
    patchWriteFileEnsuringDirectory(sys2);
  }
  return sys2;
})();
if (sys && sys.getEnvironmentVariable) {
  setCustomPollingValues(sys);
  Debug.setAssertionLevel(
    /^development$/i.test(sys.getEnvironmentVariable("NODE_ENV")) ? 1 /* Normal */ : 0 /* None */
  );
}
if (sys && sys.debugMode) {
  Debug.isDebugging = true;
}

// src/compiler/path.ts
var directorySeparator = "/";
var altDirectorySeparator = "\\";
var urlSchemeSeparator = "://";
var backslashRegExp = /\\/g;
function isAnyDirectorySeparator(charCode) {
  return charCode === 47 /* slash */ || charCode === 92 /* backslash */;
}
function isRootedDiskPath(path) {
  return getEncodedRootLength(path) > 0;
}
function isDiskPathRoot(path) {
  const rootLength = getEncodedRootLength(path);
  return rootLength > 0 && rootLength === path.length;
}
function pathIsAbsolute(path) {
  return getEncodedRootLength(path) !== 0;
}
function pathIsRelative(path) {
  return /^\.\.?(?:$|[\\/])/.test(path);
}
function pathIsBareSpecifier(path) {
  return !pathIsAbsolute(path) && !pathIsRelative(path);
}
function hasExtension(fileName) {
  return getBaseFileName(fileName).includes(".");
}
function fileExtensionIs(path, extension) {
  return path.length > extension.length && endsWith(path, extension);
}
function fileExtensionIsOneOf(path, extensions) {
  for (const extension of extensions) {
    if (fileExtensionIs(path, extension)) {
      return true;
    }
  }
  return false;
}
function hasTrailingDirectorySeparator(path) {
  return path.length > 0 && isAnyDirectorySeparator(path.charCodeAt(path.length - 1));
}
function isVolumeCharacter(charCode) {
  return charCode >= 97 /* a */ && charCode <= 122 /* z */ || charCode >= 65 /* A */ && charCode <= 90 /* Z */;
}
function getFileUrlVolumeSeparatorEnd(url, start) {
  const ch0 = url.charCodeAt(start);
  if (ch0 === 58 /* colon */) return start + 1;
  if (ch0 === 37 /* percent */ && url.charCodeAt(start + 1) === 51 /* _3 */) {
    const ch2 = url.charCodeAt(start + 2);
    if (ch2 === 97 /* a */ || ch2 === 65 /* A */) return start + 3;
  }
  return -1;
}
function getEncodedRootLength(path) {
  if (!path) return 0;
  const ch0 = path.charCodeAt(0);
  if (ch0 === 47 /* slash */ || ch0 === 92 /* backslash */) {
    if (path.charCodeAt(1) !== ch0) return 1;
    const p1 = path.indexOf(ch0 === 47 /* slash */ ? directorySeparator : altDirectorySeparator, 2);
    if (p1 < 0) return path.length;
    return p1 + 1;
  }
  if (isVolumeCharacter(ch0) && path.charCodeAt(1) === 58 /* colon */) {
    const ch2 = path.charCodeAt(2);
    if (ch2 === 47 /* slash */ || ch2 === 92 /* backslash */) return 3;
    if (path.length === 2) return 2;
  }
  const schemeEnd = path.indexOf(urlSchemeSeparator);
  if (schemeEnd !== -1) {
    const authorityStart = schemeEnd + urlSchemeSeparator.length;
    const authorityEnd = path.indexOf(directorySeparator, authorityStart);
    if (authorityEnd !== -1) {
      const scheme = path.slice(0, schemeEnd);
      const authority = path.slice(authorityStart, authorityEnd);
      if (scheme === "file" && (authority === "" || authority === "localhost") && isVolumeCharacter(path.charCodeAt(authorityEnd + 1))) {
        const volumeSeparatorEnd = getFileUrlVolumeSeparatorEnd(path, authorityEnd + 2);
        if (volumeSeparatorEnd !== -1) {
          if (path.charCodeAt(volumeSeparatorEnd) === 47 /* slash */) {
            return ~(volumeSeparatorEnd + 1);
          }
          if (volumeSeparatorEnd === path.length) {
            return ~volumeSeparatorEnd;
          }
        }
      }
      return ~(authorityEnd + 1);
    }
    return ~path.length;
  }
  return 0;
}
function getRootLength(path) {
  const rootLength = getEncodedRootLength(path);
  return rootLength < 0 ? ~rootLength : rootLength;
}
function getDirectoryPath(path) {
  path = normalizeSlashes(path);
  const rootLength = getRootLength(path);
  if (rootLength === path.length) return path;
  path = removeTrailingDirectorySeparator(path);
  return path.slice(0, Math.max(rootLength, path.lastIndexOf(directorySeparator)));
}
function getBaseFileName(path, extensions, ignoreCase) {
  path = normalizeSlashes(path);
  const rootLength = getRootLength(path);
  if (rootLength === path.length) return "";
  path = removeTrailingDirectorySeparator(path);
  const name = path.slice(Math.max(getRootLength(path), path.lastIndexOf(directorySeparator) + 1));
  const extension = extensions !== void 0 && ignoreCase !== void 0 ? getAnyExtensionFromPath(name, extensions, ignoreCase) : void 0;
  return extension ? name.slice(0, name.length - extension.length) : name;
}
function tryGetExtensionFromPath(path, extension, stringEqualityComparer) {
  if (!startsWith(extension, ".")) extension = "." + extension;
  if (path.length >= extension.length && path.charCodeAt(path.length - extension.length) === 46 /* dot */) {
    const pathExtension = path.slice(path.length - extension.length);
    if (stringEqualityComparer(pathExtension, extension)) {
      return pathExtension;
    }
  }
}
function getAnyExtensionFromPathWorker(path, extensions, stringEqualityComparer) {
  if (typeof extensions === "string") {
    return tryGetExtensionFromPath(path, extensions, stringEqualityComparer) || "";
  }
  for (const extension of extensions) {
    const result = tryGetExtensionFromPath(path, extension, stringEqualityComparer);
    if (result) return result;
  }
  return "";
}
function getAnyExtensionFromPath(path, extensions, ignoreCase) {
  if (extensions) {
    return getAnyExtensionFromPathWorker(removeTrailingDirectorySeparator(path), extensions, ignoreCase ? equateStringsCaseInsensitive : equateStringsCaseSensitive);
  }
  const baseFileName = getBaseFileName(path);
  const extensionIndex = baseFileName.lastIndexOf(".");
  if (extensionIndex >= 0) {
    return baseFileName.substring(extensionIndex);
  }
  return "";
}
function pathComponents(path, rootLength) {
  const root = path.substring(0, rootLength);
  const rest = path.substring(rootLength).split(directorySeparator);
  if (rest.length && !lastOrUndefined(rest)) rest.pop();
  return [root, ...rest];
}
function getPathComponents(path, currentDirectory = "") {
  path = combinePaths(currentDirectory, path);
  return pathComponents(path, getRootLength(path));
}
function getPathFromPathComponents(pathComponents2, length2) {
  if (pathComponents2.length === 0) return "";
  const root = pathComponents2[0] && ensureTrailingDirectorySeparator(pathComponents2[0]);
  return root + pathComponents2.slice(1, length2).join(directorySeparator);
}
function normalizeSlashes(path) {
  return path.includes("\\") ? path.replace(backslashRegExp, directorySeparator) : path;
}
function reducePathComponents(components) {
  if (!some(components)) return [];
  const reduced = [components[0]];
  for (let i = 1; i < components.length; i++) {
    const component = components[i];
    if (!component) continue;
    if (component === ".") continue;
    if (component === "..") {
      if (reduced.length > 1) {
        if (reduced[reduced.length - 1] !== "..") {
          reduced.pop();
          continue;
        }
      } else if (reduced[0]) continue;
    }
    reduced.push(component);
  }
  return reduced;
}
function combinePaths(path, ...paths) {
  if (path) path = normalizeSlashes(path);
  for (let relativePath of paths) {
    if (!relativePath) continue;
    relativePath = normalizeSlashes(relativePath);
    if (!path || getRootLength(relativePath) !== 0) {
      path = relativePath;
    } else {
      path = ensureTrailingDirectorySeparator(path) + relativePath;
    }
  }
  return path;
}
function resolvePath(path, ...paths) {
  return normalizePath(some(paths) ? combinePaths(path, ...paths) : normalizeSlashes(path));
}
function getNormalizedPathComponents(path, currentDirectory) {
  return reducePathComponents(getPathComponents(path, currentDirectory));
}
function getNormalizedAbsolutePath(path, currentDirectory) {
  let rootLength = getRootLength(path);
  if (rootLength === 0 && currentDirectory) {
    path = combinePaths(currentDirectory, path);
    rootLength = getRootLength(path);
  } else {
    path = normalizeSlashes(path);
  }
  const simpleNormalized = simpleNormalizePath(path);
  if (simpleNormalized !== void 0) {
    return simpleNormalized.length > rootLength ? removeTrailingDirectorySeparator(simpleNormalized) : simpleNormalized;
  }
  const length2 = path.length;
  const root = path.substring(0, rootLength);
  let normalized;
  let index = rootLength;
  let segmentStart = index;
  let normalizedUpTo = index;
  let seenNonDotDotSegment = rootLength !== 0;
  while (index < length2) {
    segmentStart = index;
    let ch = path.charCodeAt(index);
    while (ch === 47 /* slash */ && index + 1 < length2) {
      index++;
      ch = path.charCodeAt(index);
    }
    if (index > segmentStart) {
      normalized ?? (normalized = path.substring(0, segmentStart - 1));
      segmentStart = index;
    }
    let segmentEnd = path.indexOf(directorySeparator, index + 1);
    if (segmentEnd === -1) {
      segmentEnd = length2;
    }
    const segmentLength = segmentEnd - segmentStart;
    if (segmentLength === 1 && path.charCodeAt(index) === 46 /* dot */) {
      normalized ?? (normalized = path.substring(0, normalizedUpTo));
    } else if (segmentLength === 2 && path.charCodeAt(index) === 46 /* dot */ && path.charCodeAt(index + 1) === 46 /* dot */) {
      if (!seenNonDotDotSegment) {
        if (normalized !== void 0) {
          normalized += normalized.length === rootLength ? ".." : "/..";
        } else {
          normalizedUpTo = index + 2;
        }
      } else if (normalized === void 0) {
        if (normalizedUpTo - 2 >= 0) {
          normalized = path.substring(0, Math.max(rootLength, path.lastIndexOf(directorySeparator, normalizedUpTo - 2)));
        } else {
          normalized = path.substring(0, normalizedUpTo);
        }
      } else {
        const lastSlash = normalized.lastIndexOf(directorySeparator);
        if (lastSlash !== -1) {
          normalized = normalized.substring(0, Math.max(rootLength, lastSlash));
        } else {
          normalized = root;
        }
        if (normalized.length === rootLength) {
          seenNonDotDotSegment = rootLength !== 0;
        }
      }
    } else if (normalized !== void 0) {
      if (normalized.length !== rootLength) {
        normalized += directorySeparator;
      }
      seenNonDotDotSegment = true;
      normalized += path.substring(segmentStart, segmentEnd);
    } else {
      seenNonDotDotSegment = true;
      normalizedUpTo = segmentEnd;
    }
    index = segmentEnd + 1;
  }
  return normalized ?? (length2 > rootLength ? removeTrailingDirectorySeparator(path) : path);
}
function normalizePath(path) {
  path = normalizeSlashes(path);
  let normalized = simpleNormalizePath(path);
  if (normalized !== void 0) {
    return normalized;
  }
  normalized = getNormalizedAbsolutePath(path, "");
  return normalized && hasTrailingDirectorySeparator(path) ? ensureTrailingDirectorySeparator(normalized) : normalized;
}
function simpleNormalizePath(path) {
  if (!relativePathSegmentRegExp.test(path)) {
    return path;
  }
  let simplified = path.replace(/\/\.\//g, "/");
  if (simplified.startsWith("./")) {
    simplified = simplified.slice(2);
  }
  if (simplified !== path) {
    path = simplified;
    if (!relativePathSegmentRegExp.test(path)) {
      return path;
    }
  }
  return void 0;
}
function getPathWithoutRoot(pathComponents2) {
  if (pathComponents2.length === 0) return "";
  return pathComponents2.slice(1).join(directorySeparator);
}
function getNormalizedAbsolutePathWithoutRoot(fileName, currentDirectory) {
  return getPathWithoutRoot(getNormalizedPathComponents(fileName, currentDirectory));
}
function toPath(fileName, basePath, getCanonicalFileName) {
  const nonCanonicalizedPath = isRootedDiskPath(fileName) ? normalizePath(fileName) : getNormalizedAbsolutePath(fileName, basePath);
  return getCanonicalFileName(nonCanonicalizedPath);
}
function removeTrailingDirectorySeparator(path) {
  if (hasTrailingDirectorySeparator(path)) {
    return path.substr(0, path.length - 1);
  }
  return path;
}
function ensureTrailingDirectorySeparator(path) {
  if (!hasTrailingDirectorySeparator(path)) {
    return path + directorySeparator;
  }
  return path;
}
function ensurePathIsNonModuleName(path) {
  return !pathIsAbsolute(path) && !pathIsRelative(path) ? "./" + path : path;
}
function changeAnyExtension(path, ext, extensions, ignoreCase) {
  const pathext = extensions !== void 0 && ignoreCase !== void 0 ? getAnyExtensionFromPath(path, extensions, ignoreCase) : getAnyExtensionFromPath(path);
  return pathext ? path.slice(0, path.length - pathext.length) + (startsWith(ext, ".") ? ext : "." + ext) : path;
}
function changeFullExtension(path, newExtension) {
  const declarationExtension = getDeclarationFileExtension(path);
  if (declarationExtension) {
    return path.slice(0, path.length - declarationExtension.length) + (startsWith(newExtension, ".") ? newExtension : "." + newExtension);
  }
  return changeAnyExtension(path, newExtension);
}
var relativePathSegmentRegExp = /\/\/|(?:^|\/)\.\.?(?:$|\/)/;
function comparePathsWorker(a, b, componentComparer) {
  if (a === b) return 0 /* EqualTo */;
  if (a === void 0) return -1 /* LessThan */;
  if (b === void 0) return 1 /* GreaterThan */;
  const aRoot = a.substring(0, getRootLength(a));
  const bRoot = b.substring(0, getRootLength(b));
  const result = compareStringsCaseInsensitive(aRoot, bRoot);
  if (result !== 0 /* EqualTo */) {
    return result;
  }
  const aRest = a.substring(aRoot.length);
  const bRest = b.substring(bRoot.length);
  if (!relativePathSegmentRegExp.test(aRest) && !relativePathSegmentRegExp.test(bRest)) {
    return componentComparer(aRest, bRest);
  }
  const aComponents = reducePathComponents(getPathComponents(a));
  const bComponents = reducePathComponents(getPathComponents(b));
  const sharedLength = Math.min(aComponents.length, bComponents.length);
  for (let i = 1; i < sharedLength; i++) {
    const result2 = componentComparer(aComponents[i], bComponents[i]);
    if (result2 !== 0 /* EqualTo */) {
      return result2;
    }
  }
  return compareValues(aComponents.length, bComponents.length);
}
function comparePaths(a, b, currentDirectory, ignoreCase) {
  if (typeof currentDirectory === "string") {
    a = combinePaths(currentDirectory, a);
    b = combinePaths(currentDirectory, b);
  } else if (typeof currentDirectory === "boolean") {
    ignoreCase = currentDirectory;
  }
  return comparePathsWorker(a, b, getStringComparer(ignoreCase));
}
function containsPath(parent, child, currentDirectory, ignoreCase) {
  if (typeof currentDirectory === "string") {
    parent = combinePaths(currentDirectory, parent);
    child = combinePaths(currentDirectory, child);
  } else if (typeof currentDirectory === "boolean") {
    ignoreCase = currentDirectory;
  }
  if (parent === void 0 || child === void 0) return false;
  if (parent === child) return true;
  const parentComponents = reducePathComponents(getPathComponents(parent));
  const childComponents = reducePathComponents(getPathComponents(child));
  if (childComponents.length < parentComponents.length) {
    return false;
  }
  const componentEqualityComparer = ignoreCase ? equateStringsCaseInsensitive : equateStringsCaseSensitive;
  for (let i = 0; i < parentComponents.length; i++) {
    const equalityComparer = i === 0 ? equateStringsCaseInsensitive : componentEqualityComparer;
    if (!equalityComparer(parentComponents[i], childComponents[i])) {
      return false;
    }
  }
  return true;
}
function startsWithDirectory(fileName, directoryName, getCanonicalFileName) {
  const canonicalFileName = getCanonicalFileName(fileName);
  const canonicalDirectoryName = getCanonicalFileName(directoryName);
  return startsWith(canonicalFileName, canonicalDirectoryName + "/") || startsWith(canonicalFileName, canonicalDirectoryName + "\\");
}
function getPathComponentsRelativeTo(from, to, stringEqualityComparer, getCanonicalFileName) {
  const fromComponents = reducePathComponents(getPathComponents(from));
  const toComponents = reducePathComponents(getPathComponents(to));
  let start;
  for (start = 0; start < fromComponents.length && start < toComponents.length; start++) {
    const fromComponent = getCanonicalFileName(fromComponents[start]);
    const toComponent = getCanonicalFileName(toComponents[start]);
    const comparer = start === 0 ? equateStringsCaseInsensitive : stringEqualityComparer;
    if (!comparer(fromComponent, toComponent)) break;
  }
  if (start === 0) {
    return toComponents;
  }
  const components = toComponents.slice(start);
  const relative = [];
  for (; start < fromComponents.length; start++) {
    relative.push("..");
  }
  return ["", ...relative, ...components];
}
function getRelativePathFromDirectory(fromDirectory, to, getCanonicalFileNameOrIgnoreCase) {
  Debug.assert(getRootLength(fromDirectory) > 0 === getRootLength(to) > 0, "Paths must either both be absolute or both be relative");
  const getCanonicalFileName = typeof getCanonicalFileNameOrIgnoreCase === "function" ? getCanonicalFileNameOrIgnoreCase : identity;
  const ignoreCase = typeof getCanonicalFileNameOrIgnoreCase === "boolean" ? getCanonicalFileNameOrIgnoreCase : false;
  const pathComponents2 = getPathComponentsRelativeTo(fromDirectory, to, ignoreCase ? equateStringsCaseInsensitive : equateStringsCaseSensitive, getCanonicalFileName);
  return getPathFromPathComponents(pathComponents2);
}
function convertToRelativePath(absoluteOrRelativePath, basePath, getCanonicalFileName) {
  return !isRootedDiskPath(absoluteOrRelativePath) ? absoluteOrRelativePath : getRelativePathToDirectoryOrUrl(
    basePath,
    absoluteOrRelativePath,
    basePath,
    getCanonicalFileName,
    /*isAbsolutePathAnUrl*/
    false
  );
}
function getRelativePathFromFile(from, to, getCanonicalFileName) {
  return ensurePathIsNonModuleName(getRelativePathFromDirectory(getDirectoryPath(from), to, getCanonicalFileName));
}
function getRelativePathToDirectoryOrUrl(directoryPathOrUrl, relativeOrAbsolutePath, currentDirectory, getCanonicalFileName, isAbsolutePathAnUrl) {
  const pathComponents2 = getPathComponentsRelativeTo(
    resolvePath(currentDirectory, directoryPathOrUrl),
    resolvePath(currentDirectory, relativeOrAbsolutePath),
    equateStringsCaseSensitive,
    getCanonicalFileName
  );
  const firstComponent = pathComponents2[0];
  if (isAbsolutePathAnUrl && isRootedDiskPath(firstComponent)) {
    const prefix = firstComponent.charAt(0) === directorySeparator ? "file://" : "file:///";
    pathComponents2[0] = prefix + firstComponent;
  }
  return getPathFromPathComponents(pathComponents2);
}
function forEachAncestorDirectory(directory, callback) {
  while (true) {
    const result = callback(directory);
    if (result !== void 0) {
      return result;
    }
    const parentPath = getDirectoryPath(directory);
    if (parentPath === directory) {
      return void 0;
    }
    directory = parentPath;
  }
}
function isNodeModulesDirectory(dirPath) {
  return endsWith(dirPath, "/node_modules");
}

// src/compiler/diagnosticInformationMap.generated.ts
function diag(code, category, key, message, reportsUnnecessary, elidedInCompatabilityPyramid, reportsDeprecated) {
  return { code, category, key, message, reportsUnnecessary, elidedInCompatabilityPyramid, reportsDeprecated };
}
var Diagnostics = {
  Unterminated_string_literal: diag(1002, 1 /* Error */, "Unterminated_string_literal_1002", "Unterminated string literal."),
  Identifier_expected: diag(1003, 1 /* Error */, "Identifier_expected_1003", "Identifier expected."),
  _0_expected: diag(1005, 1 /* Error */, "_0_expected_1005", "'{0}' expected."),
  A_file_cannot_have_a_reference_to_itself: diag(1006, 1 /* Error */, "A_file_cannot_have_a_reference_to_itself_1006", "A file cannot have a reference to itself."),
  The_parser_expected_to_find_a_1_to_match_the_0_token_here: diag(1007, 1 /* Error */, "The_parser_expected_to_find_a_1_to_match_the_0_token_here_1007", "The parser expected to find a '{1}' to match the '{0}' token here."),
  Trailing_comma_not_allowed: diag(1009, 1 /* Error */, "Trailing_comma_not_allowed_1009", "Trailing comma not allowed."),
  Asterisk_Slash_expected: diag(1010, 1 /* Error */, "Asterisk_Slash_expected_1010", "'*/' expected."),
  An_element_access_expression_should_take_an_argument: diag(1011, 1 /* Error */, "An_element_access_expression_should_take_an_argument_1011", "An element access expression should take an argument."),
  Unexpected_token: diag(1012, 1 /* Error */, "Unexpected_token_1012", "Unexpected token."),
  A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma: diag(1013, 1 /* Error */, "A_rest_parameter_or_binding_pattern_may_not_have_a_trailing_comma_1013", "A rest parameter or binding pattern may not have a trailing comma."),
  A_rest_parameter_must_be_last_in_a_parameter_list: diag(1014, 1 /* Error */, "A_rest_parameter_must_be_last_in_a_parameter_list_1014", "A rest parameter must be last in a parameter list."),
  Parameter_cannot_have_question_mark_and_initializer: diag(1015, 1 /* Error */, "Parameter_cannot_have_question_mark_and_initializer_1015", "Parameter cannot have question mark and initializer."),
  A_required_parameter_cannot_follow_an_optional_parameter: diag(1016, 1 /* Error */, "A_required_parameter_cannot_follow_an_optional_parameter_1016", "A required parameter cannot follow an optional parameter."),
  An_index_signature_cannot_have_a_rest_parameter: diag(1017, 1 /* Error */, "An_index_signature_cannot_have_a_rest_parameter_1017", "An index signature cannot have a rest parameter."),
  An_index_signature_parameter_cannot_have_an_accessibility_modifier: diag(1018, 1 /* Error */, "An_index_signature_parameter_cannot_have_an_accessibility_modifier_1018", "An index signature parameter cannot have an accessibility modifier."),
  An_index_signature_parameter_cannot_have_a_question_mark: diag(1019, 1 /* Error */, "An_index_signature_parameter_cannot_have_a_question_mark_1019", "An index signature parameter cannot have a question mark."),
  An_index_signature_parameter_cannot_have_an_initializer: diag(1020, 1 /* Error */, "An_index_signature_parameter_cannot_have_an_initializer_1020", "An index signature parameter cannot have an initializer."),
  An_index_signature_must_have_a_type_annotation: diag(1021, 1 /* Error */, "An_index_signature_must_have_a_type_annotation_1021", "An index signature must have a type annotation."),
  An_index_signature_parameter_must_have_a_type_annotation: diag(1022, 1 /* Error */, "An_index_signature_parameter_must_have_a_type_annotation_1022", "An index signature parameter must have a type annotation."),
  readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature: diag(1024, 1 /* Error */, "readonly_modifier_can_only_appear_on_a_property_declaration_or_index_signature_1024", "'readonly' modifier can only appear on a property declaration or index signature."),
  An_index_signature_cannot_have_a_trailing_comma: diag(1025, 1 /* Error */, "An_index_signature_cannot_have_a_trailing_comma_1025", "An index signature cannot have a trailing comma."),
  Accessibility_modifier_already_seen: diag(1028, 1 /* Error */, "Accessibility_modifier_already_seen_1028", "Accessibility modifier already seen."),
  _0_modifier_must_precede_1_modifier: diag(1029, 1 /* Error */, "_0_modifier_must_precede_1_modifier_1029", "'{0}' modifier must precede '{1}' modifier."),
  _0_modifier_already_seen: diag(1030, 1 /* Error */, "_0_modifier_already_seen_1030", "'{0}' modifier already seen."),
  _0_modifier_cannot_appear_on_class_elements_of_this_kind: diag(1031, 1 /* Error */, "_0_modifier_cannot_appear_on_class_elements_of_this_kind_1031", "'{0}' modifier cannot appear on class elements of this kind."),
  super_must_be_followed_by_an_argument_list_or_member_access: diag(1034, 1 /* Error */, "super_must_be_followed_by_an_argument_list_or_member_access_1034", "'super' must be followed by an argument list or member access."),
  Only_ambient_modules_can_use_quoted_names: diag(1035, 1 /* Error */, "Only_ambient_modules_can_use_quoted_names_1035", "Only ambient modules can use quoted names."),
  Statements_are_not_allowed_in_ambient_contexts: diag(1036, 1 /* Error */, "Statements_are_not_allowed_in_ambient_contexts_1036", "Statements are not allowed in ambient contexts."),
  A_declare_modifier_cannot_be_used_in_an_already_ambient_context: diag(1038, 1 /* Error */, "A_declare_modifier_cannot_be_used_in_an_already_ambient_context_1038", "A 'declare' modifier cannot be used in an already ambient context."),
  Initializers_are_not_allowed_in_ambient_contexts: diag(1039, 1 /* Error */, "Initializers_are_not_allowed_in_ambient_contexts_1039", "Initializers are not allowed in ambient contexts."),
  _0_modifier_cannot_be_used_in_an_ambient_context: diag(1040, 1 /* Error */, "_0_modifier_cannot_be_used_in_an_ambient_context_1040", "'{0}' modifier cannot be used in an ambient context."),
  _0_modifier_cannot_be_used_here: diag(1042, 1 /* Error */, "_0_modifier_cannot_be_used_here_1042", "'{0}' modifier cannot be used here."),
  _0_modifier_cannot_appear_on_a_module_or_namespace_element: diag(1044, 1 /* Error */, "_0_modifier_cannot_appear_on_a_module_or_namespace_element_1044", "'{0}' modifier cannot appear on a module or namespace element."),
  Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier: diag(1046, 1 /* Error */, "Top_level_declarations_in_d_ts_files_must_start_with_either_a_declare_or_export_modifier_1046", "Top-level declarations in .d.ts files must start with either a 'declare' or 'export' modifier."),
  A_rest_parameter_cannot_be_optional: diag(1047, 1 /* Error */, "A_rest_parameter_cannot_be_optional_1047", "A rest parameter cannot be optional."),
  A_rest_parameter_cannot_have_an_initializer: diag(1048, 1 /* Error */, "A_rest_parameter_cannot_have_an_initializer_1048", "A rest parameter cannot have an initializer."),
  A_set_accessor_must_have_exactly_one_parameter: diag(1049, 1 /* Error */, "A_set_accessor_must_have_exactly_one_parameter_1049", "A 'set' accessor must have exactly one parameter."),
  A_set_accessor_cannot_have_an_optional_parameter: diag(1051, 1 /* Error */, "A_set_accessor_cannot_have_an_optional_parameter_1051", "A 'set' accessor cannot have an optional parameter."),
  A_set_accessor_parameter_cannot_have_an_initializer: diag(1052, 1 /* Error */, "A_set_accessor_parameter_cannot_have_an_initializer_1052", "A 'set' accessor parameter cannot have an initializer."),
  A_set_accessor_cannot_have_rest_parameter: diag(1053, 1 /* Error */, "A_set_accessor_cannot_have_rest_parameter_1053", "A 'set' accessor cannot have rest parameter."),
  A_get_accessor_cannot_have_parameters: diag(1054, 1 /* Error */, "A_get_accessor_cannot_have_parameters_1054", "A 'get' accessor cannot have parameters."),
  Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compatible_constructor_value: diag(1055, 1 /* Error */, "Type_0_is_not_a_valid_async_function_return_type_in_ES5_because_it_does_not_refer_to_a_Promise_compa_1055", "Type '{0}' is not a valid async function return type in ES5 because it does not refer to a Promise-compatible constructor value."),
  Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher: diag(1056, 1 /* Error */, "Accessors_are_only_available_when_targeting_ECMAScript_5_and_higher_1056", "Accessors are only available when targeting ECMAScript 5 and higher."),
  The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_then_member: diag(1058, 1 /* Error */, "The_return_type_of_an_async_function_must_either_be_a_valid_promise_or_must_not_contain_a_callable_t_1058", "The return type of an async function must either be a valid promise or must not contain a callable 'then' member."),
  A_promise_must_have_a_then_method: diag(1059, 1 /* Error */, "A_promise_must_have_a_then_method_1059", "A promise must have a 'then' method."),
  The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback: diag(1060, 1 /* Error */, "The_first_parameter_of_the_then_method_of_a_promise_must_be_a_callback_1060", "The first parameter of the 'then' method of a promise must be a callback."),
  Enum_member_must_have_initializer: diag(1061, 1 /* Error */, "Enum_member_must_have_initializer_1061", "Enum member must have initializer."),
  Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method: diag(1062, 1 /* Error */, "Type_is_referenced_directly_or_indirectly_in_the_fulfillment_callback_of_its_own_then_method_1062", "Type is referenced directly or indirectly in the fulfillment callback of its own 'then' method."),
  An_export_assignment_cannot_be_used_in_a_namespace: diag(1063, 1 /* Error */, "An_export_assignment_cannot_be_used_in_a_namespace_1063", "An export assignment cannot be used in a namespace."),
  The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_write_Promise_0: diag(1064, 1 /* Error */, "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_Did_you_mean_to_wri_1064", "The return type of an async function or method must be the global Promise<T> type. Did you mean to write 'Promise<{0}>'?"),
  The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type: diag(1065, 1 /* Error */, "The_return_type_of_an_async_function_or_method_must_be_the_global_Promise_T_type_1065", "The return type of an async function or method must be the global Promise<T> type."),
  In_ambient_enum_declarations_member_initializer_must_be_constant_expression: diag(1066, 1 /* Error */, "In_ambient_enum_declarations_member_initializer_must_be_constant_expression_1066", "In ambient enum declarations member initializer must be constant expression."),
  Unexpected_token_A_constructor_method_accessor_or_property_was_expected: diag(1068, 1 /* Error */, "Unexpected_token_A_constructor_method_accessor_or_property_was_expected_1068", "Unexpected token. A constructor, method, accessor, or property was expected."),
  Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces: diag(1069, 1 /* Error */, "Unexpected_token_A_type_parameter_name_was_expected_without_curly_braces_1069", "Unexpected token. A type parameter name was expected without curly braces."),
  _0_modifier_cannot_appear_on_a_type_member: diag(1070, 1 /* Error */, "_0_modifier_cannot_appear_on_a_type_member_1070", "'{0}' modifier cannot appear on a type member."),
  _0_modifier_cannot_appear_on_an_index_signature: diag(1071, 1 /* Error */, "_0_modifier_cannot_appear_on_an_index_signature_1071", "'{0}' modifier cannot appear on an index signature."),
  A_0_modifier_cannot_be_used_with_an_import_declaration: diag(1079, 1 /* Error */, "A_0_modifier_cannot_be_used_with_an_import_declaration_1079", "A '{0}' modifier cannot be used with an import declaration."),
  Invalid_reference_directive_syntax: diag(1084, 1 /* Error */, "Invalid_reference_directive_syntax_1084", "Invalid 'reference' directive syntax."),
  _0_modifier_cannot_appear_on_a_constructor_declaration: diag(1089, 1 /* Error */, "_0_modifier_cannot_appear_on_a_constructor_declaration_1089", "'{0}' modifier cannot appear on a constructor declaration."),
  _0_modifier_cannot_appear_on_a_parameter: diag(1090, 1 /* Error */, "_0_modifier_cannot_appear_on_a_parameter_1090", "'{0}' modifier cannot appear on a parameter."),
  Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement: diag(1091, 1 /* Error */, "Only_a_single_variable_declaration_is_allowed_in_a_for_in_statement_1091", "Only a single variable declaration is allowed in a 'for...in' statement."),
  Type_parameters_cannot_appear_on_a_constructor_declaration: diag(1092, 1 /* Error */, "Type_parameters_cannot_appear_on_a_constructor_declaration_1092", "Type parameters cannot appear on a constructor declaration."),
  Type_annotation_cannot_appear_on_a_constructor_declaration: diag(1093, 1 /* Error */, "Type_annotation_cannot_appear_on_a_constructor_declaration_1093", "Type annotation cannot appear on a constructor declaration."),
  An_accessor_cannot_have_type_parameters: diag(1094, 1 /* Error */, "An_accessor_cannot_have_type_parameters_1094", "An accessor cannot have type parameters."),
  A_set_accessor_cannot_have_a_return_type_annotation: diag(1095, 1 /* Error */, "A_set_accessor_cannot_have_a_return_type_annotation_1095", "A 'set' accessor cannot have a return type annotation."),
  An_index_signature_must_have_exactly_one_parameter: diag(1096, 1 /* Error */, "An_index_signature_must_have_exactly_one_parameter_1096", "An index signature must have exactly one parameter."),
  _0_list_cannot_be_empty: diag(1097, 1 /* Error */, "_0_list_cannot_be_empty_1097", "'{0}' list cannot be empty."),
  Type_parameter_list_cannot_be_empty: diag(1098, 1 /* Error */, "Type_parameter_list_cannot_be_empty_1098", "Type parameter list cannot be empty."),
  Type_argument_list_cannot_be_empty: diag(1099, 1 /* Error */, "Type_argument_list_cannot_be_empty_1099", "Type argument list cannot be empty."),
  Invalid_use_of_0_in_strict_mode: diag(1100, 1 /* Error */, "Invalid_use_of_0_in_strict_mode_1100", "Invalid use of '{0}' in strict mode."),
  with_statements_are_not_allowed_in_strict_mode: diag(1101, 1 /* Error */, "with_statements_are_not_allowed_in_strict_mode_1101", "'with' statements are not allowed in strict mode."),
  delete_cannot_be_called_on_an_identifier_in_strict_mode: diag(1102, 1 /* Error */, "delete_cannot_be_called_on_an_identifier_in_strict_mode_1102", "'delete' cannot be called on an identifier in strict mode."),
  for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules: diag(1103, 1 /* Error */, "for_await_loops_are_only_allowed_within_async_functions_and_at_the_top_levels_of_modules_1103", "'for await' loops are only allowed within async functions and at the top levels of modules."),
  A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement: diag(1104, 1 /* Error */, "A_continue_statement_can_only_be_used_within_an_enclosing_iteration_statement_1104", "A 'continue' statement can only be used within an enclosing iteration statement."),
  A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement: diag(1105, 1 /* Error */, "A_break_statement_can_only_be_used_within_an_enclosing_iteration_or_switch_statement_1105", "A 'break' statement can only be used within an enclosing iteration or switch statement."),
  The_left_hand_side_of_a_for_of_statement_may_not_be_async: diag(1106, 1 /* Error */, "The_left_hand_side_of_a_for_of_statement_may_not_be_async_1106", "The left-hand side of a 'for...of' statement may not be 'async'."),
  Jump_target_cannot_cross_function_boundary: diag(1107, 1 /* Error */, "Jump_target_cannot_cross_function_boundary_1107", "Jump target cannot cross function boundary."),
  A_return_statement_can_only_be_used_within_a_function_body: diag(1108, 1 /* Error */, "A_return_statement_can_only_be_used_within_a_function_body_1108", "A 'return' statement can only be used within a function body."),
  Expression_expected: diag(1109, 1 /* Error */, "Expression_expected_1109", "Expression expected."),
  Type_expected: diag(1110, 1 /* Error */, "Type_expected_1110", "Type expected."),
  Private_field_0_must_be_declared_in_an_enclosing_class: diag(1111, 1 /* Error */, "Private_field_0_must_be_declared_in_an_enclosing_class_1111", "Private field '{0}' must be declared in an enclosing class."),
  A_default_clause_cannot_appear_more_than_once_in_a_switch_statement: diag(1113, 1 /* Error */, "A_default_clause_cannot_appear_more_than_once_in_a_switch_statement_1113", "A 'default' clause cannot appear more than once in a 'switch' statement."),
  Duplicate_label_0: diag(1114, 1 /* Error */, "Duplicate_label_0_1114", "Duplicate label '{0}'."),
  A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement: diag(1115, 1 /* Error */, "A_continue_statement_can_only_jump_to_a_label_of_an_enclosing_iteration_statement_1115", "A 'continue' statement can only jump to a label of an enclosing iteration statement."),
  A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement: diag(1116, 1 /* Error */, "A_break_statement_can_only_jump_to_a_label_of_an_enclosing_statement_1116", "A 'break' statement can only jump to a label of an enclosing statement."),
  An_object_literal_cannot_have_multiple_properties_with_the_same_name: diag(1117, 1 /* Error */, "An_object_literal_cannot_have_multiple_properties_with_the_same_name_1117", "An object literal cannot have multiple properties with the same name."),
  An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name: diag(1118, 1 /* Error */, "An_object_literal_cannot_have_multiple_get_Slashset_accessors_with_the_same_name_1118", "An object literal cannot have multiple get/set accessors with the same name."),
  An_object_literal_cannot_have_property_and_accessor_with_the_same_name: diag(1119, 1 /* Error */, "An_object_literal_cannot_have_property_and_accessor_with_the_same_name_1119", "An object literal cannot have property and accessor with the same name."),
  An_export_assignment_cannot_have_modifiers: diag(1120, 1 /* Error */, "An_export_assignment_cannot_have_modifiers_1120", "An export assignment cannot have modifiers."),
  Octal_literals_are_not_allowed_Use_the_syntax_0: diag(1121, 1 /* Error */, "Octal_literals_are_not_allowed_Use_the_syntax_0_1121", "Octal literals are not allowed. Use the syntax '{0}'."),
  Variable_declaration_list_cannot_be_empty: diag(1123, 1 /* Error */, "Variable_declaration_list_cannot_be_empty_1123", "Variable declaration list cannot be empty."),
  Digit_expected: diag(1124, 1 /* Error */, "Digit_expected_1124", "Digit expected."),
  Hexadecimal_digit_expected: diag(1125, 1 /* Error */, "Hexadecimal_digit_expected_1125", "Hexadecimal digit expected."),
  Unexpected_end_of_text: diag(1126, 1 /* Error */, "Unexpected_end_of_text_1126", "Unexpected end of text."),
  Invalid_character: diag(1127, 1 /* Error */, "Invalid_character_1127", "Invalid character."),
  Declaration_or_statement_expected: diag(1128, 1 /* Error */, "Declaration_or_statement_expected_1128", "Declaration or statement expected."),
  Statement_expected: diag(1129, 1 /* Error */, "Statement_expected_1129", "Statement expected."),
  case_or_default_expected: diag(1130, 1 /* Error */, "case_or_default_expected_1130", "'case' or 'default' expected."),
  Property_or_signature_expected: diag(1131, 1 /* Error */, "Property_or_signature_expected_1131", "Property or signature expected."),
  Enum_member_expected: diag(1132, 1 /* Error */, "Enum_member_expected_1132", "Enum member expected."),
  Variable_declaration_expected: diag(1134, 1 /* Error */, "Variable_declaration_expected_1134", "Variable declaration expected."),
  Argument_expression_expected: diag(1135, 1 /* Error */, "Argument_expression_expected_1135", "Argument expression expected."),
  Property_assignment_expected: diag(1136, 1 /* Error */, "Property_assignment_expected_1136", "Property assignment expected."),
  Expression_or_comma_expected: diag(1137, 1 /* Error */, "Expression_or_comma_expected_1137", "Expression or comma expected."),
  Parameter_declaration_expected: diag(1138, 1 /* Error */, "Parameter_declaration_expected_1138", "Parameter declaration expected."),
  Type_parameter_declaration_expected: diag(1139, 1 /* Error */, "Type_parameter_declaration_expected_1139", "Type parameter declaration expected."),
  Type_argument_expected: diag(1140, 1 /* Error */, "Type_argument_expected_1140", "Type argument expected."),
  String_literal_expected: diag(1141, 1 /* Error */, "String_literal_expected_1141", "String literal expected."),
  Line_break_not_permitted_here: diag(1142, 1 /* Error */, "Line_break_not_permitted_here_1142", "Line break not permitted here."),
  or_expected: diag(1144, 1 /* Error */, "or_expected_1144", "'{' or ';' expected."),
  or_JSX_element_expected: diag(1145, 1 /* Error */, "or_JSX_element_expected_1145", "'{' or JSX element expected."),
  Declaration_expected: diag(1146, 1 /* Error */, "Declaration_expected_1146", "Declaration expected."),
  Import_declarations_in_a_namespace_cannot_reference_a_module: diag(1147, 1 /* Error */, "Import_declarations_in_a_namespace_cannot_reference_a_module_1147", "Import declarations in a namespace cannot reference a module."),
  Cannot_use_imports_exports_or_module_augmentations_when_module_is_none: diag(1148, 1 /* Error */, "Cannot_use_imports_exports_or_module_augmentations_when_module_is_none_1148", "Cannot use imports, exports, or module augmentations when '--module' is 'none'."),
  File_name_0_differs_from_already_included_file_name_1_only_in_casing: diag(1149, 1 /* Error */, "File_name_0_differs_from_already_included_file_name_1_only_in_casing_1149", "File name '{0}' differs from already included file name '{1}' only in casing."),
  _0_declarations_must_be_initialized: diag(1155, 1 /* Error */, "_0_declarations_must_be_initialized_1155", "'{0}' declarations must be initialized."),
  _0_declarations_can_only_be_declared_inside_a_block: diag(1156, 1 /* Error */, "_0_declarations_can_only_be_declared_inside_a_block_1156", "'{0}' declarations can only be declared inside a block."),
  Unterminated_template_literal: diag(1160, 1 /* Error */, "Unterminated_template_literal_1160", "Unterminated template literal."),
  Unterminated_regular_expression_literal: diag(1161, 1 /* Error */, "Unterminated_regular_expression_literal_1161", "Unterminated regular expression literal."),
  An_object_member_cannot_be_declared_optional: diag(1162, 1 /* Error */, "An_object_member_cannot_be_declared_optional_1162", "An object member cannot be declared optional."),
  A_yield_expression_is_only_allowed_in_a_generator_body: diag(1163, 1 /* Error */, "A_yield_expression_is_only_allowed_in_a_generator_body_1163", "A 'yield' expression is only allowed in a generator body."),
  Computed_property_names_are_not_allowed_in_enums: diag(1164, 1 /* Error */, "Computed_property_names_are_not_allowed_in_enums_1164", "Computed property names are not allowed in enums."),
  A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_type_or_a_unique_symbol_type: diag(1165, 1 /* Error */, "A_computed_property_name_in_an_ambient_context_must_refer_to_an_expression_whose_type_is_a_literal_t_1165", "A computed property name in an ambient context must refer to an expression whose type is a literal type or a 'unique symbol' type."),
  A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_symbol_type: diag(1166, 1 /* Error */, "A_computed_property_name_in_a_class_property_declaration_must_have_a_simple_literal_type_or_a_unique_1166", "A computed property name in a class property declaration must have a simple literal type or a 'unique symbol' type."),
  A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_type_or_a_unique_symbol_type: diag(1168, 1 /* Error */, "A_computed_property_name_in_a_method_overload_must_refer_to_an_expression_whose_type_is_a_literal_ty_1168", "A computed property name in a method overload must refer to an expression whose type is a literal type or a 'unique symbol' type."),
  A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_a_unique_symbol_type: diag(1169, 1 /* Error */, "A_computed_property_name_in_an_interface_must_refer_to_an_expression_whose_type_is_a_literal_type_or_1169", "A computed property name in an interface must refer to an expression whose type is a literal type or a 'unique symbol' type."),
  A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type_or_a_unique_symbol_type: diag(1170, 1 /* Error */, "A_computed_property_name_in_a_type_literal_must_refer_to_an_expression_whose_type_is_a_literal_type__1170", "A computed property name in a type literal must refer to an expression whose type is a literal type or a 'unique symbol' type."),
  A_comma_expression_is_not_allowed_in_a_computed_property_name: diag(1171, 1 /* Error */, "A_comma_expression_is_not_allowed_in_a_computed_property_name_1171", "A comma expression is not allowed in a computed property name."),
  extends_clause_already_seen: diag(1172, 1 /* Error */, "extends_clause_already_seen_1172", "'extends' clause already seen."),
  extends_clause_must_precede_implements_clause: diag(1173, 1 /* Error */, "extends_clause_must_precede_implements_clause_1173", "'extends' clause must precede 'implements' clause."),
  Classes_can_only_extend_a_single_class: diag(1174, 1 /* Error */, "Classes_can_only_extend_a_single_class_1174", "Classes can only extend a single class."),
  implements_clause_already_seen: diag(1175, 1 /* Error */, "implements_clause_already_seen_1175", "'implements' clause already seen."),
  Interface_declaration_cannot_have_implements_clause: diag(1176, 1 /* Error */, "Interface_declaration_cannot_have_implements_clause_1176", "Interface declaration cannot have 'implements' clause."),
  Binary_digit_expected: diag(1177, 1 /* Error */, "Binary_digit_expected_1177", "Binary digit expected."),
  Octal_digit_expected: diag(1178, 1 /* Error */, "Octal_digit_expected_1178", "Octal digit expected."),
  Unexpected_token_expected: diag(1179, 1 /* Error */, "Unexpected_token_expected_1179", "Unexpected token. '{' expected."),
  Property_destructuring_pattern_expected: diag(1180, 1 /* Error */, "Property_destructuring_pattern_expected_1180", "Property destructuring pattern expected."),
  Array_element_destructuring_pattern_expected: diag(1181, 1 /* Error */, "Array_element_destructuring_pattern_expected_1181", "Array element destructuring pattern expected."),
  A_destructuring_declaration_must_have_an_initializer: diag(1182, 1 /* Error */, "A_destructuring_declaration_must_have_an_initializer_1182", "A destructuring declaration must have an initializer."),
  An_implementation_cannot_be_declared_in_ambient_contexts: diag(1183, 1 /* Error */, "An_implementation_cannot_be_declared_in_ambient_contexts_1183", "An implementation cannot be declared in ambient contexts."),
  Modifiers_cannot_appear_here: diag(1184, 1 /* Error */, "Modifiers_cannot_appear_here_1184", "Modifiers cannot appear here."),
  Merge_conflict_marker_encountered: diag(1185, 1 /* Error */, "Merge_conflict_marker_encountered_1185", "Merge conflict marker encountered."),
  A_rest_element_cannot_have_an_initializer: diag(1186, 1 /* Error */, "A_rest_element_cannot_have_an_initializer_1186", "A rest element cannot have an initializer."),
  A_parameter_property_may_not_be_declared_using_a_binding_pattern: diag(1187, 1 /* Error */, "A_parameter_property_may_not_be_declared_using_a_binding_pattern_1187", "A parameter property may not be declared using a binding pattern."),
  Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement: diag(1188, 1 /* Error */, "Only_a_single_variable_declaration_is_allowed_in_a_for_of_statement_1188", "Only a single variable declaration is allowed in a 'for...of' statement."),
  The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer: diag(1189, 1 /* Error */, "The_variable_declaration_of_a_for_in_statement_cannot_have_an_initializer_1189", "The variable declaration of a 'for...in' statement cannot have an initializer."),
  The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer: diag(1190, 1 /* Error */, "The_variable_declaration_of_a_for_of_statement_cannot_have_an_initializer_1190", "The variable declaration of a 'for...of' statement cannot have an initializer."),
  An_import_declaration_cannot_have_modifiers: diag(1191, 1 /* Error */, "An_import_declaration_cannot_have_modifiers_1191", "An import declaration cannot have modifiers."),
  Module_0_has_no_default_export: diag(1192, 1 /* Error */, "Module_0_has_no_default_export_1192", "Module '{0}' has no default export."),
  An_export_declaration_cannot_have_modifiers: diag(1193, 1 /* Error */, "An_export_declaration_cannot_have_modifiers_1193", "An export declaration cannot have modifiers."),
  Export_declarations_are_not_permitted_in_a_namespace: diag(1194, 1 /* Error */, "Export_declarations_are_not_permitted_in_a_namespace_1194", "Export declarations are not permitted in a namespace."),
  export_Asterisk_does_not_re_export_a_default: diag(1195, 1 /* Error */, "export_Asterisk_does_not_re_export_a_default_1195", "'export *' does not re-export a default."),
  Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified: diag(1196, 1 /* Error */, "Catch_clause_variable_type_annotation_must_be_any_or_unknown_if_specified_1196", "Catch clause variable type annotation must be 'any' or 'unknown' if specified."),
  Catch_clause_variable_cannot_have_an_initializer: diag(1197, 1 /* Error */, "Catch_clause_variable_cannot_have_an_initializer_1197", "Catch clause variable cannot have an initializer."),
  An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive: diag(1198, 1 /* Error */, "An_extended_Unicode_escape_value_must_be_between_0x0_and_0x10FFFF_inclusive_1198", "An extended Unicode escape value must be between 0x0 and 0x10FFFF inclusive."),
  Unterminated_Unicode_escape_sequence: diag(1199, 1 /* Error */, "Unterminated_Unicode_escape_sequence_1199", "Unterminated Unicode escape sequence."),
  Line_terminator_not_permitted_before_arrow: diag(1200, 1 /* Error */, "Line_terminator_not_permitted_before_arrow_1200", "Line terminator not permitted before arrow."),
  Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_ns_from_mod_import_a_from_mod_import_d_from_mod_or_another_module_format_instead: diag(1202, 1 /* Error */, "Import_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_import_Asterisk_as_1202", `Import assignment cannot be used when targeting ECMAScript modules. Consider using 'import * as ns from "mod"', 'import {a} from "mod"', 'import d from "mod"', or another module format instead.`),
  Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or_another_module_format_instead: diag(1203, 1 /* Error */, "Export_assignment_cannot_be_used_when_targeting_ECMAScript_modules_Consider_using_export_default_or__1203", "Export assignment cannot be used when targeting ECMAScript modules. Consider using 'export default' or another module format instead."),
  Re_exporting_a_type_when_0_is_enabled_requires_using_export_type: diag(1205, 1 /* Error */, "Re_exporting_a_type_when_0_is_enabled_requires_using_export_type_1205", "Re-exporting a type when '{0}' is enabled requires using 'export type'."),
  Decorators_are_not_valid_here: diag(1206, 1 /* Error */, "Decorators_are_not_valid_here_1206", "Decorators are not valid here."),
  Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name: diag(1207, 1 /* Error */, "Decorators_cannot_be_applied_to_multiple_get_Slashset_accessors_of_the_same_name_1207", "Decorators cannot be applied to multiple get/set accessors of the same name."),
  Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0: diag(1209, 1 /* Error */, "Invalid_optional_chain_from_new_expression_Did_you_mean_to_call_0_1209", "Invalid optional chain from new expression. Did you mean to call '{0}()'?"),
  Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of_0_For_more_information_see_https_Colon_Slash_Slashdeveloper_mozilla_org_Slashen_US_Slashdocs_SlashWeb_SlashJavaScript_SlashReference_SlashStrict_mode: diag(1210, 1 /* Error */, "Code_contained_in_a_class_is_evaluated_in_JavaScript_s_strict_mode_which_does_not_allow_this_use_of__1210", "Code contained in a class is evaluated in JavaScript's strict mode which does not allow this use of '{0}'. For more information, see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Strict_mode."),
  A_class_declaration_without_the_default_modifier_must_have_a_name: diag(1211, 1 /* Error */, "A_class_declaration_without_the_default_modifier_must_have_a_name_1211", "A class declaration without the 'default' modifier must have a name."),
  Identifier_expected_0_is_a_reserved_word_in_strict_mode: diag(1212, 1 /* Error */, "Identifier_expected_0_is_a_reserved_word_in_strict_mode_1212", "Identifier expected. '{0}' is a reserved word in strict mode."),
  Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_strict_mode: diag(1213, 1 /* Error */, "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Class_definitions_are_automatically_in_stric_1213", "Identifier expected. '{0}' is a reserved word in strict mode. Class definitions are automatically in strict mode."),
  Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode: diag(1214, 1 /* Error */, "Identifier_expected_0_is_a_reserved_word_in_strict_mode_Modules_are_automatically_in_strict_mode_1214", "Identifier expected. '{0}' is a reserved word in strict mode. Modules are automatically in strict mode."),
  Invalid_use_of_0_Modules_are_automatically_in_strict_mode: diag(1215, 1 /* Error */, "Invalid_use_of_0_Modules_are_automatically_in_strict_mode_1215", "Invalid use of '{0}'. Modules are automatically in strict mode."),
  Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules: diag(1216, 1 /* Error */, "Identifier_expected_esModule_is_reserved_as_an_exported_marker_when_transforming_ECMAScript_modules_1216", "Identifier expected. '__esModule' is reserved as an exported marker when transforming ECMAScript modules."),
  Export_assignment_is_not_supported_when_module_flag_is_system: diag(1218, 1 /* Error */, "Export_assignment_is_not_supported_when_module_flag_is_system_1218", "Export assignment is not supported when '--module' flag is 'system'."),
  Generators_are_not_allowed_in_an_ambient_context: diag(1221, 1 /* Error */, "Generators_are_not_allowed_in_an_ambient_context_1221", "Generators are not allowed in an ambient context."),
  An_overload_signature_cannot_be_declared_as_a_generator: diag(1222, 1 /* Error */, "An_overload_signature_cannot_be_declared_as_a_generator_1222", "An overload signature cannot be declared as a generator."),
  _0_tag_already_specified: diag(1223, 1 /* Error */, "_0_tag_already_specified_1223", "'{0}' tag already specified."),
  Signature_0_must_be_a_type_predicate: diag(1224, 1 /* Error */, "Signature_0_must_be_a_type_predicate_1224", "Signature '{0}' must be a type predicate."),
  Cannot_find_parameter_0: diag(1225, 1 /* Error */, "Cannot_find_parameter_0_1225", "Cannot find parameter '{0}'."),
  Type_predicate_0_is_not_assignable_to_1: diag(1226, 1 /* Error */, "Type_predicate_0_is_not_assignable_to_1_1226", "Type predicate '{0}' is not assignable to '{1}'."),
  Parameter_0_is_not_in_the_same_position_as_parameter_1: diag(1227, 1 /* Error */, "Parameter_0_is_not_in_the_same_position_as_parameter_1_1227", "Parameter '{0}' is not in the same position as parameter '{1}'."),
  A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods: diag(1228, 1 /* Error */, "A_type_predicate_is_only_allowed_in_return_type_position_for_functions_and_methods_1228", "A type predicate is only allowed in return type position for functions and methods."),
  A_type_predicate_cannot_reference_a_rest_parameter: diag(1229, 1 /* Error */, "A_type_predicate_cannot_reference_a_rest_parameter_1229", "A type predicate cannot reference a rest parameter."),
  A_type_predicate_cannot_reference_element_0_in_a_binding_pattern: diag(1230, 1 /* Error */, "A_type_predicate_cannot_reference_element_0_in_a_binding_pattern_1230", "A type predicate cannot reference element '{0}' in a binding pattern."),
  An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration: diag(1231, 1 /* Error */, "An_export_assignment_must_be_at_the_top_level_of_a_file_or_module_declaration_1231", "An export assignment must be at the top level of a file or module declaration."),
  An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module: diag(1232, 1 /* Error */, "An_import_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1232", "An import declaration can only be used at the top level of a namespace or module."),
  An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module: diag(1233, 1 /* Error */, "An_export_declaration_can_only_be_used_at_the_top_level_of_a_namespace_or_module_1233", "An export declaration can only be used at the top level of a namespace or module."),
  An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file: diag(1234, 1 /* Error */, "An_ambient_module_declaration_is_only_allowed_at_the_top_level_in_a_file_1234", "An ambient module declaration is only allowed at the top level in a file."),
  A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module: diag(1235, 1 /* Error */, "A_namespace_declaration_is_only_allowed_at_the_top_level_of_a_namespace_or_module_1235", "A namespace declaration is only allowed at the top level of a namespace or module."),
  The_return_type_of_a_property_decorator_function_must_be_either_void_or_any: diag(1236, 1 /* Error */, "The_return_type_of_a_property_decorator_function_must_be_either_void_or_any_1236", "The return type of a property decorator function must be either 'void' or 'any'."),
  The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any: diag(1237, 1 /* Error */, "The_return_type_of_a_parameter_decorator_function_must_be_either_void_or_any_1237", "The return type of a parameter decorator function must be either 'void' or 'any'."),
  Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression: diag(1238, 1 /* Error */, "Unable_to_resolve_signature_of_class_decorator_when_called_as_an_expression_1238", "Unable to resolve signature of class decorator when called as an expression."),
  Unable_to_r