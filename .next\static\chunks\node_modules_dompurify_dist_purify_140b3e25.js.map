{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "file": "purify.js", "sources": ["file:///E:/ASCAES/node_modules/dompurify/src/utils.js", "file:///E:/ASCAES/node_modules/dompurify/src/tags.js", "file:///E:/ASCAES/node_modules/dompurify/src/attrs.js", "file:///E:/ASCAES/node_modules/dompurify/src/regexp.js", "file:///E:/ASCAES/node_modules/dompurify/src/purify.js"], "sourcesContent": ["const {\n  hasOwnProperty,\n  setPrototypeOf,\n  isFrozen,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n} = Object;\n\nlet { freeze, seal, create } = Object; // eslint-disable-line import/no-mutable-exports\nlet { apply, construct } = typeof Reflect !== 'undefined' && Reflect;\n\nif (!apply) {\n  apply = function (fun, thisValue, args) {\n    return fun.apply(thisValue, args);\n  };\n}\n\nif (!freeze) {\n  freeze = function (x) {\n    return x;\n  };\n}\n\nif (!seal) {\n  seal = function (x) {\n    return x;\n  };\n}\n\nif (!construct) {\n  construct = function (Func, args) {\n    return new Func(...args);\n  };\n}\n\nconst arrayForEach = unapply(Array.prototype.forEach);\nconst arrayIndexOf = unapply(Array.prototype.indexOf);\nconst arrayPop = unapply(Array.prototype.pop);\nconst arrayPush = unapply(Array.prototype.push);\nconst arraySlice = unapply(Array.prototype.slice);\n\nconst stringToLowerCase = unapply(String.prototype.toLowerCase);\nconst stringToString = unapply(String.prototype.toString);\nconst stringMatch = unapply(String.prototype.match);\nconst stringReplace = unapply(String.prototype.replace);\nconst stringIndexOf = unapply(String.prototype.indexOf);\nconst stringTrim = unapply(String.prototype.trim);\n\nconst regExpTest = unapply(RegExp.prototype.test);\n\nconst typeErrorCreate = unconstruct(TypeError);\n\nexport function unapply(func) {\n  return (thisArg, ...args) => apply(func, thisArg, args);\n}\n\nexport function unconstruct(func) {\n  return (...args) => construct(func, args);\n}\n\n/* Add properties to a lookup table */\nexport function addToSet(set, array, transformCaseFunc) {\n  transformCaseFunc = transformCaseFunc ?? stringToLowerCase;\n  if (setPrototypeOf) {\n    // Make 'in' and truthy checks like Boolean(set.constructor)\n    // independent of any properties defined on Object.prototype.\n    // Prevent prototype setters from intercepting set as a this value.\n    setPrototypeOf(set, null);\n  }\n\n  let l = array.length;\n  while (l--) {\n    let element = array[l];\n    if (typeof element === 'string') {\n      const lcElement = transformCaseFunc(element);\n      if (lcElement !== element) {\n        // Config presets (e.g. tags.js, attrs.js) are immutable.\n        if (!isFrozen(array)) {\n          array[l] = lcElement;\n        }\n\n        element = lcElement;\n      }\n    }\n\n    set[element] = true;\n  }\n\n  return set;\n}\n\n/* Shallow clone an object */\nexport function clone(object) {\n  const newObject = create(null);\n\n  let property;\n  for (property in object) {\n    if (apply(hasOwnProperty, object, [property]) === true) {\n      newObject[property] = object[property];\n    }\n  }\n\n  return newObject;\n}\n\n/* IE10 doesn't support __lookupGetter__ so lets'\n * simulate it. It also automatically checks\n * if the prop is function or getter and behaves\n * accordingly. */\nfunction lookupGetter(object, prop) {\n  while (object !== null) {\n    const desc = getOwnPropertyDescriptor(object, prop);\n    if (desc) {\n      if (desc.get) {\n        return unapply(desc.get);\n      }\n\n      if (typeof desc.value === 'function') {\n        return unapply(desc.value);\n      }\n    }\n\n    object = getPrototypeOf(object);\n  }\n\n  function fallbackValue(element) {\n    console.warn('fallback value for', element);\n    return null;\n  }\n\n  return fallbackValue;\n}\n\nexport {\n  // Array\n  arrayForEach,\n  arrayIndexOf,\n  arrayPop,\n  arrayPush,\n  arraySlice,\n  // Object\n  freeze,\n  getPrototypeOf,\n  getOwnPropertyDescriptor,\n  hasOwnProperty,\n  isFrozen,\n  setPrototypeOf,\n  seal,\n  // RegExp\n  regExpTest,\n  // String\n  stringIndexOf,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringTrim,\n  // Errors\n  typeErrorCreate,\n  // Other\n  lookupGetter,\n};\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'a',\n  'abbr',\n  'acronym',\n  'address',\n  'area',\n  'article',\n  'aside',\n  'audio',\n  'b',\n  'bdi',\n  'bdo',\n  'big',\n  'blink',\n  'blockquote',\n  'body',\n  'br',\n  'button',\n  'canvas',\n  'caption',\n  'center',\n  'cite',\n  'code',\n  'col',\n  'colgroup',\n  'content',\n  'data',\n  'datalist',\n  'dd',\n  'decorator',\n  'del',\n  'details',\n  'dfn',\n  'dialog',\n  'dir',\n  'div',\n  'dl',\n  'dt',\n  'element',\n  'em',\n  'fieldset',\n  'figcaption',\n  'figure',\n  'font',\n  'footer',\n  'form',\n  'h1',\n  'h2',\n  'h3',\n  'h4',\n  'h5',\n  'h6',\n  'head',\n  'header',\n  'hgroup',\n  'hr',\n  'html',\n  'i',\n  'img',\n  'input',\n  'ins',\n  'kbd',\n  'label',\n  'legend',\n  'li',\n  'main',\n  'map',\n  'mark',\n  'marquee',\n  'menu',\n  'menuitem',\n  'meter',\n  'nav',\n  'nobr',\n  'ol',\n  'optgroup',\n  'option',\n  'output',\n  'p',\n  'picture',\n  'pre',\n  'progress',\n  'q',\n  'rp',\n  'rt',\n  'ruby',\n  's',\n  'samp',\n  'section',\n  'select',\n  'shadow',\n  'small',\n  'source',\n  'spacer',\n  'span',\n  'strike',\n  'strong',\n  'style',\n  'sub',\n  'summary',\n  'sup',\n  'table',\n  'tbody',\n  'td',\n  'template',\n  'textarea',\n  'tfoot',\n  'th',\n  'thead',\n  'time',\n  'tr',\n  'track',\n  'tt',\n  'u',\n  'ul',\n  'var',\n  'video',\n  'wbr',\n]);\n\n// SVG\nexport const svg = freeze([\n  'svg',\n  'a',\n  'altglyph',\n  'altglyphdef',\n  'altglyphitem',\n  'animatecolor',\n  'animatemotion',\n  'animatetransform',\n  'circle',\n  'clippath',\n  'defs',\n  'desc',\n  'ellipse',\n  'filter',\n  'font',\n  'g',\n  'glyph',\n  'glyphref',\n  'hkern',\n  'image',\n  'line',\n  'lineargradient',\n  'marker',\n  'mask',\n  'metadata',\n  'mpath',\n  'path',\n  'pattern',\n  'polygon',\n  'polyline',\n  'radialgradient',\n  'rect',\n  'stop',\n  'style',\n  'switch',\n  'symbol',\n  'text',\n  'textpath',\n  'title',\n  'tref',\n  'tspan',\n  'view',\n  'vkern',\n]);\n\nexport const svgFilters = freeze([\n  'feBlend',\n  'feColorMatrix',\n  'feComponentTransfer',\n  'feComposite',\n  'feConvolveMatrix',\n  'feDiffuseLighting',\n  'feDisplacementMap',\n  'feDistantLight',\n  'feFlood',\n  'feFuncA',\n  'feFuncB',\n  'feFuncG',\n  'feFuncR',\n  'feGaussianBlur',\n  'feImage',\n  'feMerge',\n  'feMergeNode',\n  'feMorphology',\n  'feOffset',\n  'fePointLight',\n  'feSpecularLighting',\n  'feSpotLight',\n  'feTile',\n  'feTurbulence',\n]);\n\n// List of SVG elements that are disallowed by default.\n// We still need to know them so that we can do namespace\n// checks properly in case one wants to add them to\n// allow-list.\nexport const svgDisallowed = freeze([\n  'animate',\n  'color-profile',\n  'cursor',\n  'discard',\n  'fedropshadow',\n  'font-face',\n  'font-face-format',\n  'font-face-name',\n  'font-face-src',\n  'font-face-uri',\n  'foreignobject',\n  'hatch',\n  'hatchpath',\n  'mesh',\n  'meshgradient',\n  'meshpatch',\n  'meshrow',\n  'missing-glyph',\n  'script',\n  'set',\n  'solidcolor',\n  'unknown',\n  'use',\n]);\n\nexport const mathMl = freeze([\n  'math',\n  'menclose',\n  'merror',\n  'mfenced',\n  'mfrac',\n  'mglyph',\n  'mi',\n  'mlabeledtr',\n  'mmultiscripts',\n  'mn',\n  'mo',\n  'mover',\n  'mpadded',\n  'mphantom',\n  'mroot',\n  'mrow',\n  'ms',\n  'mspace',\n  'msqrt',\n  'mstyle',\n  'msub',\n  'msup',\n  'msubsup',\n  'mtable',\n  'mtd',\n  'mtext',\n  'mtr',\n  'munder',\n  'munderover',\n]);\n\n// Similarly to SVG, we want to know all MathML elements,\n// even those that we disallow by default.\nexport const mathMlDisallowed = freeze([\n  'maction',\n  'maligngroup',\n  'malignmark',\n  'mlongdiv',\n  'mscarries',\n  'mscarry',\n  'msgroup',\n  'mstack',\n  'msline',\n  'msrow',\n  'semantics',\n  'annotation',\n  'annotation-xml',\n  'mprescripts',\n  'none',\n]);\n\nexport const text = freeze(['#text']);\n", "import { freeze } from './utils.js';\n\nexport const html = freeze([\n  'accept',\n  'action',\n  'align',\n  'alt',\n  'autocapitalize',\n  'autocomplete',\n  'autopictureinpicture',\n  'autoplay',\n  'background',\n  'bgcolor',\n  'border',\n  'capture',\n  'cellpadding',\n  'cellspacing',\n  'checked',\n  'cite',\n  'class',\n  'clear',\n  'color',\n  'cols',\n  'colspan',\n  'controls',\n  'controlslist',\n  'coords',\n  'crossorigin',\n  'datetime',\n  'decoding',\n  'default',\n  'dir',\n  'disabled',\n  'disablepictureinpicture',\n  'disableremoteplayback',\n  'download',\n  'draggable',\n  'enctype',\n  'enterkeyhint',\n  'face',\n  'for',\n  'headers',\n  'height',\n  'hidden',\n  'high',\n  'href',\n  'hreflang',\n  'id',\n  'inputmode',\n  'integrity',\n  'ismap',\n  'kind',\n  'label',\n  'lang',\n  'list',\n  'loading',\n  'loop',\n  'low',\n  'max',\n  'maxlength',\n  'media',\n  'method',\n  'min',\n  'minlength',\n  'multiple',\n  'muted',\n  'name',\n  'nonce',\n  'noshade',\n  'novalidate',\n  'nowrap',\n  'open',\n  'optimum',\n  'pattern',\n  'placeholder',\n  'playsinline',\n  'poster',\n  'preload',\n  'pubdate',\n  'radiogroup',\n  'readonly',\n  'rel',\n  'required',\n  'rev',\n  'reversed',\n  'role',\n  'rows',\n  'rowspan',\n  'spellcheck',\n  'scope',\n  'selected',\n  'shape',\n  'size',\n  'sizes',\n  'span',\n  'srclang',\n  'start',\n  'src',\n  'srcset',\n  'step',\n  'style',\n  'summary',\n  'tabindex',\n  'title',\n  'translate',\n  'type',\n  'usemap',\n  'valign',\n  'value',\n  'width',\n  'xmlns',\n  'slot',\n]);\n\nexport const svg = freeze([\n  'accent-height',\n  'accumulate',\n  'additive',\n  'alignment-baseline',\n  'ascent',\n  'attributename',\n  'attributetype',\n  'azimuth',\n  'basefrequency',\n  'baseline-shift',\n  'begin',\n  'bias',\n  'by',\n  'class',\n  'clip',\n  'clippathunits',\n  'clip-path',\n  'clip-rule',\n  'color',\n  'color-interpolation',\n  'color-interpolation-filters',\n  'color-profile',\n  'color-rendering',\n  'cx',\n  'cy',\n  'd',\n  'dx',\n  'dy',\n  'diffuseconstant',\n  'direction',\n  'display',\n  'divisor',\n  'dur',\n  'edgemode',\n  'elevation',\n  'end',\n  'fill',\n  'fill-opacity',\n  'fill-rule',\n  'filter',\n  'filterunits',\n  'flood-color',\n  'flood-opacity',\n  'font-family',\n  'font-size',\n  'font-size-adjust',\n  'font-stretch',\n  'font-style',\n  'font-variant',\n  'font-weight',\n  'fx',\n  'fy',\n  'g1',\n  'g2',\n  'glyph-name',\n  'glyphref',\n  'gradientunits',\n  'gradienttransform',\n  'height',\n  'href',\n  'id',\n  'image-rendering',\n  'in',\n  'in2',\n  'k',\n  'k1',\n  'k2',\n  'k3',\n  'k4',\n  'kerning',\n  'keypoints',\n  'keysplines',\n  'keytimes',\n  'lang',\n  'lengthadjust',\n  'letter-spacing',\n  'kernelmatrix',\n  'kernelunitlength',\n  'lighting-color',\n  'local',\n  'marker-end',\n  'marker-mid',\n  'marker-start',\n  'markerheight',\n  'markerunits',\n  'markerwidth',\n  'maskcontentunits',\n  'maskunits',\n  'max',\n  'mask',\n  'media',\n  'method',\n  'mode',\n  'min',\n  'name',\n  'numoctaves',\n  'offset',\n  'operator',\n  'opacity',\n  'order',\n  'orient',\n  'orientation',\n  'origin',\n  'overflow',\n  'paint-order',\n  'path',\n  'pathlength',\n  'patterncontentunits',\n  'patterntransform',\n  'patternunits',\n  'points',\n  'preservealpha',\n  'preserveaspectratio',\n  'primitiveunits',\n  'r',\n  'rx',\n  'ry',\n  'radius',\n  'refx',\n  'refy',\n  'repeatcount',\n  'repeatdur',\n  'restart',\n  'result',\n  'rotate',\n  'scale',\n  'seed',\n  'shape-rendering',\n  'specularconstant',\n  'specularexponent',\n  'spreadmethod',\n  'startoffset',\n  'stddeviation',\n  'stitchtiles',\n  'stop-color',\n  'stop-opacity',\n  'stroke-dasharray',\n  'stroke-dashoffset',\n  'stroke-linecap',\n  'stroke-linejoin',\n  'stroke-miterlimit',\n  'stroke-opacity',\n  'stroke',\n  'stroke-width',\n  'style',\n  'surfacescale',\n  'systemlanguage',\n  'tabindex',\n  'targetx',\n  'targety',\n  'transform',\n  'transform-origin',\n  'text-anchor',\n  'text-decoration',\n  'text-rendering',\n  'textlength',\n  'type',\n  'u1',\n  'u2',\n  'unicode',\n  'values',\n  'viewbox',\n  'visibility',\n  'version',\n  'vert-adv-y',\n  'vert-origin-x',\n  'vert-origin-y',\n  'width',\n  'word-spacing',\n  'wrap',\n  'writing-mode',\n  'xchannelselector',\n  'ychannelselector',\n  'x',\n  'x1',\n  'x2',\n  'xmlns',\n  'y',\n  'y1',\n  'y2',\n  'z',\n  'zoomandpan',\n]);\n\nexport const mathMl = freeze([\n  'accent',\n  'accentunder',\n  'align',\n  'bevelled',\n  'close',\n  'columnsalign',\n  'columnlines',\n  'columnspan',\n  'denomalign',\n  'depth',\n  'dir',\n  'display',\n  'displaystyle',\n  'encoding',\n  'fence',\n  'frame',\n  'height',\n  'href',\n  'id',\n  'largeop',\n  'length',\n  'linethickness',\n  'lspace',\n  'lquote',\n  'mathbackground',\n  'mathcolor',\n  'mathsize',\n  'mathvariant',\n  'maxsize',\n  'minsize',\n  'movablelimits',\n  'notation',\n  'numalign',\n  'open',\n  'rowalign',\n  'rowlines',\n  'rowspacing',\n  'rowspan',\n  'rspace',\n  'rquote',\n  'scriptlevel',\n  'scriptminsize',\n  'scriptsizemultiplier',\n  'selection',\n  'separator',\n  'separators',\n  'stretchy',\n  'subscriptshift',\n  'supscriptshift',\n  'symmetric',\n  'voffset',\n  'width',\n  'xmlns',\n]);\n\nexport const xml = freeze([\n  'xlink:href',\n  'xml:id',\n  'xlink:title',\n  'xml:space',\n  'xmlns:xlink',\n]);\n", "import { seal } from './utils.js';\n\n// eslint-disable-next-line unicorn/better-regex\nexport const MUSTACHE_EXPR = seal(/\\{\\{[\\w\\W]*|[\\w\\W]*\\}\\}/gm); // Specify template detection regex for SAFE_FOR_TEMPLATES mode\nexport const ERB_EXPR = seal(/<%[\\w\\W]*|[\\w\\W]*%>/gm);\nexport const TMPLIT_EXPR = seal(/\\${[\\w\\W]*}/gm);\nexport const DATA_ATTR = seal(/^data-[\\-\\w.\\u00B7-\\uFFFF]+$/); // eslint-disable-line no-useless-escape\nexport const ARIA_ATTR = seal(/^aria-[\\-\\w]+$/); // eslint-disable-line no-useless-escape\nexport const IS_ALLOWED_URI = seal(\n  /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))/i // eslint-disable-line no-useless-escape\n);\nexport const IS_SCRIPT_OR_DATA = seal(/^(?:\\w+script|data):/i);\nexport const ATTR_WHITESPACE = seal(\n  /[\\u0000-\\u0020\\u00A0\\u1680\\u180E\\u2000-\\u2029\\u205F\\u3000]/g // eslint-disable-line no-control-regex\n);\nexport const DOCTYPE_NAME = seal(/^html$/i);\nexport const CUSTOM_ELEMENT = seal(/^[a-z][.\\w]*(-[.\\w]+)+$/i);\n", "import * as TAGS from './tags.js';\nimport * as ATTRS from './attrs.js';\nimport * as EXPRESSIONS from './regexp.js';\nimport {\n  addToSet,\n  clone,\n  freeze,\n  arrayForEach,\n  arrayPop,\n  arrayPush,\n  stringMatch,\n  stringReplace,\n  stringToLowerCase,\n  stringToString,\n  stringIndexOf,\n  stringTrim,\n  regExpTest,\n  typeErrorCreate,\n  lookupGetter,\n} from './utils.js';\n\nconst getGlobal = () => (typeof window === 'undefined' ? null : window);\n\n/**\n * Creates a no-op policy for internal use only.\n * Don't export this function outside this module!\n * @param {?TrustedTypePolicyFactory} trustedTypes The policy factory.\n * @param {Document} document The document object (to determine policy name suffix)\n * @return {?TrustedTypePolicy} The policy created (or null, if Trusted Types\n * are not supported).\n */\nconst _createTrustedTypesPolicy = function (trustedTypes, document) {\n  if (\n    typeof trustedTypes !== 'object' ||\n    typeof trustedTypes.createPolicy !== 'function'\n  ) {\n    return null;\n  }\n\n  // Allow the callers to control the unique policy name\n  // by adding a data-tt-policy-suffix to the script element with the DOMPurify.\n  // Policy creation with duplicate names throws in Trusted Types.\n  let suffix = null;\n  const ATTR_NAME = 'data-tt-policy-suffix';\n  if (\n    document.currentScript &&\n    document.currentScript.hasAttribute(ATTR_NAME)\n  ) {\n    suffix = document.currentScript.getAttribute(ATTR_NAME);\n  }\n\n  const policyName = 'dompurify' + (suffix ? '#' + suffix : '');\n\n  try {\n    return trustedTypes.createPolicy(policyName, {\n      createHTML(html) {\n        return html;\n      },\n      createScriptURL(scriptUrl) {\n        return scriptUrl;\n      },\n    });\n  } catch (_) {\n    // Policy creation failed (most likely another DOMPurify script has\n    // already run). Skip creating the policy, as this will only cause errors\n    // if TT are enforced.\n    console.warn(\n      'TrustedTypes policy ' + policyName + ' could not be created.'\n    );\n    return null;\n  }\n};\n\nfunction createDOMPurify(window = getGlobal()) {\n  const DOMPurify = (root) => createDOMPurify(root);\n\n  /**\n   * Version label, exposed for easier checks\n   * if DOMPurify is up to date or not\n   */\n  DOMPurify.version = VERSION;\n\n  /**\n   * Array of elements that DOMPurify removed during sanitation.\n   * Empty if nothing was removed.\n   */\n  DOMPurify.removed = [];\n\n  if (!window || !window.document || window.document.nodeType !== 9) {\n    // Not running in a browser, provide a factory function\n    // so that you can pass your own Window\n    DOMPurify.isSupported = false;\n\n    return DOMPurify;\n  }\n\n  const originalDocument = window.document;\n\n  let { document } = window;\n  const {\n    DocumentFragment,\n    HTMLTemplateElement,\n    Node,\n    Element,\n    NodeFilter,\n    NamedNodeMap = window.NamedNodeMap || window.MozNamedAttrMap,\n    HTMLFormElement,\n    DOMParser,\n    trustedTypes,\n  } = window;\n\n  const ElementPrototype = Element.prototype;\n\n  const cloneNode = lookupGetter(ElementPrototype, 'cloneNode');\n  const getNextSibling = lookupGetter(ElementPrototype, 'nextSibling');\n  const getChildNodes = lookupGetter(ElementPrototype, 'childNodes');\n  const getParentNode = lookupGetter(ElementPrototype, 'parentNode');\n\n  // As per issue #47, the web-components registry is inherited by a\n  // new document created via createHTMLDocument. As per the spec\n  // (http://w3c.github.io/webcomponents/spec/custom/#creating-and-passing-registries)\n  // a new empty registry is used when creating a template contents owner\n  // document, so we use that as our parent document to ensure nothing\n  // is inherited.\n  if (typeof HTMLTemplateElement === 'function') {\n    const template = document.createElement('template');\n    if (template.content && template.content.ownerDocument) {\n      document = template.content.ownerDocument;\n    }\n  }\n\n  const trustedTypesPolicy = _createTrustedTypesPolicy(\n    trustedTypes,\n    originalDocument\n  );\n  const emptyHTML = trustedTypesPolicy ? trustedTypesPolicy.createHTML('') : '';\n\n  const {\n    implementation,\n    createNodeIterator,\n    createDocumentFragment,\n    getElementsByTagName,\n  } = document;\n  const { importNode } = originalDocument;\n\n  let documentMode = {};\n  try {\n    documentMode = clone(document).documentMode ? document.documentMode : {};\n  } catch (_) {}\n\n  let hooks = {};\n\n  /**\n   * Expose whether this browser supports running the full DOMPurify.\n   */\n  DOMPurify.isSupported =\n    typeof getParentNode === 'function' &&\n    implementation &&\n    implementation.createHTMLDocument !== undefined &&\n    documentMode !== 9;\n\n  const {\n    MUSTACHE_EXPR,\n    ERB_EXPR,\n    TMPLIT_EXPR,\n    DATA_ATTR,\n    ARIA_ATTR,\n    IS_SCRIPT_OR_DATA,\n    ATTR_WHITESPACE,\n    CUSTOM_ELEMENT,\n  } = EXPRESSIONS;\n\n  let { IS_ALLOWED_URI } = EXPRESSIONS;\n\n  /**\n   * We consider the elements and attributes below to be safe. Ideally\n   * don't add any new ones but feel free to remove unwanted ones.\n   */\n\n  /* allowed element names */\n  let ALLOWED_TAGS = null;\n  const DEFAULT_ALLOWED_TAGS = addToSet({}, [\n    ...TAGS.html,\n    ...TAGS.svg,\n    ...TAGS.svgFilters,\n    ...TAGS.mathMl,\n    ...TAGS.text,\n  ]);\n\n  /* Allowed attribute names */\n  let ALLOWED_ATTR = null;\n  const DEFAULT_ALLOWED_ATTR = addToSet({}, [\n    ...ATTRS.html,\n    ...ATTRS.svg,\n    ...ATTRS.mathMl,\n    ...ATTRS.xml,\n  ]);\n\n  /*\n   * Configure how DOMPUrify should handle custom elements and their attributes as well as customized built-in elements.\n   * @property {RegExp|Function|null} tagNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any custom elements)\n   * @property {RegExp|Function|null} attributeNameCheck one of [null, regexPattern, predicate]. Default: `null` (disallow any attributes not on the allow list)\n   * @property {boolean} allowCustomizedBuiltInElements allow custom elements derived from built-ins if they pass CUSTOM_ELEMENT_HANDLING.tagNameCheck. Default: `false`.\n   */\n  let CUSTOM_ELEMENT_HANDLING = Object.seal(\n    Object.create(null, {\n      tagNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      attributeNameCheck: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: null,\n      },\n      allowCustomizedBuiltInElements: {\n        writable: true,\n        configurable: false,\n        enumerable: true,\n        value: false,\n      },\n    })\n  );\n\n  /* Explicitly forbidden tags (overrides ALLOWED_TAGS/ADD_TAGS) */\n  let FORBID_TAGS = null;\n\n  /* Explicitly forbidden attributes (overrides ALLOWED_ATTR/ADD_ATTR) */\n  let FORBID_ATTR = null;\n\n  /* Decide if ARIA attributes are okay */\n  let ALLOW_ARIA_ATTR = true;\n\n  /* Decide if custom data attributes are okay */\n  let ALLOW_DATA_ATTR = true;\n\n  /* Decide if unknown protocols are okay */\n  let ALLOW_UNKNOWN_PROTOCOLS = false;\n\n  /* Decide if self-closing tags in attributes are allowed.\n   * Usually removed due to a mXSS issue in jQuery 3.0 */\n  let ALLOW_SELF_CLOSE_IN_ATTR = true;\n\n  /* Output should be safe for common template engines.\n   * This means, DOMPurify removes data attributes, mustaches and ERB\n   */\n  let SAFE_FOR_TEMPLATES = false;\n\n  /* Output should be safe even for XML used within HTML and alike.\n   * This means, DOMPurify removes comments when containing risky content.\n   */\n  let SAFE_FOR_XML = true;\n\n  /* Decide if document with <html>... should be returned */\n  let WHOLE_DOCUMENT = false;\n\n  /* Track whether config is already set on this instance of DOMPurify. */\n  let SET_CONFIG = false;\n\n  /* Decide if all elements (e.g. style, script) must be children of\n   * document.body. By default, browsers might move them to document.head */\n  let FORCE_BODY = false;\n\n  /* Decide if a DOM `HTMLBodyElement` should be returned, instead of a html\n   * string (or a TrustedHTML object if Trusted Types are supported).\n   * If `WHOLE_DOCUMENT` is enabled a `HTMLHtmlElement` will be returned instead\n   */\n  let RETURN_DOM = false;\n\n  /* Decide if a DOM `DocumentFragment` should be returned, instead of a html\n   * string  (or a TrustedHTML object if Trusted Types are supported) */\n  let RETURN_DOM_FRAGMENT = false;\n\n  /* Try to return a Trusted Type object instead of a string, return a string in\n   * case Trusted Types are not supported  */\n  let RETURN_TRUSTED_TYPE = false;\n\n  /* Output should be free from DOM clobbering attacks?\n   * This sanitizes markups named with colliding, clobberable built-in DOM APIs.\n   */\n  let SANITIZE_DOM = true;\n\n  /* Achieve full DOM Clobbering protection by isolating the namespace of named\n   * properties and JS variables, mitigating attacks that abuse the HTML/DOM spec rules.\n   *\n   * HTML/DOM spec rules that enable DOM Clobbering:\n   *   - Named Access on Window (§7.3.3)\n   *   - DOM Tree Accessors (§3.1.5)\n   *   - Form Element Parent-Child Relations (§4.10.3)\n   *   - Iframe srcdoc / Nested WindowProxies (§4.8.5)\n   *   - HTMLCollection (§4.2.10.2)\n   *\n   * Namespace isolation is implemented by prefixing `id` and `name` attributes\n   * with a constant string, i.e., `user-content-`\n   */\n  let SANITIZE_NAMED_PROPS = false;\n  const SANITIZE_NAMED_PROPS_PREFIX = 'user-content-';\n\n  /* Keep element content when removing element? */\n  let KEEP_CONTENT = true;\n\n  /* If a `Node` is passed to sanitize(), then performs sanitization in-place instead\n   * of importing it into a new Document and returning a sanitized copy */\n  let IN_PLACE = false;\n\n  /* Allow usage of profiles like html, svg and mathMl */\n  let USE_PROFILES = {};\n\n  /* Tags to ignore content of when KEEP_CONTENT is true */\n  let FORBID_CONTENTS = null;\n  const DEFAULT_FORBID_CONTENTS = addToSet({}, [\n    'annotation-xml',\n    'audio',\n    'colgroup',\n    'desc',\n    'foreignobject',\n    'head',\n    'iframe',\n    'math',\n    'mi',\n    'mn',\n    'mo',\n    'ms',\n    'mtext',\n    'noembed',\n    'noframes',\n    'noscript',\n    'plaintext',\n    'script',\n    'style',\n    'svg',\n    'template',\n    'thead',\n    'title',\n    'video',\n    'xmp',\n  ]);\n\n  /* Tags that are safe for data: URIs */\n  let DATA_URI_TAGS = null;\n  const DEFAULT_DATA_URI_TAGS = addToSet({}, [\n    'audio',\n    'video',\n    'img',\n    'source',\n    'image',\n    'track',\n  ]);\n\n  /* Attributes safe for values like \"javascript:\" */\n  let URI_SAFE_ATTRIBUTES = null;\n  const DEFAULT_URI_SAFE_ATTRIBUTES = addToSet({}, [\n    'alt',\n    'class',\n    'for',\n    'id',\n    'label',\n    'name',\n    'pattern',\n    'placeholder',\n    'role',\n    'summary',\n    'title',\n    'value',\n    'style',\n    'xmlns',\n  ]);\n\n  const MATHML_NAMESPACE = 'http://www.w3.org/1998/Math/MathML';\n  const SVG_NAMESPACE = 'http://www.w3.org/2000/svg';\n  const HTML_NAMESPACE = 'http://www.w3.org/1999/xhtml';\n  /* Document namespace */\n  let NAMESPACE = HTML_NAMESPACE;\n  let IS_EMPTY_INPUT = false;\n\n  /* Allowed XHTML+XML namespaces */\n  let ALLOWED_NAMESPACES = null;\n  const DEFAULT_ALLOWED_NAMESPACES = addToSet(\n    {},\n    [MATHML_NAMESPACE, SVG_NAMESPACE, HTML_NAMESPACE],\n    stringToString\n  );\n\n  /* Parsing of strict XHTML documents */\n  let PARSER_MEDIA_TYPE;\n  const SUPPORTED_PARSER_MEDIA_TYPES = ['application/xhtml+xml', 'text/html'];\n  const DEFAULT_PARSER_MEDIA_TYPE = 'text/html';\n  let transformCaseFunc;\n\n  /* Keep a reference to config to pass to hooks */\n  let CONFIG = null;\n\n  /* Ideally, do not touch anything below this line */\n  /* ______________________________________________ */\n\n  const formElement = document.createElement('form');\n\n  const isRegexOrFunction = function (testValue) {\n    return testValue instanceof RegExp || testValue instanceof Function;\n  };\n\n  /**\n   * _parseConfig\n   *\n   * @param  {Object} cfg optional config literal\n   */\n  // eslint-disable-next-line complexity\n  const _parseConfig = function (cfg) {\n    if (CONFIG && CONFIG === cfg) {\n      return;\n    }\n\n    /* Shield configuration object from tampering */\n    if (!cfg || typeof cfg !== 'object') {\n      cfg = {};\n    }\n\n    /* Shield configuration object from prototype pollution */\n    cfg = clone(cfg);\n\n    PARSER_MEDIA_TYPE =\n      // eslint-disable-next-line unicorn/prefer-includes\n      SUPPORTED_PARSER_MEDIA_TYPES.indexOf(cfg.PARSER_MEDIA_TYPE) === -1\n        ? (PARSER_MEDIA_TYPE = DEFAULT_PARSER_MEDIA_TYPE)\n        : (PARSER_MEDIA_TYPE = cfg.PARSER_MEDIA_TYPE);\n\n    // HTML tags and attributes are not case-sensitive, converting to lowercase. Keeping XHTML as is.\n    transformCaseFunc =\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml'\n        ? stringToString\n        : stringToLowerCase;\n\n    /* Set configuration parameters */\n    ALLOWED_TAGS =\n      'ALLOWED_TAGS' in cfg\n        ? addToSet({}, cfg.ALLOWED_TAGS, transformCaseFunc)\n        : DEFAULT_ALLOWED_TAGS;\n    ALLOWED_ATTR =\n      'ALLOWED_ATTR' in cfg\n        ? addToSet({}, cfg.ALLOWED_ATTR, transformCaseFunc)\n        : DEFAULT_ALLOWED_ATTR;\n    ALLOWED_NAMESPACES =\n      'ALLOWED_NAMESPACES' in cfg\n        ? addToSet({}, cfg.ALLOWED_NAMESPACES, stringToString)\n        : DEFAULT_ALLOWED_NAMESPACES;\n    URI_SAFE_ATTRIBUTES =\n      'ADD_URI_SAFE_ATTR' in cfg\n        ? addToSet(\n            clone(DEFAULT_URI_SAFE_ATTRIBUTES), // eslint-disable-line indent\n            cfg.ADD_URI_SAFE_ATTR, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_URI_SAFE_ATTRIBUTES;\n    DATA_URI_TAGS =\n      'ADD_DATA_URI_TAGS' in cfg\n        ? addToSet(\n            clone(DEFAULT_DATA_URI_TAGS), // eslint-disable-line indent\n            cfg.ADD_DATA_URI_TAGS, // eslint-disable-line indent\n            transformCaseFunc // eslint-disable-line indent\n          ) // eslint-disable-line indent\n        : DEFAULT_DATA_URI_TAGS;\n    FORBID_CONTENTS =\n      'FORBID_CONTENTS' in cfg\n        ? addToSet({}, cfg.FORBID_CONTENTS, transformCaseFunc)\n        : DEFAULT_FORBID_CONTENTS;\n    FORBID_TAGS =\n      'FORBID_TAGS' in cfg\n        ? addToSet({}, cfg.FORBID_TAGS, transformCaseFunc)\n        : {};\n    FORBID_ATTR =\n      'FORBID_ATTR' in cfg\n        ? addToSet({}, cfg.FORBID_ATTR, transformCaseFunc)\n        : {};\n    USE_PROFILES = 'USE_PROFILES' in cfg ? cfg.USE_PROFILES : false;\n    ALLOW_ARIA_ATTR = cfg.ALLOW_ARIA_ATTR !== false; // Default true\n    ALLOW_DATA_ATTR = cfg.ALLOW_DATA_ATTR !== false; // Default true\n    ALLOW_UNKNOWN_PROTOCOLS = cfg.ALLOW_UNKNOWN_PROTOCOLS || false; // Default false\n    ALLOW_SELF_CLOSE_IN_ATTR = cfg.ALLOW_SELF_CLOSE_IN_ATTR !== false; // Default true\n    SAFE_FOR_TEMPLATES = cfg.SAFE_FOR_TEMPLATES || false; // Default false\n    SAFE_FOR_XML = cfg.SAFE_FOR_XML !== false; // Default true\n    WHOLE_DOCUMENT = cfg.WHOLE_DOCUMENT || false; // Default false\n    RETURN_DOM = cfg.RETURN_DOM || false; // Default false\n    RETURN_DOM_FRAGMENT = cfg.RETURN_DOM_FRAGMENT || false; // Default false\n    RETURN_TRUSTED_TYPE = cfg.RETURN_TRUSTED_TYPE || false; // Default false\n    FORCE_BODY = cfg.FORCE_BODY || false; // Default false\n    SANITIZE_DOM = cfg.SANITIZE_DOM !== false; // Default true\n    SANITIZE_NAMED_PROPS = cfg.SANITIZE_NAMED_PROPS || false; // Default false\n    KEEP_CONTENT = cfg.KEEP_CONTENT !== false; // Default true\n    IN_PLACE = cfg.IN_PLACE || false; // Default false\n    IS_ALLOWED_URI = cfg.ALLOWED_URI_REGEXP || IS_ALLOWED_URI;\n    NAMESPACE = cfg.NAMESPACE || HTML_NAMESPACE;\n    CUSTOM_ELEMENT_HANDLING = cfg.CUSTOM_ELEMENT_HANDLING || {};\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.tagNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.tagNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      isRegexOrFunction(cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)\n    ) {\n      CUSTOM_ELEMENT_HANDLING.attributeNameCheck =\n        cfg.CUSTOM_ELEMENT_HANDLING.attributeNameCheck;\n    }\n\n    if (\n      cfg.CUSTOM_ELEMENT_HANDLING &&\n      typeof cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements ===\n        'boolean'\n    ) {\n      CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements =\n        cfg.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements;\n    }\n\n    if (SAFE_FOR_TEMPLATES) {\n      ALLOW_DATA_ATTR = false;\n    }\n\n    if (RETURN_DOM_FRAGMENT) {\n      RETURN_DOM = true;\n    }\n\n    /* Parse profile info */\n    if (USE_PROFILES) {\n      ALLOWED_TAGS = addToSet({}, [...TAGS.text]);\n      ALLOWED_ATTR = [];\n      if (USE_PROFILES.html === true) {\n        addToSet(ALLOWED_TAGS, TAGS.html);\n        addToSet(ALLOWED_ATTR, ATTRS.html);\n      }\n\n      if (USE_PROFILES.svg === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.svgFilters === true) {\n        addToSet(ALLOWED_TAGS, TAGS.svgFilters);\n        addToSet(ALLOWED_ATTR, ATTRS.svg);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n\n      if (USE_PROFILES.mathMl === true) {\n        addToSet(ALLOWED_TAGS, TAGS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.mathMl);\n        addToSet(ALLOWED_ATTR, ATTRS.xml);\n      }\n    }\n\n    /* Merge configuration parameters */\n    if (cfg.ADD_TAGS) {\n      if (ALLOWED_TAGS === DEFAULT_ALLOWED_TAGS) {\n        ALLOWED_TAGS = clone(ALLOWED_TAGS);\n      }\n\n      addToSet(ALLOWED_TAGS, cfg.ADD_TAGS, transformCaseFunc);\n    }\n\n    if (cfg.ADD_ATTR) {\n      if (ALLOWED_ATTR === DEFAULT_ALLOWED_ATTR) {\n        ALLOWED_ATTR = clone(ALLOWED_ATTR);\n      }\n\n      addToSet(ALLOWED_ATTR, cfg.ADD_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.ADD_URI_SAFE_ATTR) {\n      addToSet(URI_SAFE_ATTRIBUTES, cfg.ADD_URI_SAFE_ATTR, transformCaseFunc);\n    }\n\n    if (cfg.FORBID_CONTENTS) {\n      if (FORBID_CONTENTS === DEFAULT_FORBID_CONTENTS) {\n        FORBID_CONTENTS = clone(FORBID_CONTENTS);\n      }\n\n      addToSet(FORBID_CONTENTS, cfg.FORBID_CONTENTS, transformCaseFunc);\n    }\n\n    /* Add #text in case KEEP_CONTENT is set to true */\n    if (KEEP_CONTENT) {\n      ALLOWED_TAGS['#text'] = true;\n    }\n\n    /* Add html, head and body to ALLOWED_TAGS in case WHOLE_DOCUMENT is true */\n    if (WHOLE_DOCUMENT) {\n      addToSet(ALLOWED_TAGS, ['html', 'head', 'body']);\n    }\n\n    /* Add tbody to ALLOWED_TAGS in case tables are permitted, see #286, #365 */\n    if (ALLOWED_TAGS.table) {\n      addToSet(ALLOWED_TAGS, ['tbody']);\n      delete FORBID_TAGS.tbody;\n    }\n\n    // Prevent further manipulation of configuration.\n    // Not available in IE8, Safari 5, etc.\n    if (freeze) {\n      freeze(cfg);\n    }\n\n    CONFIG = cfg;\n  };\n\n  const MATHML_TEXT_INTEGRATION_POINTS = addToSet({}, [\n    'mi',\n    'mo',\n    'mn',\n    'ms',\n    'mtext',\n  ]);\n\n  const HTML_INTEGRATION_POINTS = addToSet({}, ['annotation-xml']);\n\n  // Certain elements are allowed in both SVG and HTML\n  // namespace. We need to specify them explicitly\n  // so that they don't get erroneously deleted from\n  // HTML namespace.\n  const COMMON_SVG_AND_HTML_ELEMENTS = addToSet({}, [\n    'title',\n    'style',\n    'font',\n    'a',\n    'script',\n  ]);\n\n  /* Keep track of all possible SVG and MathML tags\n   * so that we can perform the namespace checks\n   * correctly. */\n  const ALL_SVG_TAGS = addToSet({}, TAGS.svg);\n  addToSet(ALL_SVG_TAGS, TAGS.svgFilters);\n  addToSet(ALL_SVG_TAGS, TAGS.svgDisallowed);\n\n  const ALL_MATHML_TAGS = addToSet({}, TAGS.mathMl);\n  addToSet(ALL_MATHML_TAGS, TAGS.mathMlDisallowed);\n\n  /**\n   *\n   *\n   * @param  {Element} element a DOM element whose namespace is being checked\n   * @returns {boolean} Return false if the element has a\n   *  namespace that a spec-compliant parser would never\n   *  return. Return true otherwise.\n   */\n  const _checkValidNamespace = function (element) {\n    let parent = getParentNode(element);\n\n    // In JSDOM, if we're inside shadow DOM, then parentNode\n    // can be null. We just simulate parent in this case.\n    if (!parent || !parent.tagName) {\n      parent = {\n        namespaceURI: NAMESPACE,\n        tagName: 'template',\n      };\n    }\n\n    const tagName = stringToLowerCase(element.tagName);\n    const parentTagName = stringToLowerCase(parent.tagName);\n\n    if (!ALLOWED_NAMESPACES[element.namespaceURI]) {\n      return false;\n    }\n\n    if (element.namespaceURI === SVG_NAMESPACE) {\n      // The only way to switch from HTML namespace to SVG\n      // is via <svg>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'svg';\n      }\n\n      // The only way to switch from MathML to SVG is via`\n      // svg if parent is either <annotation-xml> or MathML\n      // text integration points.\n      if (parent.namespaceURI === MATHML_NAMESPACE) {\n        return (\n          tagName === 'svg' &&\n          (parentTagName === 'annotation-xml' ||\n            MATHML_TEXT_INTEGRATION_POINTS[parentTagName])\n        );\n      }\n\n      // We only allow elements that are defined in SVG\n      // spec. All others are disallowed in SVG namespace.\n      return Boolean(ALL_SVG_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === MATHML_NAMESPACE) {\n      // The only way to switch from HTML namespace to MathML\n      // is via <math>. If it happens via any other tag, then\n      // it should be killed.\n      if (parent.namespaceURI === HTML_NAMESPACE) {\n        return tagName === 'math';\n      }\n\n      // The only way to switch from SVG to MathML is via\n      // <math> and HTML integration points\n      if (parent.namespaceURI === SVG_NAMESPACE) {\n        return tagName === 'math' && HTML_INTEGRATION_POINTS[parentTagName];\n      }\n\n      // We only allow elements that are defined in MathML\n      // spec. All others are disallowed in MathML namespace.\n      return Boolean(ALL_MATHML_TAGS[tagName]);\n    }\n\n    if (element.namespaceURI === HTML_NAMESPACE) {\n      // The only way to switch from SVG to HTML is via\n      // HTML integration points, and from MathML to HTML\n      // is via MathML text integration points\n      if (\n        parent.namespaceURI === SVG_NAMESPACE &&\n        !HTML_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      if (\n        parent.namespaceURI === MATHML_NAMESPACE &&\n        !MATHML_TEXT_INTEGRATION_POINTS[parentTagName]\n      ) {\n        return false;\n      }\n\n      // We disallow tags that are specific for MathML\n      // or SVG and should never appear in HTML namespace\n      return (\n        !ALL_MATHML_TAGS[tagName] &&\n        (COMMON_SVG_AND_HTML_ELEMENTS[tagName] || !ALL_SVG_TAGS[tagName])\n      );\n    }\n\n    // For XHTML and XML documents that support custom namespaces\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      ALLOWED_NAMESPACES[element.namespaceURI]\n    ) {\n      return true;\n    }\n\n    // The code should never reach this place (this means\n    // that the element somehow got namespace that is not\n    // HTML, SVG, MathML or allowed via ALLOWED_NAMESPACES).\n    // Return false just in case.\n    return false;\n  };\n\n  /**\n   * _forceRemove\n   *\n   * @param  {Node} node a DOM node\n   */\n  const _forceRemove = function (node) {\n    arrayPush(DOMPurify.removed, { element: node });\n    try {\n      // eslint-disable-next-line unicorn/prefer-dom-node-remove\n      node.parentNode.removeChild(node);\n    } catch (_) {\n      try {\n        node.outerHTML = emptyHTML;\n      } catch (_) {\n        node.remove();\n      }\n    }\n  };\n\n  /**\n   * _removeAttribute\n   *\n   * @param  {String} name an Attribute name\n   * @param  {Node} node a DOM node\n   */\n  const _removeAttribute = function (name, node) {\n    try {\n      arrayPush(DOMPurify.removed, {\n        attribute: node.getAttributeNode(name),\n        from: node,\n      });\n    } catch (_) {\n      arrayPush(DOMPurify.removed, {\n        attribute: null,\n        from: node,\n      });\n    }\n\n    node.removeAttribute(name);\n\n    // We void attribute values for unremovable \"is\"\" attributes\n    if (name === 'is' && !ALLOWED_ATTR[name]) {\n      if (RETURN_DOM || RETURN_DOM_FRAGMENT) {\n        try {\n          _forceRemove(node);\n        } catch (_) {}\n      } else {\n        try {\n          node.setAttribute(name, '');\n        } catch (_) {}\n      }\n    }\n  };\n\n  /**\n   * _initDocument\n   *\n   * @param  {String} dirty a string of dirty markup\n   * @return {Document} a DOM, filled with the dirty markup\n   */\n  const _initDocument = function (dirty) {\n    /* Create a HTML document */\n    let doc;\n    let leadingWhitespace;\n\n    if (FORCE_BODY) {\n      dirty = '<remove></remove>' + dirty;\n    } else {\n      /* If FORCE_BODY isn't used, leading whitespace needs to be preserved manually */\n      const matches = stringMatch(dirty, /^[\\r\\n\\t ]+/);\n      leadingWhitespace = matches && matches[0];\n    }\n\n    if (\n      PARSER_MEDIA_TYPE === 'application/xhtml+xml' &&\n      NAMESPACE === HTML_NAMESPACE\n    ) {\n      // Root of XHTML doc must contain xmlns declaration (see https://www.w3.org/TR/xhtml1/normative.html#strict)\n      dirty =\n        '<html xmlns=\"http://www.w3.org/1999/xhtml\"><head></head><body>' +\n        dirty +\n        '</body></html>';\n    }\n\n    const dirtyPayload = trustedTypesPolicy\n      ? trustedTypesPolicy.createHTML(dirty)\n      : dirty;\n    /*\n     * Use the DOMParser API by default, fallback later if needs be\n     * DOMParser not work for svg when has multiple root element.\n     */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      try {\n        doc = new DOMParser().parseFromString(dirtyPayload, PARSER_MEDIA_TYPE);\n      } catch (_) {}\n    }\n\n    /* Use createHTMLDocument in case DOMParser is not available */\n    if (!doc || !doc.documentElement) {\n      doc = implementation.createDocument(NAMESPACE, 'template', null);\n      try {\n        doc.documentElement.innerHTML = IS_EMPTY_INPUT\n          ? emptyHTML\n          : dirtyPayload;\n      } catch (_) {\n        // Syntax error if dirtyPayload is invalid xml\n      }\n    }\n\n    const body = doc.body || doc.documentElement;\n\n    if (dirty && leadingWhitespace) {\n      body.insertBefore(\n        document.createTextNode(leadingWhitespace),\n        body.childNodes[0] || null\n      );\n    }\n\n    /* Work on whole document or just its body */\n    if (NAMESPACE === HTML_NAMESPACE) {\n      return getElementsByTagName.call(\n        doc,\n        WHOLE_DOCUMENT ? 'html' : 'body'\n      )[0];\n    }\n\n    return WHOLE_DOCUMENT ? doc.documentElement : body;\n  };\n\n  /**\n   * _createIterator\n   *\n   * @param  {Document} root document/fragment to create iterator for\n   * @return {Iterator} iterator instance\n   */\n  const _createIterator = function (root) {\n    return createNodeIterator.call(\n      root.ownerDocument || root,\n      root,\n      // eslint-disable-next-line no-bitwise\n      NodeFilter.SHOW_ELEMENT |\n        NodeFilter.SHOW_COMMENT |\n        NodeFilter.SHOW_TEXT |\n        NodeFilter.SHOW_PROCESSING_INSTRUCTION |\n        NodeFilter.SHOW_CDATA_SECTION,\n      null,\n      false\n    );\n  };\n\n  /**\n   * _isClobbered\n   *\n   * @param  {Node} elm element to check for clobbering attacks\n   * @return {Boolean} true if clobbered, false if safe\n   */\n  const _isClobbered = function (elm) {\n    return (\n      elm instanceof HTMLFormElement &&\n      (typeof elm.nodeName !== 'string' ||\n        typeof elm.textContent !== 'string' ||\n        typeof elm.removeChild !== 'function' ||\n        !(elm.attributes instanceof NamedNodeMap) ||\n        typeof elm.removeAttribute !== 'function' ||\n        typeof elm.setAttribute !== 'function' ||\n        typeof elm.namespaceURI !== 'string' ||\n        typeof elm.insertBefore !== 'function' ||\n        typeof elm.hasChildNodes !== 'function')\n    );\n  };\n\n  /**\n   * _isNode\n   *\n   * @param  {Node} obj object to check whether it's a DOM node\n   * @return {Boolean} true is object is a DOM node\n   */\n  const _isNode = function (object) {\n    return typeof Node === 'object'\n      ? object instanceof Node\n      : object &&\n          typeof object === 'object' &&\n          typeof object.nodeType === 'number' &&\n          typeof object.nodeName === 'string';\n  };\n\n  /**\n   * _executeHook\n   * Execute user configurable hooks\n   *\n   * @param  {String} entryPoint  Name of the hook's entry point\n   * @param  {Node} currentNode node to work on with the hook\n   * @param  {Object} data additional hook parameters\n   */\n  const _executeHook = function (entryPoint, currentNode, data) {\n    if (!hooks[entryPoint]) {\n      return;\n    }\n\n    arrayForEach(hooks[entryPoint], (hook) => {\n      hook.call(DOMPurify, currentNode, data, CONFIG);\n    });\n  };\n\n  /**\n   * _sanitizeElements\n   *\n   * @protect nodeName\n   * @protect textContent\n   * @protect removeChild\n   *\n   * @param   {Node} currentNode to check for permission to exist\n   * @return  {Boolean} true if node was killed, false if left alive\n   */\n  const _sanitizeElements = function (currentNode) {\n    let content;\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeElements', currentNode, null);\n\n    /* Check if element is clobbered or can clobber */\n    if (_isClobbered(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check if tagname contains Unicode */\n    if (regExpTest(/[\\u0080-\\uFFFF]/, currentNode.nodeName)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Now let's check the element's type and name */\n    const tagName = transformCaseFunc(currentNode.nodeName);\n\n    /* Execute a hook if present */\n    _executeHook('uponSanitizeElement', currentNode, {\n      tagName,\n      allowedTags: ALLOWED_TAGS,\n    });\n\n    /* Detect mXSS attempts abusing namespace confusion */\n    if (\n      currentNode.hasChildNodes() &&\n      !_isNode(currentNode.firstElementChild) &&\n      (!_isNode(currentNode.content) ||\n        !_isNode(currentNode.content.firstElementChild)) &&\n      regExpTest(/<[/\\w]/g, currentNode.innerHTML) &&\n      regExpTest(/<[/\\w]/g, currentNode.textContent)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Mitigate a problem with templates inside select */\n    if (\n      tagName === 'select' &&\n      regExpTest(/<template/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any ocurrence of processing instructions */\n    if (currentNode.nodeType === 7) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove any kind of possibly harmful comments */\n    if (\n      SAFE_FOR_XML &&\n      currentNode.nodeType === 8 &&\n      regExpTest(/<[/\\w]/g, currentNode.data)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Remove element if anything forbids its presence */\n    if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n      /* Check if we have a custom element to handle */\n      if (!FORBID_TAGS[tagName] && _basicCustomElementTest(tagName)) {\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n          regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, tagName)\n        )\n          return false;\n        if (\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n          CUSTOM_ELEMENT_HANDLING.tagNameCheck(tagName)\n        )\n          return false;\n      }\n\n      /* Keep content except for bad-listed elements */\n      if (KEEP_CONTENT && !FORBID_CONTENTS[tagName]) {\n        const parentNode = getParentNode(currentNode) || currentNode.parentNode;\n        const childNodes = getChildNodes(currentNode) || currentNode.childNodes;\n\n        if (childNodes && parentNode) {\n          const childCount = childNodes.length;\n\n          for (let i = childCount - 1; i >= 0; --i) {\n            const childClone = cloneNode(childNodes[i], true);\n            childClone.__removalCount = (currentNode.__removalCount || 0) + 1;\n            parentNode.insertBefore(childClone, getNextSibling(currentNode));\n          }\n        }\n      }\n\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Check whether element has a valid namespace */\n    if (currentNode instanceof Element && !_checkValidNamespace(currentNode)) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Make sure that older browsers don't get fallback-tag mXSS */\n    if (\n      (tagName === 'noscript' ||\n        tagName === 'noembed' ||\n        tagName === 'noframes') &&\n      regExpTest(/<\\/no(script|embed|frames)/i, currentNode.innerHTML)\n    ) {\n      _forceRemove(currentNode);\n      return true;\n    }\n\n    /* Sanitize element content to be template-safe */\n    if (SAFE_FOR_TEMPLATES && currentNode.nodeType === 3) {\n      /* Get the element's text content */\n      content = currentNode.textContent;\n      content = stringReplace(content, MUSTACHE_EXPR, ' ');\n      content = stringReplace(content, ERB_EXPR, ' ');\n      content = stringReplace(content, TMPLIT_EXPR, ' ');\n      if (currentNode.textContent !== content) {\n        arrayPush(DOMPurify.removed, { element: currentNode.cloneNode() });\n        currentNode.textContent = content;\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeElements', currentNode, null);\n\n    return false;\n  };\n\n  /**\n   * _isValidAttribute\n   *\n   * @param  {string} lcTag Lowercase tag name of containing element.\n   * @param  {string} lcName Lowercase attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid, otherwise false.\n   */\n  // eslint-disable-next-line complexity\n  const _isValidAttribute = function (lcTag, lcName, value) {\n    /* Make sure attribute cannot clobber */\n    if (\n      SANITIZE_DOM &&\n      (lcName === 'id' || lcName === 'name') &&\n      (value in document || value in formElement)\n    ) {\n      return false;\n    }\n\n    /* Allow valid data-* attributes: At least one character after \"-\"\n        (https://html.spec.whatwg.org/multipage/dom.html#embedding-custom-non-visible-data-with-the-data-*-attributes)\n        XML-compatible (https://html.spec.whatwg.org/multipage/infrastructure.html#xml-compatible and http://www.w3.org/TR/xml/#d0e804)\n        We don't need to check the value; it's always URI safe. */\n    if (\n      ALLOW_DATA_ATTR &&\n      !FORBID_ATTR[lcName] &&\n      regExpTest(DATA_ATTR, lcName)\n    ) {\n      // This attribute is safe\n    } else if (ALLOW_ARIA_ATTR && regExpTest(ARIA_ATTR, lcName)) {\n      // This attribute is safe\n      /* Otherwise, check the name is permitted */\n    } else if (!ALLOWED_ATTR[lcName] || FORBID_ATTR[lcName]) {\n      if (\n        // First condition does a very basic check if a) it's basically a valid custom element tagname AND\n        // b) if the tagName passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        // and c) if the attribute name passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.attributeNameCheck\n        (_basicCustomElementTest(lcTag) &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, lcTag)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(lcTag))) &&\n          ((CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.attributeNameCheck, lcName)) ||\n            (CUSTOM_ELEMENT_HANDLING.attributeNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.attributeNameCheck(lcName)))) ||\n        // Alternative, second condition checks if it's an `is`-attribute, AND\n        // the value passes whatever the user has configured for CUSTOM_ELEMENT_HANDLING.tagNameCheck\n        (lcName === 'is' &&\n          CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements &&\n          ((CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof RegExp &&\n            regExpTest(CUSTOM_ELEMENT_HANDLING.tagNameCheck, value)) ||\n            (CUSTOM_ELEMENT_HANDLING.tagNameCheck instanceof Function &&\n              CUSTOM_ELEMENT_HANDLING.tagNameCheck(value))))\n      ) {\n        // If user has supplied a regexp or function in CUSTOM_ELEMENT_HANDLING.tagNameCheck, we need to also allow derived custom elements using the same tagName test.\n        // Additionally, we need to allow attributes passing the CUSTOM_ELEMENT_HANDLING.attributeNameCheck user has configured, as custom elements can define these at their own discretion.\n      } else {\n        return false;\n      }\n      /* Check value is safe. First, is attr inert? If so, is safe */\n    } else if (URI_SAFE_ATTRIBUTES[lcName]) {\n      // This attribute is safe\n      /* Check no script, data or unknown possibly unsafe URI\n        unless we know URI values are safe for that attribute */\n    } else if (\n      regExpTest(IS_ALLOWED_URI, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Keep image data URIs alive if src/xlink:href is allowed */\n      /* Further prevent gadget XSS for dynamically built script tags */\n    } else if (\n      (lcName === 'src' || lcName === 'xlink:href' || lcName === 'href') &&\n      lcTag !== 'script' &&\n      stringIndexOf(value, 'data:') === 0 &&\n      DATA_URI_TAGS[lcTag]\n    ) {\n      // This attribute is safe\n      /* Allow unknown protocols: This provides support for links that\n        are handled by protocol handlers which may be unknown ahead of\n        time, e.g. fb:, spotify: */\n    } else if (\n      ALLOW_UNKNOWN_PROTOCOLS &&\n      !regExpTest(IS_SCRIPT_OR_DATA, stringReplace(value, ATTR_WHITESPACE, ''))\n    ) {\n      // This attribute is safe\n      /* Check for binary attributes */\n    } else if (value) {\n      return false;\n    } else {\n      // Binary attributes are safe at this point\n      /* Anything else, presume unsafe, do not add it back */\n    }\n\n    return true;\n  };\n\n  /**\n   * _basicCustomElementCheck\n   * checks if at least one dash is included in tagName, and it's not the first char\n   * for more sophisticated checking see https://github.com/sindresorhus/validate-element-name\n   * @param {string} tagName name of the tag of the node to sanitize\n   */\n  const _basicCustomElementTest = function (tagName) {\n    return tagName !== 'annotation-xml' && stringMatch(tagName, CUSTOM_ELEMENT);\n  };\n\n  /**\n   * _sanitizeAttributes\n   *\n   * @protect attributes\n   * @protect nodeName\n   * @protect removeAttribute\n   * @protect setAttribute\n   *\n   * @param  {Node} currentNode to sanitize\n   */\n  const _sanitizeAttributes = function (currentNode) {\n    let attr;\n    let value;\n    let lcName;\n    let l;\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeAttributes', currentNode, null);\n\n    const { attributes } = currentNode;\n\n    /* Check if we have attributes; if not we might have a text node */\n    if (!attributes || _isClobbered(currentNode)) {\n      return;\n    }\n\n    const hookEvent = {\n      attrName: '',\n      attrValue: '',\n      keepAttr: true,\n      allowedAttributes: ALLOWED_ATTR,\n    };\n    l = attributes.length;\n\n    /* Go backwards over all attributes; safely remove bad ones */\n    while (l--) {\n      attr = attributes[l];\n      const { name, namespaceURI } = attr;\n      value = name === 'value' ? attr.value : stringTrim(attr.value);\n      lcName = transformCaseFunc(name);\n\n      /* Execute a hook if present */\n      hookEvent.attrName = lcName;\n      hookEvent.attrValue = value;\n      hookEvent.keepAttr = true;\n      hookEvent.forceKeepAttr = undefined; // Allows developers to see this is a property they can set\n      _executeHook('uponSanitizeAttribute', currentNode, hookEvent);\n      value = hookEvent.attrValue;\n\n      /* Did the hooks approve of the attribute? */\n      if (hookEvent.forceKeepAttr) {\n        continue;\n      }\n\n      /* Remove attribute */\n      _removeAttribute(name, currentNode);\n\n      /* Did the hooks approve of the attribute? */\n      if (!hookEvent.keepAttr) {\n        continue;\n      }\n\n      /* Work around a security issue in jQuery 3.0 */\n      if (!ALLOW_SELF_CLOSE_IN_ATTR && regExpTest(/\\/>/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Sanitize attribute content to be template-safe */\n      if (SAFE_FOR_TEMPLATES) {\n        value = stringReplace(value, MUSTACHE_EXPR, ' ');\n        value = stringReplace(value, ERB_EXPR, ' ');\n        value = stringReplace(value, TMPLIT_EXPR, ' ');\n      }\n\n      /* Is `value` valid for this attribute? */\n      const lcTag = transformCaseFunc(currentNode.nodeName);\n      if (!_isValidAttribute(lcTag, lcName, value)) {\n        continue;\n      }\n\n      /* Full DOM Clobbering protection via namespace isolation,\n       * Prefix id and name attributes with `user-content-`\n       */\n      if (SANITIZE_NAMED_PROPS && (lcName === 'id' || lcName === 'name')) {\n        // Remove the attribute with this value\n        _removeAttribute(name, currentNode);\n\n        // Prefix the value and later re-create the attribute with the sanitized value\n        value = SANITIZE_NAMED_PROPS_PREFIX + value;\n      }\n\n      /* Work around a security issue with comments inside attributes */\n      if (SAFE_FOR_XML && regExpTest(/((--!?|])>)|<\\/(style|title)/i, value)) {\n        _removeAttribute(name, currentNode);\n        continue;\n      }\n\n      /* Handle attributes that require Trusted Types */\n      if (\n        trustedTypesPolicy &&\n        typeof trustedTypes === 'object' &&\n        typeof trustedTypes.getAttributeType === 'function'\n      ) {\n        if (namespaceURI) {\n          /* Namespaces are not yet supported, see https://bugs.chromium.org/p/chromium/issues/detail?id=1305293 */\n        } else {\n          switch (trustedTypes.getAttributeType(lcTag, lcName)) {\n            case 'TrustedHTML': {\n              value = trustedTypesPolicy.createHTML(value);\n              break;\n            }\n\n            case 'TrustedScriptURL': {\n              value = trustedTypesPolicy.createScriptURL(value);\n              break;\n            }\n\n            default: {\n              break;\n            }\n          }\n        }\n      }\n\n      /* Handle invalid data-* attribute set by try-catching it */\n      try {\n        if (namespaceURI) {\n          currentNode.setAttributeNS(namespaceURI, name, value);\n        } else {\n          /* Fallback to setAttribute() for browser-unrecognized namespaces e.g. \"x-schema\". */\n          currentNode.setAttribute(name, value);\n        }\n\n        if (_isClobbered(currentNode)) {\n          _forceRemove(currentNode);\n        } else {\n          arrayPop(DOMPurify.removed);\n        }\n      } catch (_) {}\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeAttributes', currentNode, null);\n  };\n\n  /**\n   * _sanitizeShadowDOM\n   *\n   * @param  {DocumentFragment} fragment to iterate over recursively\n   */\n  const _sanitizeShadowDOM = function (fragment) {\n    let shadowNode;\n    const shadowIterator = _createIterator(fragment);\n\n    /* Execute a hook if present */\n    _executeHook('beforeSanitizeShadowDOM', fragment, null);\n\n    while ((shadowNode = shadowIterator.nextNode())) {\n      /* Execute a hook if present */\n      _executeHook('uponSanitizeShadowNode', shadowNode, null);\n      /* Sanitize tags and elements */\n      _sanitizeElements(shadowNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(shadowNode);\n\n      /* Deep shadow DOM detected */\n      if (shadowNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(shadowNode.content);\n      }\n    }\n\n    /* Execute a hook if present */\n    _executeHook('afterSanitizeShadowDOM', fragment, null);\n  };\n\n  /**\n   * Sanitize\n   * Public method providing core sanitation functionality\n   *\n   * @param {String|Node} dirty string or DOM node\n   * @param {Object} configuration object\n   */\n  // eslint-disable-next-line complexity\n  DOMPurify.sanitize = function (dirty, cfg = {}) {\n    let body;\n    let importedNode;\n    let currentNode;\n    let oldNode;\n    let returnNode;\n    /* Make sure we have a string to sanitize.\n      DO NOT return early, as this will return the wrong type if\n      the user has requested a DOM object rather than a string */\n    IS_EMPTY_INPUT = !dirty;\n    if (IS_EMPTY_INPUT) {\n      dirty = '<!-->';\n    }\n\n    /* Stringify, in case dirty is an object */\n    if (typeof dirty !== 'string' && !_isNode(dirty)) {\n      if (typeof dirty.toString === 'function') {\n        dirty = dirty.toString();\n        if (typeof dirty !== 'string') {\n          throw typeErrorCreate('dirty is not a string, aborting');\n        }\n      } else {\n        throw typeErrorCreate('toString is not a function');\n      }\n    }\n\n    /* Check we can run. Otherwise fall back or ignore */\n    if (!DOMPurify.isSupported) {\n      if (\n        typeof window.toStaticHTML === 'object' ||\n        typeof window.toStaticHTML === 'function'\n      ) {\n        if (typeof dirty === 'string') {\n          return window.toStaticHTML(dirty);\n        }\n\n        if (_isNode(dirty)) {\n          return window.toStaticHTML(dirty.outerHTML);\n        }\n      }\n\n      return dirty;\n    }\n\n    /* Assign config vars */\n    if (!SET_CONFIG) {\n      _parseConfig(cfg);\n    }\n\n    /* Clean up removed elements */\n    DOMPurify.removed = [];\n\n    /* Check if dirty is correctly typed for IN_PLACE */\n    if (typeof dirty === 'string') {\n      IN_PLACE = false;\n    }\n\n    if (IN_PLACE) {\n      /* Do some early pre-sanitization to avoid unsafe root nodes */\n      if (dirty.nodeName) {\n        const tagName = transformCaseFunc(dirty.nodeName);\n        if (!ALLOWED_TAGS[tagName] || FORBID_TAGS[tagName]) {\n          throw typeErrorCreate(\n            'root node is forbidden and cannot be sanitized in-place'\n          );\n        }\n      }\n    } else if (dirty instanceof Node) {\n      /* If dirty is a DOM element, append to an empty document to avoid\n         elements being stripped by the parser */\n      body = _initDocument('<!---->');\n      importedNode = body.ownerDocument.importNode(dirty, true);\n      if (importedNode.nodeType === 1 && importedNode.nodeName === 'BODY') {\n        /* Node is already a body, use as is */\n        body = importedNode;\n      } else if (importedNode.nodeName === 'HTML') {\n        body = importedNode;\n      } else {\n        // eslint-disable-next-line unicorn/prefer-dom-node-append\n        body.appendChild(importedNode);\n      }\n    } else {\n      /* Exit directly if we have nothing to do */\n      if (\n        !RETURN_DOM &&\n        !SAFE_FOR_TEMPLATES &&\n        !WHOLE_DOCUMENT &&\n        // eslint-disable-next-line unicorn/prefer-includes\n        dirty.indexOf('<') === -1\n      ) {\n        return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n          ? trustedTypesPolicy.createHTML(dirty)\n          : dirty;\n      }\n\n      /* Initialize the document to work on */\n      body = _initDocument(dirty);\n\n      /* Check we have a DOM node from the data */\n      if (!body) {\n        return RETURN_DOM ? null : RETURN_TRUSTED_TYPE ? emptyHTML : '';\n      }\n    }\n\n    /* Remove first element node (ours) if FORCE_BODY is set */\n    if (body && FORCE_BODY) {\n      _forceRemove(body.firstChild);\n    }\n\n    /* Get node iterator */\n    const nodeIterator = _createIterator(IN_PLACE ? dirty : body);\n\n    /* Now start iterating over the created document */\n    while ((currentNode = nodeIterator.nextNode())) {\n      /* Fix IE's strange behavior with manipulated textNodes #89 */\n      if (currentNode.nodeType === 3 && currentNode === oldNode) {\n        continue;\n      }\n\n      /* Sanitize tags and elements */\n      _sanitizeElements(currentNode);\n\n      /* Check attributes next */\n      _sanitizeAttributes(currentNode);\n\n      /* Shadow DOM detected, sanitize it */\n      if (currentNode.content instanceof DocumentFragment) {\n        _sanitizeShadowDOM(currentNode.content);\n      }\n\n      oldNode = currentNode;\n    }\n\n    oldNode = null;\n\n    /* If we sanitized `dirty` in-place, return it. */\n    if (IN_PLACE) {\n      return dirty;\n    }\n\n    /* Return sanitized string or DOM */\n    if (RETURN_DOM) {\n      if (RETURN_DOM_FRAGMENT) {\n        returnNode = createDocumentFragment.call(body.ownerDocument);\n\n        while (body.firstChild) {\n          // eslint-disable-next-line unicorn/prefer-dom-node-append\n          returnNode.appendChild(body.firstChild);\n        }\n      } else {\n        returnNode = body;\n      }\n\n      if (ALLOWED_ATTR.shadowroot || ALLOWED_ATTR.shadowrootmod) {\n        /*\n          AdoptNode() is not used because internal state is not reset\n          (e.g. the past names map of a HTMLFormElement), this is safe\n          in theory but we would rather not risk another attack vector.\n          The state that is cloned by importNode() is explicitly defined\n          by the specs.\n        */\n        returnNode = importNode.call(originalDocument, returnNode, true);\n      }\n\n      return returnNode;\n    }\n\n    let serializedHTML = WHOLE_DOCUMENT ? body.outerHTML : body.innerHTML;\n\n    /* Serialize doctype if allowed */\n    if (\n      WHOLE_DOCUMENT &&\n      ALLOWED_TAGS['!doctype'] &&\n      body.ownerDocument &&\n      body.ownerDocument.doctype &&\n      body.ownerDocument.doctype.name &&\n      regExpTest(EXPRESSIONS.DOCTYPE_NAME, body.ownerDocument.doctype.name)\n    ) {\n      serializedHTML =\n        '<!DOCTYPE ' + body.ownerDocument.doctype.name + '>\\n' + serializedHTML;\n    }\n\n    /* Sanitize final string template-safe */\n    if (SAFE_FOR_TEMPLATES) {\n      serializedHTML = stringReplace(serializedHTML, MUSTACHE_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, ERB_EXPR, ' ');\n      serializedHTML = stringReplace(serializedHTML, TMPLIT_EXPR, ' ');\n    }\n\n    return trustedTypesPolicy && RETURN_TRUSTED_TYPE\n      ? trustedTypesPolicy.createHTML(serializedHTML)\n      : serializedHTML;\n  };\n\n  /**\n   * Public method to set the configuration once\n   * setConfig\n   *\n   * @param {Object} cfg configuration object\n   */\n  DOMPurify.setConfig = function (cfg) {\n    _parseConfig(cfg);\n    SET_CONFIG = true;\n  };\n\n  /**\n   * Public method to remove the configuration\n   * clearConfig\n   *\n   */\n  DOMPurify.clearConfig = function () {\n    CONFIG = null;\n    SET_CONFIG = false;\n  };\n\n  /**\n   * Public method to check if an attribute value is valid.\n   * Uses last set config, if any. Otherwise, uses config defaults.\n   * isValidAttribute\n   *\n   * @param  {string} tag Tag name of containing element.\n   * @param  {string} attr Attribute name.\n   * @param  {string} value Attribute value.\n   * @return {Boolean} Returns true if `value` is valid. Otherwise, returns false.\n   */\n  DOMPurify.isValidAttribute = function (tag, attr, value) {\n    /* Initialize shared config vars if necessary. */\n    if (!CONFIG) {\n      _parseConfig({});\n    }\n\n    const lcTag = transformCaseFunc(tag);\n    const lcName = transformCaseFunc(attr);\n    return _isValidAttribute(lcTag, lcName, value);\n  };\n\n  /**\n   * AddHook\n   * Public method to add DOMPurify hooks\n   *\n   * @param {String} entryPoint entry point for the hook to add\n   * @param {Function} hookFunction function to execute\n   */\n  DOMPurify.addHook = function (entryPoint, hookFunction) {\n    if (typeof hookFunction !== 'function') {\n      return;\n    }\n\n    hooks[entryPoint] = hooks[entryPoint] || [];\n    arrayPush(hooks[entryPoint], hookFunction);\n  };\n\n  /**\n   * RemoveHook\n   * Public method to remove a DOMPurify hook at a given entryPoint\n   * (pops it from the stack of hooks if more are present)\n   *\n   * @param {String} entryPoint entry point for the hook to remove\n   * @return {Function} removed(popped) hook\n   */\n  DOMPurify.removeHook = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      return arrayPop(hooks[entryPoint]);\n    }\n  };\n\n  /**\n   * RemoveHooks\n   * Public method to remove all DOMPurify hooks at a given entryPoint\n   *\n   * @param  {String} entryPoint entry point for the hooks to remove\n   */\n  DOMPurify.removeHooks = function (entryPoint) {\n    if (hooks[entryPoint]) {\n      hooks[entryPoint] = [];\n    }\n  };\n\n  /**\n   * RemoveAllHooks\n   * Public method to remove all DOMPurify hooks\n   *\n   */\n  DOMPurify.removeAllHooks = function () {\n    hooks = {};\n  };\n\n  return DOMPurify;\n}\n\nexport default createDOMPurify();\n"], "names": ["hasOwnProperty", "Object", "setPrototypeOf", "isFrozen", "getPrototypeOf", "getOwnPropertyDescriptor", "freeze", "seal", "create", "_ref", "Reflect", "apply", "construct", "fun", "thisValue", "args", "x", "Func", "_construct", "_toConsumableArray", "arrayForEach", "unapply", "Array", "prototype", "for<PERSON>ach", "arrayPop", "pop", "arrayPush", "push", "stringToLowerCase", "String", "toLowerCase", "stringToString", "toString", "stringMatch", "match", "stringReplace", "replace", "stringIndexOf", "indexOf", "stringTrim", "trim", "regExpTest", "RegExp", "test", "typeErrorCreate", "unconstruct", "TypeError", "func", "thisArg", "_len", "arguments", "length", "_key", "_len2", "_key2", "addToSet", "set", "array", "transformCaseFunc", "_transformCaseFunc", "l", "element", "lcElement", "clone", "object", "newObject", "property", "lookupGetter", "prop", "desc", "get", "value", "fallback<PERSON><PERSON><PERSON>", "console", "warn", "html", "svg", "svgFilters", "svgDisallowed", "mathMl", "mathMlDisallowed", "text", "xml", "MUSTACHE_EXPR", "ERB_EXPR", "TMPLIT_EXPR", "DATA_ATTR", "ARIA_ATTR", "IS_ALLOWED_URI", "IS_SCRIPT_OR_DATA", "ATTR_WHITESPACE", "DOCTYPE_NAME", "CUSTOM_ELEMENT", "getGlobal", "window", "_createTrustedTypesPolicy", "trustedTypes", "document", "_typeof", "createPolicy", "suffix", "ATTR_NAME", "currentScript", "hasAttribute", "getAttribute", "policyName", "createHTML", "createScriptURL", "scriptUrl", "_", "createDOMPurify", "undefined", "DOMPurify", "root", "version", "VERSION", "removed", "nodeType", "isSupported", "originalDocument", "DocumentFragment", "HTMLTemplateElement", "Node", "Element", "Node<PERSON><PERSON><PERSON>", "_window$NamedNodeMap", "NamedNodeMap", "MozNamedAttrMap", "HTMLFormElement", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElementPrototype", "cloneNode", "getNextSibling", "getChildNodes", "getParentNode", "template", "createElement", "content", "ownerDocument", "trustedTypesPolicy", "emptyHTML", "_document", "implementation", "createNodeIterator", "createDocumentFragment", "getElementsByTagName", "importNode", "documentMode", "hooks", "createHTMLDocument", "EXPRESSIONS", "ALLOWED_TAGS", "DEFAULT_ALLOWED_TAGS", "concat", "TAGS", "ALLOWED_ATTR", "DEFAULT_ALLOWED_ATTR", "ATTRS", "CUSTOM_ELEMENT_HANDLING", "tagNameCheck", "writable", "configurable", "enumerable", "attributeNameCheck", "allowCustomizedBuiltInElements", "FORBID_TAGS", "FORBID_ATTR", "ALLOW_ARIA_ATTR", "ALLOW_DATA_ATTR", "ALLOW_UNKNOWN_PROTOCOLS", "ALLOW_SELF_CLOSE_IN_ATTR", "SAFE_FOR_TEMPLATES", "SAFE_FOR_XML", "WHOLE_DOCUMENT", "SET_CONFIG", "FORCE_BODY", "RETURN_DOM", "RETURN_DOM_FRAGMENT", "RETURN_TRUSTED_TYPE", "SANITIZE_DOM", "SANITIZE_NAMED_PROPS", "SANITIZE_NAMED_PROPS_PREFIX", "KEEP_CONTENT", "IN_PLACE", "USE_PROFILES", "FORBID_CONTENTS", "DEFAULT_FORBID_CONTENTS", "DATA_URI_TAGS", "DEFAULT_DATA_URI_TAGS", "URI_SAFE_ATTRIBUTES", "DEFAULT_URI_SAFE_ATTRIBUTES", "MATHML_NAMESPACE", "SVG_NAMESPACE", "HTML_NAMESPACE", "NAMESPACE", "IS_EMPTY_INPUT", "ALLOWED_NAMESPACES", "DEFAULT_ALLOWED_NAMESPACES", "PARSER_MEDIA_TYPE", "SUPPORTED_PARSER_MEDIA_TYPES", "DEFAULT_PARSER_MEDIA_TYPE", "CONFIG", "formElement", "isRegexOrFunction", "testValue", "Function", "_parseConfig", "cfg", "ADD_URI_SAFE_ATTR", "ADD_DATA_URI_TAGS", "ALLOWED_URI_REGEXP", "ADD_TAGS", "ADD_ATTR", "table", "tbody", "MATHML_TEXT_INTEGRATION_POINTS", "HTML_INTEGRATION_POINTS", "COMMON_SVG_AND_HTML_ELEMENTS", "ALL_SVG_TAGS", "ALL_MATHML_TAGS", "_checkValidNamespace", "parent", "tagName", "namespaceURI", "parentTagName", "Boolean", "_forceRemove", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "outerHTML", "remove", "_removeAttribute", "name", "attribute", "getAttributeNode", "from", "removeAttribute", "setAttribute", "_initDocument", "dirty", "doc", "leadingWhitespace", "matches", "dirtyPayload", "parseFromString", "documentElement", "createDocument", "innerHTML", "body", "insertBefore", "createTextNode", "childNodes", "call", "_createIterator", "SHOW_ELEMENT", "SHOW_COMMENT", "SHOW_TEXT", "SHOW_PROCESSING_INSTRUCTION", "SHOW_CDATA_SECTION", "_isClobbered", "elm", "nodeName", "textContent", "attributes", "hasChildNodes", "_isNode", "_executeHook", "entryPoint", "currentNode", "data", "hook", "_sanitizeElements", "allowedTags", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "_basicCustomElementTest", "childCount", "i", "child<PERSON>lone", "__removalCount", "_isValidAttribute", "lcTag", "lcName", "_sanitizeAttributes", "attr", "hookEvent", "attrName", "attrValue", "keepAttr", "allowedAttributes", "_attr", "forceKeepAttr", "getAttributeType", "setAttributeNS", "_sanitizeShadowDOM", "fragment", "shadowNode", "shadowIterator", "nextNode", "sanitize", "importedNode", "oldNode", "returnNode", "toStaticHTML", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "nodeIterator", "shadowroot", "shadowrootmod", "serializedHTML", "doctype", "setConfig", "clearConfig", "isValidAttribute", "tag", "addHook", "hookFunction", "removeH<PERSON>", "removeHooks", "removeAllHooks"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAAA,IACEA,cAAc,GAKZC,MAAM,CALRD,cAAc,EACdE,cAAc,GAIZD,MAAM,CAJRC,cAAc,EACdC,QAAQ,GAGNF,MAAM,CAHRE,QAAQ,EACRC,cAAc,GAEZH,MAAM,CAFRG,cAAc,EACdC,wBAAwB,GACtBJ,MAAM,CADRI,wBAAwB,CAAA;IAG1B,IAAMC,MAAM,GAAmBL,MAAM,CAA/BK,MAAM,EAAEC,IAAI,GAAaN,MAAM,CAAvBM,IAAI,EAAEC,MAAM,GAAKP,MAAM,CAAjBO,MAAM,CAAY,CAAA,gDAAA;IACtC,IAAAC,IAAA,GAA2B,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,EAA9DC,KAAK,GAAAF,IAAA,CAALE,KAAK,EAAEC,SAAS,GAAAH,IAAA,CAATG,SAAS,CAAA;IAEtB,IAAI,CAACD,KAAK,EAAE;QACVA,KAAK,GAAG,SAAAA,KAAUE,CAAAA,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;YACtC,OAAOF,GAAG,CAACF,KAAK,CAACG,SAAS,EAAEC,IAAI,CAAC,CAAA;SAClC,CAAA;IACH,CAAA;IAEA,IAAI,CAACT,MAAM,EAAE;QACXA,MAAM,GAAG,SAAAA,MAAUU,CAAAA,CAAC,EAAE;YACpB,OAAOA,CAAC,CAAA;SACT,CAAA;IACH,CAAA;IAEA,IAAI,CAACT,IAAI,EAAE;QACTA,IAAI,GAAG,SAAAA,IAAUS,CAAAA,CAAC,EAAE;YAClB,OAAOA,CAAC,CAAA;SACT,CAAA;IACH,CAAA;IAEA,IAAI,CAACJ,SAAS,EAAE;QACdA,SAAS,GAAG,SAAAA,SAAAA,CAAUK,IAAI,EAAEF,IAAI,EAAE;YAChC,OAAAG,UAAA,CAAWD,IAAI,EAAAE,kBAAA,CAAIJ,IAAI,CAAA,CAAA,CAAA;SACxB,CAAA;IACH,CAAA;IAEA,IAAMK,YAAY,GAAGC,OAAO,CAACC,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC,CAAA;IAErD,IAAMC,QAAQ,GAAGJ,OAAO,CAACC,KAAK,CAACC,SAAS,CAACG,GAAG,CAAC,CAAA;IAC7C,IAAMC,SAAS,GAAGN,OAAO,CAACC,KAAK,CAACC,SAAS,CAACK,IAAI,CAAC,CAAA;IAG/C,IAAMC,iBAAiB,GAAGR,OAAO,CAACS,MAAM,CAACP,SAAS,CAACQ,WAAW,CAAC,CAAA;IAC/D,IAAMC,cAAc,GAAGX,OAAO,CAACS,MAAM,CAACP,SAAS,CAACU,QAAQ,CAAC,CAAA;IACzD,IAAMC,WAAW,GAAGb,OAAO,CAACS,MAAM,CAACP,SAAS,CAACY,KAAK,CAAC,CAAA;IACnD,IAAMC,aAAa,GAAGf,OAAO,CAACS,MAAM,CAACP,SAAS,CAACc,OAAO,CAAC,CAAA;IACvD,IAAMC,aAAa,GAAGjB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACgB,OAAO,CAAC,CAAA;IACvD,IAAMC,UAAU,GAAGnB,OAAO,CAACS,MAAM,CAACP,SAAS,CAACkB,IAAI,CAAC,CAAA;IAEjD,IAAMC,UAAU,GAAGrB,OAAO,CAACsB,MAAM,CAACpB,SAAS,CAACqB,IAAI,CAAC,CAAA;IAEjD,IAAMC,eAAe,GAAGC,WAAW,CAACC,SAAS,CAAC,CAAA;IAEvC,SAAS1B,OAAOA,CAAC2B,IAAI,EAAE;QAC5B,OAAO,SAACC,OAAO,EAAA;YAAA,IAAAC,IAAAA,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAKrC,IAAI,GAAA,IAAAO,KAAA,CAAA4B,IAAA,GAAAA,CAAAA,GAAAA,IAAA,GAAA,IAAA,IAAAG,IAAA,GAAA,CAAA,EAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA,EAAA,CAAA;gBAAJtC,IAAI,CAAAsC,IAAA,GAAAF,CAAAA,CAAAA,GAAAA,SAAA,CAAAE,IAAA,CAAA,CAAA;YAAA,CAAA;YAAA,OAAK1C,KAAK,CAACqC,IAAI,EAAEC,OAAO,EAAElC,IAAI,CAAC,CAAA;QAAA,CAAA,CAAA;IACzD,CAAA;IAEO,SAAS+B,WAAWA,CAACE,IAAI,EAAE;QAChC,OAAO,YAAA;YAAA,IAAA,IAAAM,KAAA,GAAAH,SAAA,CAAAC,MAAA,EAAIrC,IAAI,GAAAO,IAAAA,KAAA,CAAAgC,KAAA,GAAAC,KAAA,GAAA,CAAA,EAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA,EAAA,CAAA;gBAAJxC,IAAI,CAAAwC,KAAA,CAAAJ,GAAAA,SAAA,CAAAI,KAAA,CAAA,CAAA;YAAA,CAAA;YAAA,OAAK3C,SAAS,CAACoC,IAAI,EAAEjC,IAAI,CAAC,CAAA;QAAA,CAAA,CAAA;IAC3C,CAAA;IAEA,oCAAA,GACO,SAASyC,QAAQA,CAACC,GAAG,EAAEC,KAAK,EAAEC,iBAAiB,EAAE;QAAA,IAAAC,kBAAA,CAAA;QACtDD,iBAAiB,GAAA,CAAAC,kBAAA,GAAGD,iBAAiB,MAAA,QAAAC,kBAAA,KAAA,KAAA,CAAA,GAAAA,kBAAA,GAAI/B,iBAAiB,CAAA;QAC1D,IAAI3B,cAAc,EAAE;YAClB,4DAAA;YACA,6DAAA;YACA,mEAAA;YACAA,cAAc,CAACuD,GAAG,EAAE,IAAI,CAAC,CAAA;QAC3B,CAAA;QAEA,IAAII,CAAC,GAAGH,KAAK,CAACN,MAAM,CAAA;QACpB,MAAOS,CAAC,EAAE,CAAE;YACV,IAAIC,OAAO,GAAGJ,KAAK,CAACG,CAAC,CAAC,CAAA;YACtB,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;gBAC/B,IAAMC,SAAS,GAAGJ,iBAAiB,CAACG,OAAO,CAAC,CAAA;gBAC5C,IAAIC,SAAS,KAAKD,OAAO,EAAE;oBACzB,yDAAA;oBACA,IAAI,CAAC3D,QAAQ,CAACuD,KAAK,CAAC,EAAE;wBACpBA,KAAK,CAACG,CAAC,CAAC,GAAGE,SAAS,CAAA;oBACtB,CAAA;oBAEAD,OAAO,GAAGC,SAAS,CAAA;gBACrB,CAAA;YACF,CAAA;YAEAN,GAAG,CAACK,OAAO,CAAC,GAAG,IAAI,CAAA;QACrB,CAAA;QAEA,OAAOL,GAAG,CAAA;IACZ,CAAA;IAEA,2BAAA,GACO,SAASO,KAAKA,CAACC,MAAM,EAAE;QAC5B,IAAMC,SAAS,GAAG1D,MAAM,CAAC,IAAI,CAAC,CAAA;QAE9B,IAAI2D,QAAQ,CAAA;QACZ,IAAKA,QAAQ,IAAIF,MAAM,CAAE;YACvB,IAAItD,KAAK,CAACX,cAAc,EAAEiE,MAAM,EAAE;gBAACE,QAAQ;aAAC,CAAC,KAAK,IAAI,EAAE;gBACtDD,SAAS,CAACC,QAAQ,CAAC,GAAGF,MAAM,CAACE,QAAQ,CAAC,CAAA;YACxC,CAAA;QACF,CAAA;QAEA,OAAOD,SAAS,CAAA;IAClB,CAAA;IAEA;;;kBAGA,GACA,SAASE,YAAYA,CAACH,MAAM,EAAEI,IAAI,EAAE;QAClC,MAAOJ,MAAM,KAAK,IAAI,CAAE;YACtB,IAAMK,IAAI,GAAGjE,wBAAwB,CAAC4D,MAAM,EAAEI,IAAI,CAAC,CAAA;YACnD,IAAIC,IAAI,EAAE;gBACR,IAAIA,IAAI,CAACC,GAAG,EAAE;oBACZ,OAAOlD,OAAO,CAACiD,IAAI,CAACC,GAAG,CAAC,CAAA;gBAC1B,CAAA;gBAEA,IAAI,OAAOD,IAAI,CAACE,KAAK,KAAK,UAAU,EAAE;oBACpC,OAAOnD,OAAO,CAACiD,IAAI,CAACE,KAAK,CAAC,CAAA;gBAC5B,CAAA;YACF,CAAA;YAEAP,MAAM,GAAG7D,cAAc,CAAC6D,MAAM,CAAC,CAAA;QACjC,CAAA;QAEA,SAASQ,aAAaA,CAACX,OAAO,EAAE;YAC9BY,OAAO,CAACC,IAAI,CAAC,oBAAoB,EAAEb,OAAO,CAAC,CAAA;YAC3C,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,OAAOW,aAAa,CAAA;IACtB;ICjIO,IAAMG,MAAI,GAAGtE,MAAM,CAAC;QACzB,GAAG;QACH,MAAM;QACN,SAAS;QACT,SAAS;QACT,MAAM;QACN,SAAS;QACT,OAAO;QACP,OAAO;QACP,GAAG;QACH,KAAK;QACL,KAAK;QACL,KAAK;QACL,OAAO;QACP,YAAY;QACZ,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,MAAM;QACN,MAAM;QACN,KAAK;QACL,UAAU;QACV,SAAS;QACT,MAAM;QACN,UAAU;QACV,IAAI;QACJ,WAAW;QACX,KAAK;QACL,SAAS;QACT,KAAK;QACL,QAAQ;QACR,KAAK;QACL,KAAK;QACL,IAAI;QACJ,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,UAAU;QACV,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,IAAI;QACJ,MAAM;QACN,GAAG;QACH,KAAK;QACL,OAAO;QACP,KAAK;QACL,KAAK;QACL,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,MAAM;QACN,KAAK;QACL,MAAM;QACN,SAAS;QACT,MAAM;QACN,UAAU;QACV,OAAO;QACP,KAAK;QACL,MAAM;QACN,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,QAAQ;QACR,GAAG;QACH,SAAS;QACT,KAAK;QACL,UAAU;QACV,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,GAAG;QACH,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,SAAS;QACT,KAAK;QACL,OAAO;QACP,OAAO;QACP,IAAI;QACJ,UAAU;QACV,UAAU;QACV,OAAO;QACP,IAAI;QACJ,OAAO;QACP,MAAM;QACN,IAAI;QACJ,OAAO;QACP,IAAI;QACJ,GAAG;QACH,IAAI;QACJ,KAAK;QACL,OAAO;QACP,KAAK;KACN,CAAC,CAAA;IAEF,MAAA;IACO,IAAMuE,KAAG,GAAGvE,MAAM,CAAC;QACxB,KAAK;QACL,GAAG;QACH,UAAU;QACV,aAAa;QACb,cAAc;QACd,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,UAAU;QACV,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,MAAM;QACN,GAAG;QACH,OAAO;QACP,UAAU;QACV,OAAO;QACP,OAAO;QACP,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;QACV,gBAAgB;QAChB,MAAM;QACN,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,UAAU;QACV,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,OAAO;KACR,CAAC,CAAA;IAEK,IAAMwE,UAAU,GAAGxE,MAAM,CAAC;QAC/B,SAAS;QACT,eAAe;QACf,qBAAqB;QACrB,aAAa;QACb,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,SAAS;QACT,gBAAgB;QAChB,SAAS;QACT,SAAS;QACT,aAAa;QACb,cAAc;QACd,UAAU;QACV,cAAc;QACd,oBAAoB;QACpB,aAAa;QACb,QAAQ;QACR,cAAc;KACf,CAAC,CAAA;IAEF,uDAAA;IACA,yDAAA;IACA,mDAAA;IACA,cAAA;IACO,IAAMyE,aAAa,GAAGzE,MAAM,CAAC;QAClC,SAAS;QACT,eAAe;QACf,QAAQ;QACR,SAAS;QACT,cAAc;QACd,WAAW;QACX,kBAAkB;QAClB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;QACf,OAAO;QACP,WAAW;QACX,MAAM;QACN,cAAc;QACd,WAAW;QACX,SAAS;QACT,eAAe;QACf,QAAQ;QACR,KAAK;QACL,YAAY;QACZ,SAAS;QACT,KAAK;KACN,CAAC,CAAA;IAEK,IAAM0E,QAAM,GAAG1E,MAAM,CAAC;QAC3B,MAAM;QACN,UAAU;QACV,QAAQ;QACR,SAAS;QACT,OAAO;QACP,QAAQ;QACR,IAAI;QACJ,YAAY;QACZ,eAAe;QACf,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;QACT,UAAU;QACV,OAAO;QACP,MAAM;QACN,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;QACR,KAAK;QACL,OAAO;QACP,KAAK;QACL,QAAQ;QACR,YAAY;KACb,CAAC,CAAA;IAEF,yDAAA;IACA,0CAAA;IACO,IAAM2E,gBAAgB,GAAG3E,MAAM,CAAC;QACrC,SAAS;QACT,aAAa;QACb,YAAY;QACZ,UAAU;QACV,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,WAAW;QACX,YAAY;QACZ,gBAAgB;QAChB,aAAa;QACb,MAAM;KACP,CAAC,CAAA;IAEK,IAAM4E,IAAI,GAAG5E,MAAM,CAAC;QAAC,OAAO;KAAC,CAAC;ICpR9B,IAAMsE,IAAI,GAAGtE,MAAM,CAAC;QACzB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;QAChB,cAAc;QACd,sBAAsB;QACtB,UAAU;QACV,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,SAAS;QACT,aAAa;QACb,aAAa;QACb,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,UAAU;QACV,cAAc;QACd,QAAQ;QACR,aAAa;QACb,UAAU;QACV,UAAU;QACV,SAAS;QACT,KAAK;QACL,UAAU;QACV,yBAAyB;QACzB,uBAAuB;QACvB,UAAU;QACV,WAAW;QACX,SAAS;QACT,cAAc;QACd,MAAM;QACN,KAAK;QACL,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,MAAM;QACN,UAAU;QACV,IAAI;QACJ,WAAW;QACX,WAAW;QACX,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,MAAM;QACN,SAAS;QACT,MAAM;QACN,KAAK;QACL,KAAK;QACL,WAAW;QACX,OAAO;QACP,QAAQ;QACR,KAAK;QACL,WAAW;QACX,UAAU;QACV,OAAO;QACP,MAAM;QACN,OAAO;QACP,SAAS;QACT,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,SAAS;QACT,SAAS;QACT,aAAa;QACb,aAAa;QACb,QAAQ;QACR,SAAS;QACT,SAAS;QACT,YAAY;QACZ,UAAU;QACV,KAAK;QACL,UAAU;QACV,KAAK;QACL,UAAU;QACV,MAAM;QACN,MAAM;QACN,SAAS;QACT,YAAY;QACZ,OAAO;QACP,UAAU;QACV,OAAO;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;QACP,KAAK;QACL,QAAQ;QACR,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,OAAO;QACP,WAAW;QACX,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,OAAO;QACP,OAAO;QACP,MAAM;KACP,CAAC,CAAA;IAEK,IAAMuE,GAAG,GAAGvE,MAAM,CAAC;QACxB,eAAe;QACf,YAAY;QACZ,UAAU;QACV,oBAAoB;QACpB,QAAQ;QACR,eAAe;QACf,eAAe;QACf,SAAS;QACT,eAAe;QACf,gBAAgB;QAChB,OAAO;QACP,MAAM;QACN,IAAI;QACJ,OAAO;QACP,MAAM;QACN,eAAe;QACf,WAAW;QACX,WAAW;QACX,OAAO;QACP,qBAAqB;QACrB,6BAA6B;QAC7B,eAAe;QACf,iBAAiB;QACjB,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,iBAAiB;QACjB,WAAW;QACX,SAAS;QACT,SAAS;QACT,KAAK;QACL,UAAU;QACV,WAAW;QACX,KAAK;QACL,MAAM;QACN,cAAc;QACd,WAAW;QACX,QAAQ;QACR,aAAa;QACb,aAAa;QACb,eAAe;QACf,aAAa;QACb,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,YAAY;QACZ,cAAc;QACd,aAAa;QACb,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,YAAY;QACZ,UAAU;QACV,eAAe;QACf,mBAAmB;QACnB,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,iBAAiB;QACjB,IAAI;QACJ,KAAK;QACL,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,SAAS;QACT,WAAW;QACX,YAAY;QACZ,UAAU;QACV,MAAM;QACN,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,kBAAkB;QAClB,gBAAgB;QAChB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,cAAc;QACd,aAAa;QACb,aAAa;QACb,kBAAkB;QAClB,WAAW;QACX,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;QACR,MAAM;QACN,KAAK;QACL,MAAM;QACN,YAAY;QACZ,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,MAAM;QACN,YAAY;QACZ,qBAAqB;QACrB,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,qBAAqB;QACrB,gBAAgB;QAChB,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,QAAQ;QACR,MAAM;QACN,MAAM;QACN,aAAa;QACb,WAAW;QACX,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,MAAM;QACN,iBAAiB;QACjB,kBAAkB;QAClB,kBAAkB;QAClB,cAAc;QACd,aAAa;QACb,cAAc;QACd,aAAa;QACb,YAAY;QACZ,cAAc;QACd,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;QAChB,iBAAiB;QACjB,mBAAmB;QACnB,gBAAgB;QAChB,QAAQ;QACR,cAAc;QACd,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,UAAU;QACV,SAAS;QACT,SAAS;QACT,WAAW;QACX,kBAAkB;QAClB,aAAa;QACb,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,SAAS;QACT,YAAY;QACZ,SAAS;QACT,YAAY;QACZ,eAAe;QACf,eAAe;QACf,OAAO;QACP,cAAc;QACd,MAAM;QACN,cAAc;QACd,kBAAkB;QAClB,kBAAkB;QAClB,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,GAAG;QACH,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,YAAY;KACb,CAAC,CAAA;IAEK,IAAM0E,MAAM,GAAG1E,MAAM,CAAC;QAC3B,QAAQ;QACR,aAAa;QACb,OAAO;QACP,UAAU;QACV,OAAO;QACP,cAAc;QACd,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,OAAO;QACP,KAAK;QACL,SAAS;QACT,cAAc;QACd,UAAU;QACV,OAAO;QACP,OAAO;QACP,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,SAAS;QACT,QAAQ;QACR,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,gBAAgB;QAChB,WAAW;QACX,UAAU;QACV,aAAa;QACb,SAAS;QACT,SAAS;QACT,eAAe;QACf,UAAU;QACV,UAAU;QACV,MAAM;QACN,UAAU;QACV,UAAU;QACV,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,aAAa;QACb,eAAe;QACf,sBAAsB;QACtB,WAAW;QACX,WAAW;QACX,YAAY;QACZ,UAAU;QACV,gBAAgB;QAChB,gBAAgB;QAChB,WAAW;QACX,SAAS;QACT,OAAO;QACP,OAAO;KACR,CAAC,CAAA;IAEK,IAAM6E,GAAG,GAAG7E,MAAM,CAAC;QACxB,YAAY;QACZ,QAAQ;QACR,aAAa;QACb,WAAW;QACX,aAAa;KACd,CAAC;ICvWF,gDAAA;IACO,IAAM8E,aAAa,GAAG7E,IAAI,CAAC,2BAA2B,CAAC,CAAC,CAAA,+DAAA;IACxD,IAAM8E,QAAQ,GAAG9E,IAAI,CAAC,uBAAuB,CAAC,CAAA;IAC9C,IAAM+E,WAAW,GAAG/E,IAAI,CAAC,eAAe,CAAC,CAAA;IACzC,IAAMgF,SAAS,GAAGhF,IAAI,CAAC,8BAA8B,CAAC,CAAC,CAAA,wCAAA;IACvD,IAAMiF,SAAS,GAAGjF,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAA,wCAAA;IACzC,IAAMkF,cAAc,GAAGlF,IAAI,CAChC,uFAAuF,CAAA,wCAAA;;IAElF,IAAMmF,iBAAiB,GAAGnF,IAAI,CAAC,uBAAuB,CAAC,CAAA;IACvD,IAAMoF,eAAe,GAAGpF,IAAI,CACjC,6DAA6D,CAAA,uCAAA;;IAExD,IAAMqF,YAAY,GAAGrF,IAAI,CAAC,SAAS,CAAC,CAAA;IACpC,IAAMsF,cAAc,GAAGtF,IAAI,CAAC,0BAA0B,CAAC;ICK9D,IAAMuF,SAAS,GAAG,SAAZA,SAASA,GAAA;QAAA,OAAU,OAAOC,MAAM,KAAK,WAAW,GAAG,IAAI,GAAGA,MAAM,CAAA;IAAA,CAAC,CAAA;IAEvE;;;;;;;GAOA,GACA,IAAMC,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAaC,YAAY,EAAEC,QAAQ,EAAE;QAClE,IACEC,OAAA,CAAOF,YAAY,CAAA,KAAK,QAAQ,IAChC,OAAOA,YAAY,CAACG,YAAY,KAAK,UAAU,EAC/C;YACA,OAAO,IAAI,CAAA;QACb,CAAA;QAEA,sDAAA;QACA,8EAAA;QACA,gEAAA;QACA,IAAIC,MAAM,GAAG,IAAI,CAAA;QACjB,IAAMC,SAAS,GAAG,uBAAuB,CAAA;QACzC,IACEJ,QAAQ,CAACK,aAAa,IACtBL,QAAQ,CAACK,aAAa,CAACC,YAAY,CAACF,SAAS,CAAC,EAC9C;YACAD,MAAM,GAAGH,QAAQ,CAACK,aAAa,CAACE,YAAY,CAACH,SAAS,CAAC,CAAA;QACzD,CAAA;QAEA,IAAMI,UAAU,GAAG,WAAW,GAAA,CAAIL,MAAM,GAAG,GAAG,GAAGA,MAAM,GAAG,EAAE,CAAC,CAAA;QAE7D,IAAI;YACF,OAAOJ,YAAY,CAACG,YAAY,CAACM,UAAU,EAAE;gBAC3CC,UAAU,EAAA,SAAAA,UAAC/B,CAAAA,IAAI,EAAE;oBACf,OAAOA,IAAI,CAAA;iBACZ;gBACDgC,eAAe,EAAA,SAAAA,eAACC,CAAAA,SAAS,EAAE;oBACzB,OAAOA,SAAS,CAAA;gBAClB,CAAA;YACF,CAAC,CAAC,CAAA;SACH,CAAC,OAAOC,CAAC,EAAE;YACV,mEAAA;YACA,yEAAA;YACA,sBAAA;YACApC,OAAO,CAACC,IAAI,CACV,sBAAsB,GAAG+B,UAAU,GAAG,wBACxC,CAAC,CAAA;YACD,OAAO,IAAI,CAAA;QACb,CAAA;IACF,CAAC,CAAA;IAED,SAASK,eAAeA,GAAuB;QAAA,IAAtBhB,MAAM,IAAA5C,SAAA,CAAAC,MAAA,GAAAD,CAAAA,IAAAA,SAAA,CAAA6D,CAAAA,CAAAA,KAAAA,SAAA,GAAA7D,SAAA,CAAG2C,CAAAA,CAAAA,GAAAA,SAAS,EAAE,CAAA;QAC3C,IAAMmB,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAA;YAAA,OAAKH,eAAe,CAACG,IAAI,CAAC,CAAA;QAAA,CAAA,CAAA;QAEjD;;;KAGF,GACED,SAAS,CAACE,OAAO,GAAGC,OAAO,CAAA;QAE3B;;;KAGF,GACEH,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;QAEtB,IAAI,CAACtB,MAAM,KAAI,CAACA,MAAM,EAACG,QAAQ,IAAIH,MAAM,EAACG,QAAQ,CAACoB,QAAQ,KAAK,CAAC,EAAE;YACjE,uDAAA;YACA,uCAAA;YACAL,SAAS,CAACM,WAAW,GAAG,KAAK,CAAA;YAE7B,OAAON,SAAS,CAAA;QAClB,CAAA;QAEA,IAAMO,gBAAgB,GAAGzB,MAAM,EAACG,QAAQ,CAAA;QAExC,IAAMA,QAAQ,GAAKH,MAAM,EAAnBG,QAAQ,CAAA;QACd,IACEuB,gBAAgB,GASd1B,MAAM,EATR0B,gBAAgB,EAChBC,mBAAmB,GAQjB3B,MAAM,EARR2B,mBAAmB,EACnBC,IAAI,GAOF5B,MAAM,EAPR4B,IAAI,EACJC,OAAO,GAML7B,MAAM,EANR6B,OAAO,EACPC,UAAU,GAKR9B,MAAM,EALR8B,UAAU,EAAAC,oBAAA,GAKR/B,MAAM,EAJRgC,YAAY,EAAZA,YAAY,GAAAD,oBAAA,KAAA,KAAA,CAAA,GAAG/B,MAAM,EAACgC,YAAY,IAAIhC,MAAM,EAACiC,eAAe,GAAAF,oBAAA,EAC5DG,eAAe,GAGblC,MAAM,EAHRkC,eAAe,EACfC,SAAS,GAEPnC,MAAM,EAFRmC,SAAS,EACTjC,YAAY,GACVF,MAAM,EADRE,YAAY,CAAA;QAGd,IAAMkC,gBAAgB,GAAGP,OAAO,CAACrG,SAAS,CAAA;QAE1C,IAAM6G,SAAS,GAAGhE,YAAY,CAAC+D,gBAAgB,EAAE,WAAW,CAAC,CAAA;QAC7D,IAAME,cAAc,GAAGjE,YAAY,CAAC+D,gBAAgB,EAAE,aAAa,CAAC,CAAA;QACpE,IAAMG,aAAa,GAAGlE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;QAClE,IAAMI,aAAa,GAAGnE,YAAY,CAAC+D,gBAAgB,EAAE,YAAY,CAAC,CAAA;QAElE,kEAAA;QACA,+DAAA;QACA,oFAAA;QACA,uEAAA;QACA,oEAAA;QACA,gBAAA;QACA,IAAI,OAAOT,mBAAmB,KAAK,UAAU,EAAE;YAC7C,IAAMc,QAAQ,GAAGtC,QAAQ,CAACuC,aAAa,CAAC,UAAU,CAAC,CAAA;YACnD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACE,OAAO,CAACC,aAAa,EAAE;gBACtDzC,QAAQ,GAAGsC,QAAQ,CAACE,OAAO,CAACC,aAAa,CAAA;YAC3C,CAAA;QACF,CAAA;QAEA,IAAMC,kBAAkB,GAAG5C,yBAAyB,CAClDC,YAAY,EACZuB,gBACF,CAAC,CAAA;QACD,IAAMqB,SAAS,GAAGD,kBAAkB,GAAGA,kBAAkB,CAACjC,UAAU,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;QAE7E,IAAAmC,SAAA,GAKI5C,QAAQ,EAJV6C,cAAc,GAAAD,SAAA,CAAdC,cAAc,EACdC,kBAAkB,GAAAF,SAAA,CAAlBE,kBAAkB,EAClBC,sBAAsB,GAAAH,SAAA,CAAtBG,sBAAsB,EACtBC,oBAAoB,GAAAJ,SAAA,CAApBI,oBAAoB,CAAA;QAEtB,IAAQC,UAAU,GAAK3B,gBAAgB,CAA/B2B,UAAU,CAAA;QAElB,IAAIC,YAAY,GAAG,CAAA,CAAE,CAAA;QACrB,IAAI;YACFA,YAAY,GAAGpF,KAAK,CAACkC,QAAQ,CAAC,CAACkD,YAAY,GAAGlD,QAAQ,CAACkD,YAAY,GAAG,CAAA,CAAE,CAAA;QAC1E,CAAC,CAAC,OAAOtC,CAAC,EAAE,CAAA,CAAC;QAEb,IAAIuC,KAAK,GAAG,CAAA,CAAE,CAAA;QAEd;;KAEF,GACEpC,SAAS,CAACM,WAAW,GACnB,OAAOgB,aAAa,KAAK,UAAU,IACnCQ,cAAc,IACdA,cAAc,CAACO,kBAAkB,KAAKtC,SAAS,IAC/CoC,YAAY,KAAK,CAAC,CAAA;QAEpB,IACEhE,eAAa,GAQXmE,aARW,EACblE,UAAQ,GAONkE,QAPM,EACRjE,aAAW,GAMTiE,WANS,EACXhE,WAAS,GAKPgE,SALO,EACT/D,WAAS,GAIP+D,SAJO,EACT7D,mBAAiB,GAGf6D,iBAHe,EACjB5D,iBAAe,GAEb4D,eAFa,EACf1D,gBAAc,GACZ0D,cADY,CAAA;QAGhB,IAAM9D,gBAAc,GAAK8D,cAAL,CAAA;QAEpB;;;KAGF,GAEE,yBAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;QACvB,IAAMC,oBAAoB,GAAGjG,QAAQ,CAAC,CAAA,CAAE,EAAAkG,EAAAA,CAAAA,MAAA,CAAAvI,kBAAA,CACnCwI,MAAS,CAAAxI,EAAAA,kBAAA,CACTwI,KAAQ,CAAA,EAAAxI,kBAAA,CACRwI,UAAe,CAAA,EAAAxI,kBAAA,CACfwI,QAAW,GAAAxI,kBAAA,CACXwI,IAAS,EACb,CAAC,CAAA;QAEF,2BAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;QACvB,IAAMC,oBAAoB,GAAGrG,QAAQ,CAAC,CAAA,CAAE,EAAA,EAAA,CAAAkG,MAAA,CAAAvI,kBAAA,CACnC2I,IAAU,CAAA3I,EAAAA,kBAAA,CACV2I,GAAS,CAAA3I,EAAAA,kBAAA,CACT2I,MAAY,CAAA,EAAA3I,kBAAA,CACZ2I,GAAS,EACb,CAAC,CAAA;QAEF;;;;;KAKF,GACE,IAAIC,uBAAuB,GAAG9J,MAAM,CAACM,IAAI,CACvCN,MAAM,CAACO,MAAM,CAAC,IAAI,EAAE;YAClBwJ,YAAY,EAAE;gBACZC,QAAQ,EAAE,IAAI;gBACdC,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAE,IAAI;gBAChB3F,KAAK,EAAE,IAAA;aACR;YACD4F,kBAAkB,EAAE;gBAClBH,QAAQ,EAAE,IAAI;gBACdC,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAE,IAAI;gBAChB3F,KAAK,EAAE,IAAA;aACR;YACD6F,8BAA8B,EAAE;gBAC9BJ,QAAQ,EAAE,IAAI;gBACdC,YAAY,EAAE,KAAK;gBACnBC,UAAU,EAAE,IAAI;gBAChB3F,KAAK,EAAE,KAAA;YACT,CAAA;QACF,CAAC,CACH,CAAC,CAAA;QAED,+DAAA,GACA,IAAI8F,WAAW,GAAG,IAAI,CAAA;QAEtB,qEAAA,GACA,IAAIC,WAAW,GAAG,IAAI,CAAA;QAEtB,sCAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;QAE1B,6CAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;QAE1B,wCAAA,GACA,IAAIC,uBAAuB,GAAG,KAAK,CAAA;QAEnC;yDACF,GACE,IAAIC,wBAAwB,GAAG,IAAI,CAAA;QAEnC;;KAEF,GACE,IAAIC,kBAAkB,GAAG,KAAK,CAAA;QAE9B;;KAEF,GACE,IAAIC,YAAY,GAAG,IAAI,CAAA;QAEvB,wDAAA,GACA,IAAIC,cAAc,GAAG,KAAK,CAAA;QAE1B,sEAAA,GACA,IAAIC,UAAU,GAAG,KAAK,CAAA;QAEtB;4EACF,GACE,IAAIC,UAAU,GAAG,KAAK,CAAA;QAEtB;;;KAGF,GACE,IAAIC,UAAU,GAAG,KAAK,CAAA;QAEtB;wEACF,GACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;QAE/B;6CACF,GACE,IAAIC,mBAAmB,GAAG,KAAK,CAAA;QAE/B;;KAEF,GACE,IAAIC,YAAY,GAAG,IAAI,CAAA;QAEvB;;;;;;;;;;;;KAYF,GACE,IAAIC,oBAAoB,GAAG,KAAK,CAAA;QAChC,IAAMC,2BAA2B,GAAG,eAAe,CAAA;QAEnD,+CAAA,GACA,IAAIC,YAAY,GAAG,IAAI,CAAA;QAEvB;0EACF,GACE,IAAIC,QAAQ,GAAG,KAAK,CAAA;QAEpB,qDAAA,GACA,IAAIC,YAAY,GAAG,CAAA,CAAE,CAAA;QAErB,uDAAA,GACA,IAAIC,eAAe,GAAG,IAAI,CAAA;QAC1B,IAAMC,uBAAuB,GAAGnI,QAAQ,CAAC,CAAA,CAAE,EAAE;YAC3C,gBAAgB;YAChB,OAAO;YACP,UAAU;YACV,MAAM;YACN,eAAe;YACf,MAAM;YACN,QAAQ;YACR,MAAM;YACN,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAO;YACP,SAAS;YACT,UAAU;YACV,UAAU;YACV,WAAW;YACX,QAAQ;YACR,OAAO;YACP,KAAK;YACL,UAAU;YACV,OAAO;YACP,OAAO;YACP,OAAO;YACP,KAAK;SACN,CAAC,CAAA;QAEF,qCAAA,GACA,IAAIoI,aAAa,GAAG,IAAI,CAAA;QACxB,IAAMC,qBAAqB,GAAGrI,QAAQ,CAAC,CAAA,CAAE,EAAE;YACzC,OAAO;YACP,OAAO;YACP,KAAK;YACL,QAAQ;YACR,OAAO;YACP,OAAO;SACR,CAAC,CAAA;QAEF,iDAAA,GACA,IAAIsI,mBAAmB,GAAG,IAAI,CAAA;QAC9B,IAAMC,2BAA2B,GAAGvI,QAAQ,CAAC,CAAA,CAAE,EAAE;YAC/C,KAAK;YACL,OAAO;YACP,KAAK;YACL,IAAI;YACJ,OAAO;YACP,MAAM;YACN,SAAS;YACT,aAAa;YACb,MAAM;YACN,SAAS;YACT,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;SACR,CAAC,CAAA;QAEF,IAAMwI,gBAAgB,GAAG,oCAAoC,CAAA;QAC7D,IAAMC,aAAa,GAAG,4BAA4B,CAAA;QAClD,IAAMC,cAAc,GAAG,8BAA8B,CAAA;QACrD,sBAAA,GACA,IAAIC,SAAS,GAAGD,cAAc,CAAA;QAC9B,IAAIE,cAAc,GAAG,KAAK,CAAA;QAE1B,gCAAA,GACA,IAAIC,kBAAkB,GAAG,IAAI,CAAA;QAC7B,IAAMC,0BAA0B,GAAG9I,QAAQ,CACzC,CAAA,CAAE,EACF;YAACwI,gBAAgB;YAAEC,aAAa;YAAEC,cAAc;SAAC,EACjDlK,cACF,CAAC,CAAA;QAED,qCAAA,GACA,IAAIuK,iBAAiB,CAAA;QACrB,IAAMC,4BAA4B,GAAG;YAAC,uBAAuB;YAAE,WAAW;SAAC,CAAA;QAC3E,IAAMC,yBAAyB,GAAG,WAAW,CAAA;QAC7C,IAAI9I,iBAAiB,CAAA;QAErB,+CAAA,GACA,IAAI+I,MAAM,GAAG,IAAI,CAAA;QAEjB,kDAAA,GACA,kDAAA,GAEA,IAAMC,WAAW,GAAGzG,QAAQ,CAACuC,aAAa,CAAC,MAAM,CAAC,CAAA;QAElD,IAAMmE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,SAAS,EAAE;YAC7C,OAAOA,SAAS,YAAYlK,MAAM,IAAIkK,SAAS,YAAYC,QAAQ,CAAA;SACpE,CAAA;QAED;;;;KAIF,GACE,sCAAA;QACA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;YAClC,IAAIN,MAAM,IAAIA,MAAM,KAAKM,GAAG,EAAE;gBAC5B,OAAA;YACF,CAAA;YAEA,8CAAA,GACA,IAAI,CAACA,GAAG,IAAI7G,OAAA,CAAO6G,GAAG,CAAA,KAAK,QAAQ,EAAE;gBACnCA,GAAG,GAAG,CAAA,CAAE,CAAA;YACV,CAAA;YAEA,wDAAA,GACAA,GAAG,GAAGhJ,KAAK,CAACgJ,GAAG,CAAC,CAAA;YAEhBT,iBAAiB,GACf,mDAAA;YACAC,4BAA4B,CAACjK,OAAO,CAACyK,GAAG,CAACT,iBAAiB,CAAC,KAAK,CAAC,CAAC,GAC7DA,iBAAiB,GAAGE,yBAAyB,GAC7CF,iBAAiB,GAAGS,GAAG,CAACT,iBAAkB,CAAA;YAEjD,iGAAA;YACA5I,iBAAiB,GACf4I,iBAAiB,KAAK,uBAAuB,GACzCvK,cAAc,GACdH,iBAAiB,CAAA;YAEvB,gCAAA,GACA2H,YAAY,GACV,cAAc,IAAIwD,GAAG,GACjBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAACxD,YAAY,EAAE7F,iBAAiB,CAAC,GACjD8F,oBAAoB,CAAA;YAC1BG,YAAY,GACV,cAAc,IAAIoD,GAAG,GACjBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAACpD,YAAY,EAAEjG,iBAAiB,CAAC,GACjDkG,oBAAoB,CAAA;YAC1BwC,kBAAkB,GAChB,oBAAoB,IAAIW,GAAG,GACvBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAACX,kBAAkB,EAAErK,cAAc,CAAC,GACpDsK,0BAA0B,CAAA;YAChCR,mBAAmB,GACjB,mBAAmB,IAAIkB,GAAG,GACtBxJ,QAAQ,CACNQ,KAAK,CAAC+H,2BAA2B,CAAC,EAAE,6BAAA;YACpCiB,GAAG,CAACC,iBAAiB,EAAE,6BAAA;YACvBtJ,iBAAiB,CAAA,6BAAA;cAClB,6BAAA;eACDoI,2BAA2B,CAAA;YACjCH,aAAa,GACX,mBAAmB,IAAIoB,GAAG,GACtBxJ,QAAQ,CACNQ,KAAK,CAAC6H,qBAAqB,CAAC,EAAE,6BAAA;YAC9BmB,GAAG,CAACE,iBAAiB,EAAE,6BAAA;YACvBvJ,iBAAiB,CAAA,6BAAA;cAClB,6BAAA;eACDkI,qBAAqB,CAAA;YAC3BH,eAAe,GACb,iBAAiB,IAAIsB,GAAG,GACpBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAACtB,eAAe,EAAE/H,iBAAiB,CAAC,GACpDgI,uBAAuB,CAAA;YAC7BrB,WAAW,GACT,aAAa,IAAI0C,GAAG,GAChBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAAC1C,WAAW,EAAE3G,iBAAiB,CAAC,GAChD,CAAA,CAAE,CAAA;YACR4G,WAAW,GACT,aAAa,IAAIyC,GAAG,GAChBxJ,QAAQ,CAAC,CAAA,CAAE,EAAEwJ,GAAG,CAACzC,WAAW,EAAE5G,iBAAiB,CAAC,GAChD,CAAA,CAAE,CAAA;YACR8H,YAAY,GAAG,cAAc,IAAIuB,GAAG,GAAGA,GAAG,CAACvB,YAAY,GAAG,KAAK,CAAA;YAC/DjB,eAAe,GAAGwC,GAAG,CAACxC,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;YAChDC,eAAe,GAAGuC,GAAG,CAACvC,eAAe,KAAK,KAAK,CAAC,CAAA,eAAA;YAChDC,uBAAuB,GAAGsC,GAAG,CAACtC,uBAAuB,IAAI,KAAK,CAAC,CAAA,gBAAA;YAC/DC,wBAAwB,GAAGqC,GAAG,CAACrC,wBAAwB,KAAK,KAAK,CAAC,CAAA,eAAA;YAClEC,kBAAkB,GAAGoC,GAAG,CAACpC,kBAAkB,IAAI,KAAK,CAAC,CAAA,gBAAA;YACrDC,YAAY,GAAGmC,GAAG,CAACnC,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;YAC1CC,cAAc,GAAGkC,GAAG,CAAClC,cAAc,IAAI,KAAK,CAAC,CAAA,gBAAA;YAC7CG,UAAU,GAAG+B,GAAG,CAAC/B,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;YACrCC,mBAAmB,GAAG8B,GAAG,CAAC9B,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;YACvDC,mBAAmB,GAAG6B,GAAG,CAAC7B,mBAAmB,IAAI,KAAK,CAAC,CAAA,gBAAA;YACvDH,UAAU,GAAGgC,GAAG,CAAChC,UAAU,IAAI,KAAK,CAAC,CAAA,gBAAA;YACrCI,YAAY,GAAG4B,GAAG,CAAC5B,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;YAC1CC,oBAAoB,GAAG2B,GAAG,CAAC3B,oBAAoB,IAAI,KAAK,CAAC,CAAA,gBAAA;YACzDE,YAAY,GAAGyB,GAAG,CAACzB,YAAY,KAAK,KAAK,CAAC,CAAA,eAAA;YAC1CC,QAAQ,GAAGwB,GAAG,CAACxB,QAAQ,IAAI,KAAK,CAAC,CAAA,gBAAA;YACjC/F,gBAAc,GAAGuH,GAAG,CAACG,kBAAkB,IAAI1H,gBAAc,CAAA;YACzD0G,SAAS,GAAGa,GAAG,CAACb,SAAS,IAAID,cAAc,CAAA;YAC3CnC,uBAAuB,GAAGiD,GAAG,CAACjD,uBAAuB,IAAI,CAAA,CAAE,CAAA;YAC3D,IACEiD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAC,EAC3D;gBACAD,uBAAuB,CAACC,YAAY,GAClCgD,GAAG,CAACjD,uBAAuB,CAACC,YAAY,CAAA;YAC5C,CAAA;YAEA,IACEgD,GAAG,CAACjD,uBAAuB,IAC3B6C,iBAAiB,CAACI,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAC,EACjE;gBACAL,uBAAuB,CAACK,kBAAkB,GACxC4C,GAAG,CAACjD,uBAAuB,CAACK,kBAAkB,CAAA;YAClD,CAAA;YAEA,IACE4C,GAAG,CAACjD,uBAAuB,IAC3B,OAAOiD,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,KAC/D,SAAS,EACX;gBACAN,uBAAuB,CAACM,8BAA8B,GACpD2C,GAAG,CAACjD,uBAAuB,CAACM,8BAA8B,CAAA;YAC9D,CAAA;YAEA,IAAIO,kBAAkB,EAAE;gBACtBH,eAAe,GAAG,KAAK,CAAA;YACzB,CAAA;YAEA,IAAIS,mBAAmB,EAAE;gBACvBD,UAAU,GAAG,IAAI,CAAA;YACnB,CAAA;YAEA,sBAAA,GACA,IAAIQ,YAAY,EAAE;gBAChBjC,YAAY,GAAGhG,QAAQ,CAAC,CAAA,CAAE,EAAArC,kBAAA,CAAMwI,IAAS,CAAC,CAAC,CAAA;gBAC3CC,YAAY,GAAG,EAAE,CAAA;gBACjB,IAAI6B,YAAY,CAAC7G,IAAI,KAAK,IAAI,EAAE;oBAC9BpB,QAAQ,CAACgG,YAAY,EAAEG,MAAS,CAAC,CAAA;oBACjCnG,QAAQ,CAACoG,YAAY,EAAEE,IAAU,CAAC,CAAA;gBACpC,CAAA;gBAEA,IAAI2B,YAAY,CAAC5G,GAAG,KAAK,IAAI,EAAE;oBAC7BrB,QAAQ,CAACgG,YAAY,EAAEG,KAAQ,CAAC,CAAA;oBAChCnG,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;oBACjCtG,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACnC,CAAA;gBAEA,IAAI2B,YAAY,CAAC3G,UAAU,KAAK,IAAI,EAAE;oBACpCtB,QAAQ,CAACgG,YAAY,EAAEG,UAAe,CAAC,CAAA;oBACvCnG,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;oBACjCtG,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACnC,CAAA;gBAEA,IAAI2B,YAAY,CAACzG,MAAM,KAAK,IAAI,EAAE;oBAChCxB,QAAQ,CAACgG,YAAY,EAAEG,QAAW,CAAC,CAAA;oBACnCnG,QAAQ,CAACoG,YAAY,EAAEE,MAAY,CAAC,CAAA;oBACpCtG,QAAQ,CAACoG,YAAY,EAAEE,GAAS,CAAC,CAAA;gBACnC,CAAA;YACF,CAAA;YAEA,kCAAA,GACA,IAAIkD,GAAG,CAACI,QAAQ,EAAE;gBAChB,IAAI5D,YAAY,KAAKC,oBAAoB,EAAE;oBACzCD,YAAY,GAAGxF,KAAK,CAACwF,YAAY,CAAC,CAAA;gBACpC,CAAA;gBAEAhG,QAAQ,CAACgG,YAAY,EAAEwD,GAAG,CAACI,QAAQ,EAAEzJ,iBAAiB,CAAC,CAAA;YACzD,CAAA;YAEA,IAAIqJ,GAAG,CAACK,QAAQ,EAAE;gBAChB,IAAIzD,YAAY,KAAKC,oBAAoB,EAAE;oBACzCD,YAAY,GAAG5F,KAAK,CAAC4F,YAAY,CAAC,CAAA;gBACpC,CAAA;gBAEApG,QAAQ,CAACoG,YAAY,EAAEoD,GAAG,CAACK,QAAQ,EAAE1J,iBAAiB,CAAC,CAAA;YACzD,CAAA;YAEA,IAAIqJ,GAAG,CAACC,iBAAiB,EAAE;gBACzBzJ,QAAQ,CAACsI,mBAAmB,EAAEkB,GAAG,CAACC,iBAAiB,EAAEtJ,iBAAiB,CAAC,CAAA;YACzE,CAAA;YAEA,IAAIqJ,GAAG,CAACtB,eAAe,EAAE;gBACvB,IAAIA,eAAe,KAAKC,uBAAuB,EAAE;oBAC/CD,eAAe,GAAG1H,KAAK,CAAC0H,eAAe,CAAC,CAAA;gBAC1C,CAAA;gBAEAlI,QAAQ,CAACkI,eAAe,EAAEsB,GAAG,CAACtB,eAAe,EAAE/H,iBAAiB,CAAC,CAAA;YACnE,CAAA;YAEA,iDAAA,GACA,IAAI4H,YAAY,EAAE;gBAChB/B,YAAY,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;YAC9B,CAAA;YAEA,0EAAA,GACA,IAAIsB,cAAc,EAAE;gBAClBtH,QAAQ,CAACgG,YAAY,EAAE;oBAAC,MAAM;oBAAE,MAAM;oBAAE,MAAM;iBAAC,CAAC,CAAA;YAClD,CAAA;YAEA,0EAAA,GACA,IAAIA,YAAY,CAAC8D,KAAK,EAAE;gBACtB9J,QAAQ,CAACgG,YAAY,EAAE;oBAAC,OAAO;iBAAC,CAAC,CAAA;gBACjC,OAAOc,WAAW,CAACiD,KAAK,CAAA;YAC1B,CAAA;YAEA,iDAAA;YACA,uCAAA;YACA,IAAIjN,MAAM,EAAE;gBACVA,MAAM,CAAC0M,GAAG,CAAC,CAAA;YACb,CAAA;YAEAN,MAAM,GAAGM,GAAG,CAAA;SACb,CAAA;QAED,IAAMQ,8BAA8B,GAAGhK,QAAQ,CAAC,CAAA,CAAE,EAAE;YAClD,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,OAAO;SACR,CAAC,CAAA;QAEF,IAAMiK,uBAAuB,GAAGjK,QAAQ,CAAC,CAAA,CAAE,EAAE;YAAC,gBAAgB;SAAC,CAAC,CAAA;QAEhE,oDAAA;QACA,gDAAA;QACA,kDAAA;QACA,kBAAA;QACA,IAAMkK,4BAA4B,GAAGlK,QAAQ,CAAC,CAAA,CAAE,EAAE;YAChD,OAAO;YACP,OAAO;YACP,MAAM;YACN,GAAG;YACH,QAAQ;SACT,CAAC,CAAA;QAEF;;kBAEF,GACE,IAAMmK,YAAY,GAAGnK,QAAQ,CAAC,CAAA,CAAE,EAAEmG,KAAQ,CAAC,CAAA;QAC3CnG,QAAQ,CAACmK,YAAY,EAAEhE,UAAe,CAAC,CAAA;QACvCnG,QAAQ,CAACmK,YAAY,EAAEhE,aAAkB,CAAC,CAAA;QAE1C,IAAMiE,eAAe,GAAGpK,QAAQ,CAAC,CAAA,CAAE,EAAEmG,QAAW,CAAC,CAAA;QACjDnG,QAAQ,CAACoK,eAAe,EAAEjE,gBAAqB,CAAC,CAAA;QAEhD;;;;;;;KAOF,GACE,IAAMkE,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAa/J,OAAO,EAAE;YAC9C,IAAIgK,MAAM,GAAGvF,aAAa,CAACzE,OAAO,CAAC,CAAA;YAEnC,wDAAA;YACA,qDAAA;YACA,IAAI,CAACgK,MAAM,IAAI,CAACA,MAAM,CAACC,OAAO,EAAE;gBAC9BD,MAAM,GAAG;oBACPE,YAAY,EAAE7B,SAAS;oBACvB4B,OAAO,EAAE,UAAA;iBACV,CAAA;YACH,CAAA;YAEA,IAAMA,OAAO,GAAGlM,iBAAiB,CAACiC,OAAO,CAACiK,OAAO,CAAC,CAAA;YAClD,IAAME,aAAa,GAAGpM,iBAAiB,CAACiM,MAAM,CAACC,OAAO,CAAC,CAAA;YAEvD,IAAI,CAAC1B,kBAAkB,CAACvI,OAAO,CAACkK,YAAY,CAAC,EAAE;gBAC7C,OAAO,KAAK,CAAA;YACd,CAAA;YAEA,IAAIlK,OAAO,CAACkK,YAAY,KAAK/B,aAAa,EAAE;gBAC1C,oDAAA;gBACA,sDAAA;gBACA,uBAAA;gBACA,IAAI6B,MAAM,CAACE,YAAY,KAAK9B,cAAc,EAAE;oBAC1C,OAAO6B,OAAO,KAAK,KAAK,CAAA;gBAC1B,CAAA;gBAEA,oDAAA;gBACA,qDAAA;gBACA,2BAAA;gBACA,IAAID,MAAM,CAACE,YAAY,KAAKhC,gBAAgB,EAAE;oBAC5C,OACE+B,OAAO,KAAK,KAAK,IAAA,CAChBE,aAAa,KAAK,gBAAgB,IACjCT,8BAA8B,CAACS,aAAa,CAAC,CAAC,CAAA;gBAEpD,CAAA;gBAEA,iDAAA;gBACA,oDAAA;gBACA,OAAOC,OAAO,CAACP,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;YACvC,CAAA;YAEA,IAAIjK,OAAO,CAACkK,YAAY,KAAKhC,gBAAgB,EAAE;gBAC7C,uDAAA;gBACA,uDAAA;gBACA,uBAAA;gBACA,IAAI8B,MAAM,CAACE,YAAY,KAAK9B,cAAc,EAAE;oBAC1C,OAAO6B,OAAO,KAAK,MAAM,CAAA;gBAC3B,CAAA;gBAEA,mDAAA;gBACA,qCAAA;gBACA,IAAID,MAAM,CAACE,YAAY,KAAK/B,aAAa,EAAE;oBACzC,OAAO8B,OAAO,KAAK,MAAM,IAAIN,uBAAuB,CAACQ,aAAa,CAAC,CAAA;gBACrE,CAAA;gBAEA,oDAAA;gBACA,uDAAA;gBACA,OAAOC,OAAO,CAACN,eAAe,CAACG,OAAO,CAAC,CAAC,CAAA;YAC1C,CAAA;YAEA,IAAIjK,OAAO,CAACkK,YAAY,KAAK9B,cAAc,EAAE;gBAC3C,iDAAA;gBACA,mDAAA;gBACA,wCAAA;gBACA,IACE4B,MAAM,CAACE,YAAY,KAAK/B,aAAa,IACrC,CAACwB,uBAAuB,CAACQ,aAAa,CAAC,EACvC;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;gBAEA,IACEH,MAAM,CAACE,YAAY,KAAKhC,gBAAgB,IACxC,CAACwB,8BAA8B,CAACS,aAAa,CAAC,EAC9C;oBACA,OAAO,KAAK,CAAA;gBACd,CAAA;gBAEA,gDAAA;gBACA,mDAAA;gBACA,OACE,CAACL,eAAe,CAACG,OAAO,CAAC,IAAA,CACxBL,4BAA4B,CAACK,OAAO,CAAC,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC,CAAA;YAErE,CAAA;YAEA,6DAAA;YACA,IACExB,iBAAiB,KAAK,uBAAuB,IAC7CF,kBAAkB,CAACvI,OAAO,CAACkK,YAAY,CAAC,EACxC;gBACA,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,qDAAA;YACA,qDAAA;YACA,wDAAA;YACA,6BAAA;YACA,OAAO,KAAK,CAAA;SACb,CAAA;QAED;;;;KAIF,GACE,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAaC,IAAI,EAAE;YACnCzM,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;gBAAEvD,OAAO,EAAEsK,IAAAA;YAAK,CAAC,CAAC,CAAA;YAC/C,IAAI;gBACF,0DAAA;gBACAA,IAAI,CAACC,UAAU,CAACC,WAAW,CAACF,IAAI,CAAC,CAAA;aAClC,CAAC,OAAOtH,CAAC,EAAE;gBACV,IAAI;oBACFsH,IAAI,CAACG,SAAS,GAAG1F,SAAS,CAAA;iBAC3B,CAAC,OAAO/B,CAAC,EAAE;oBACVsH,IAAI,CAACI,MAAM,EAAE,CAAA;gBACf,CAAA;YACF,CAAA;SACD,CAAA;QAED;;;;;KAKF,GACE,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAaC,IAAI,EAAEN,IAAI,EAAE;YAC7C,IAAI;gBACFzM,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;oBAC3BsH,SAAS,EAAEP,IAAI,CAACQ,gBAAgB,CAACF,IAAI,CAAC;oBACtCG,IAAI,EAAET,IAAAA;gBACR,CAAC,CAAC,CAAA;aACH,CAAC,OAAOtH,CAAC,EAAE;gBACVnF,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;oBAC3BsH,SAAS,EAAE,IAAI;oBACfE,IAAI,EAAET,IAAAA;gBACR,CAAC,CAAC,CAAA;YACJ,CAAA;YAEAA,IAAI,CAACU,eAAe,CAACJ,IAAI,CAAC,CAAA;YAE1B,4DAAA;YACA,IAAIA,IAAI,KAAK,IAAI,IAAI,CAAC9E,YAAY,CAAC8E,IAAI,CAAC,EAAE;gBACxC,IAAIzD,UAAU,IAAIC,mBAAmB,EAAE;oBACrC,IAAI;wBACFiD,YAAY,CAACC,IAAI,CAAC,CAAA;oBACpB,CAAC,CAAC,OAAOtH,CAAC,EAAE,CAAA,CAAC;gBACf,CAAC,MAAM;oBACL,IAAI;wBACFsH,IAAI,CAACW,YAAY,CAACL,IAAI,EAAE,EAAE,CAAC,CAAA;oBAC7B,CAAC,CAAC,OAAO5H,CAAC,EAAE,CAAA,CAAC;gBACf,CAAA;YACF,CAAA;SACD,CAAA;QAED;;;;;KAKF,GACE,IAAMkI,aAAa,GAAG,SAAhBA,aAAaA,CAAaC,KAAK,EAAE;YACrC,0BAAA,GACA,IAAIC,GAAG,CAAA;YACP,IAAIC,iBAAiB,CAAA;YAErB,IAAInE,UAAU,EAAE;gBACdiE,KAAK,GAAG,mBAAmB,GAAGA,KAAK,CAAA;YACrC,CAAC,MAAM;gBACL,+EAAA,GACA,IAAMG,OAAO,GAAGlN,WAAW,CAAC+M,KAAK,EAAE,aAAa,CAAC,CAAA;gBACjDE,iBAAiB,GAAGC,OAAO,IAAIA,OAAO,CAAC,CAAC,CAAC,CAAA;YAC3C,CAAA;YAEA,IACE7C,iBAAiB,KAAK,uBAAuB,IAC7CJ,SAAS,KAAKD,cAAc,EAC5B;gBACA,4GAAA;gBACA+C,KAAK,GACH,gEAAgE,GAChEA,KAAK,GACL,gBAAgB,CAAA;YACpB,CAAA;YAEA,IAAMI,YAAY,GAAGzG,kBAAkB,GACnCA,kBAAkB,CAACjC,UAAU,CAACsI,KAAK,CAAC,GACpCA,KAAK,CAAA;YACT;;;OAGJ,GACI,IAAI9C,SAAS,KAAKD,cAAc,EAAE;gBAChC,IAAI;oBACFgD,GAAG,GAAG,IAAIhH,SAAS,EAAE,CAACoH,eAAe,CAACD,YAAY,EAAE9C,iBAAiB,CAAC,CAAA;gBACxE,CAAC,CAAC,OAAOzF,CAAC,EAAE,CAAA,CAAC;YACf,CAAA;YAEA,6DAAA,GACA,IAAI,CAACoI,GAAG,IAAI,CAACA,GAAG,CAACK,eAAe,EAAE;gBAChCL,GAAG,GAAGnG,cAAc,CAACyG,cAAc,CAACrD,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAA;gBAChE,IAAI;oBACF+C,GAAG,CAACK,eAAe,CAACE,SAAS,GAAGrD,cAAc,GAC1CvD,SAAS,GACTwG,YAAY,CAAA;iBACjB,CAAC,OAAOvI,CAAC,EAAE;gBACV,8CAAA;gBAAA,CAAA;YAEJ,CAAA;YAEA,IAAM4I,IAAI,GAAGR,GAAG,CAACQ,IAAI,IAAIR,GAAG,CAACK,eAAe,CAAA;YAE5C,IAAIN,KAAK,IAAIE,iBAAiB,EAAE;gBAC9BO,IAAI,CAACC,YAAY,CACfzJ,QAAQ,CAAC0J,cAAc,CAACT,iBAAiB,CAAC,EAC1CO,IAAI,CAACG,UAAU,CAAC,CAAC,CAAC,IAAI,IACxB,CAAC,CAAA;YACH,CAAA;YAEA,2CAAA,GACA,IAAI1D,SAAS,KAAKD,cAAc,EAAE;gBAChC,OAAOhD,oBAAoB,CAAC4G,IAAI,CAC9BZ,GAAG,EACHpE,cAAc,GAAG,MAAM,GAAG,MAC5B,CAAC,CAAC,CAAC,CAAC,CAAA;YACN,CAAA;YAEA,OAAOA,cAAc,GAAGoE,GAAG,CAACK,eAAe,GAAGG,IAAI,CAAA;SACnD,CAAA;QAED;;;;;KAKF,GACE,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,CAAa7I,IAAI,EAAE;YACtC,OAAO8B,kBAAkB,CAAC8G,IAAI,CAC5B5I,IAAI,CAACyB,aAAa,IAAIzB,IAAI,EAC1BA,IAAI,EACJ,sCAAA;YACAW,UAAU,CAACmI,YAAY,GACrBnI,UAAU,CAACoI,YAAY,GACvBpI,UAAU,CAACqI,SAAS,GACpBrI,UAAU,CAACsI,2BAA2B,GACtCtI,UAAU,CAACuI,kBAAkB,EAC/B,IAAI,EACJ,KACF,CAAC,CAAA;SACF,CAAA;QAED;;;;;KAKF,GACE,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAaC,GAAG,EAAE;YAClC,OACEA,GAAG,YAAYrI,eAAe,IAAA,CAC7B,OAAOqI,GAAG,CAACC,QAAQ,KAAK,QAAQ,IAC/B,OAAOD,GAAG,CAACE,WAAW,KAAK,QAAQ,IACnC,OAAOF,GAAG,CAAChC,WAAW,KAAK,UAAU,IACrC,CAAA,CAAEgC,GAAG,CAACG,UAAU,YAAY1I,YAAY,CAAC,IACzC,OAAOuI,GAAG,CAACxB,eAAe,KAAK,UAAU,IACzC,OAAOwB,GAAG,CAACvB,YAAY,KAAK,UAAU,IACtC,OAAOuB,GAAG,CAACtC,YAAY,KAAK,QAAQ,IACpC,OAAOsC,GAAG,CAACX,YAAY,KAAK,UAAU,IACtC,OAAOW,GAAG,CAACI,aAAa,KAAK,UAAU,CAAC,CAAA;SAE7C,CAAA;QAED;;;;;KAKF,GACE,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAa1M,MAAM,EAAE;YAChC,OAAOkC,OAAA,CAAOwB,IAAI,CAAA,KAAK,QAAQ,GAC3B1D,MAAM,YAAY0D,IAAI,GACtB1D,MAAM,IACJkC,OAAA,CAAOlC,MAAM,CAAK,KAAA,QAAQ,IAC1B,OAAOA,MAAM,CAACqD,QAAQ,KAAK,QAAQ,IACnC,OAAOrD,MAAM,CAACsM,QAAQ,KAAK,QAAQ,CAAA;SAC1C,CAAA;QAED;;;;;;;KAOF,GACE,IAAMK,YAAY,GAAG,SAAfA,YAAYA,CAAaC,UAAU,EAAEC,WAAW,EAAEC,IAAI,EAAE;YAC5D,IAAI,CAAC1H,KAAK,CAACwH,UAAU,CAAC,EAAE;gBACtB,OAAA;YACF,CAAA;YAEAzP,YAAY,CAACiI,KAAK,CAACwH,UAAU,CAAC,EAAE,SAACG,IAAI,EAAK;gBACxCA,IAAI,CAAClB,IAAI,CAAC7I,SAAS,EAAE6J,WAAW,EAAEC,IAAI,EAAErE,MAAM,CAAC,CAAA;YACjD,CAAC,CAAC,CAAA;SACH,CAAA;QAED;;;;;;;;;KASF,GACE,IAAMuE,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaH,WAAW,EAAE;YAC/C,IAAIpI,OAAO,CAAA;YAEX,6BAAA,GACAkI,YAAY,CAAC,wBAAwB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;YAEzD,gDAAA,GACA,IAAIT,YAAY,CAACS,WAAW,CAAC,EAAE;gBAC7B3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,qCAAA,GACA,IAAIpO,UAAU,CAAC,iBAAiB,EAAEoO,WAAW,CAACP,QAAQ,CAAC,EAAE;gBACvDpC,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,+CAAA,GACA,IAAM/C,OAAO,GAAGpK,iBAAiB,CAACmN,WAAW,CAACP,QAAQ,CAAC,CAAA;YAEvD,6BAAA,GACAK,YAAY,CAAC,qBAAqB,EAAEE,WAAW,EAAE;gBAC/C/C,OAAO,EAAPA,OAAO;gBACPmD,WAAW,EAAE1H,YAAAA;YACf,CAAC,CAAC,CAAA;YAEF,oDAAA,GACA,IACEsH,WAAW,CAACJ,aAAa,EAAE,IAC3B,CAACC,OAAO,CAACG,WAAW,CAACK,iBAAiB,CAAC,IAAA,CACtC,CAACR,OAAO,CAACG,WAAW,CAACpI,OAAO,CAAC,IAC5B,CAACiI,OAAO,CAACG,WAAW,CAACpI,OAAO,CAACyI,iBAAiB,CAAC,CAAC,IAClDzO,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACrB,SAAS,CAAC,IAC5C/M,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACN,WAAW,CAAC,EAC9C;gBACArC,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,mDAAA,GACA,IACE/C,OAAO,KAAK,QAAQ,IACpBrL,UAAU,CAAC,YAAY,EAAEoO,WAAW,CAACrB,SAAS,CAAC,EAC/C;gBACAtB,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,mDAAA,GACA,IAAIA,WAAW,CAACxJ,QAAQ,KAAK,CAAC,EAAE;gBAC9B6G,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,gDAAA,GACA,IACEjG,YAAY,IACZiG,WAAW,CAACxJ,QAAQ,KAAK,CAAC,IAC1B5E,UAAU,CAAC,SAAS,EAAEoO,WAAW,CAACC,IAAI,CAAC,EACvC;gBACA5C,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,mDAAA,GACA,IAAI,CAACtH,YAAY,CAACuE,OAAO,CAAC,IAAIzD,WAAW,CAACyD,OAAO,CAAC,EAAE;gBAClD,+CAAA,GACA,IAAI,CAACzD,WAAW,CAACyD,OAAO,CAAC,IAAIqD,uBAAuB,CAACrD,OAAO,CAAC,EAAE;oBAC7D,IACEhE,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE+D,OAAO,CAAC,EAEzD,OAAO,KAAK,CAAA;oBACd,IACEhE,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACxD/C,uBAAuB,CAACC,YAAY,CAAC+D,OAAO,CAAC,EAE7C,OAAO,KAAK,CAAA;gBAChB,CAAA;gBAEA,+CAAA,GACA,IAAIxC,YAAY,IAAI,CAACG,eAAe,CAACqC,OAAO,CAAC,EAAE;oBAC7C,IAAMM,UAAU,GAAG9F,aAAa,CAACuI,WAAW,CAAC,IAAIA,WAAW,CAACzC,UAAU,CAAA;oBACvE,IAAMwB,UAAU,GAAGvH,aAAa,CAACwI,WAAW,CAAC,IAAIA,WAAW,CAACjB,UAAU,CAAA;oBAEvE,IAAIA,UAAU,IAAIxB,UAAU,EAAE;wBAC5B,IAAMgD,UAAU,GAAGxB,UAAU,CAACzM,MAAM,CAAA;wBAEpC,IAAK,IAAIkO,CAAC,GAAGD,UAAU,GAAG,CAAC,EAAEC,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,CAAE;4BACxC,IAAMC,UAAU,GAAGnJ,SAAS,CAACyH,UAAU,CAACyB,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;4BACjDC,UAAU,CAACC,cAAc,GAAG,CAACV,WAAW,CAACU,cAAc,IAAI,CAAC,IAAI,CAAC,CAAA;4BACjEnD,UAAU,CAACsB,YAAY,CAAC4B,UAAU,EAAElJ,cAAc,CAACyI,WAAW,CAAC,CAAC,CAAA;wBAClE,CAAA;oBACF,CAAA;gBACF,CAAA;gBAEA3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,+CAAA,GACA,IAAIA,WAAW,YAAYlJ,OAAO,IAAI,CAACiG,oBAAoB,CAACiD,WAAW,CAAC,EAAE;gBACxE3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,6DAAA,GACA,IACE,CAAC/C,OAAO,KAAK,UAAU,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,UAAU,KACxBrL,UAAU,CAAC,6BAA6B,EAAEoO,WAAW,CAACrB,SAAS,CAAC,EAChE;gBACAtB,YAAY,CAAC2C,WAAW,CAAC,CAAA;gBACzB,OAAO,IAAI,CAAA;YACb,CAAA;YAEA,gDAAA,GACA,IAAIlG,kBAAkB,IAAIkG,WAAW,CAACxJ,QAAQ,KAAK,CAAC,EAAE;gBACpD,kCAAA,GACAoB,OAAO,GAAGoI,WAAW,CAACN,WAAW,CAAA;gBACjC9H,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAEtD,eAAa,EAAE,GAAG,CAAC,CAAA;gBACpDsD,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAErD,UAAQ,EAAE,GAAG,CAAC,CAAA;gBAC/CqD,OAAO,GAAGtG,aAAa,CAACsG,OAAO,EAAEpD,aAAW,EAAE,GAAG,CAAC,CAAA;gBAClD,IAAIwL,WAAW,CAACN,WAAW,KAAK9H,OAAO,EAAE;oBACvC/G,SAAS,CAACsF,SAAS,CAACI,OAAO,EAAE;wBAAEvD,OAAO,EAAEgN,WAAW,CAAC1I,SAAS,EAAC;oBAAE,CAAC,CAAC,CAAA;oBAClE0I,WAAW,CAACN,WAAW,GAAG9H,OAAO,CAAA;gBACnC,CAAA;YACF,CAAA;YAEA,6BAAA,GACAkI,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;YAExD,OAAO,KAAK,CAAA;SACb,CAAA;QAED;;;;;;;KAOF,GACE,sCAAA;QACA,IAAMW,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAaC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,EAAE;YACxD,sCAAA,GACA,IACE4G,YAAY,IAAA,CACXuG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,IAAA,CACrCnN,KAAK,IAAI0B,QAAQ,IAAI1B,KAAK,IAAImI,WAAW,CAAC,EAC3C;gBACA,OAAO,KAAK,CAAA;YACd,CAAA;YAEA;;;kEAGJ,GACI,IACElC,eAAe,IACf,CAACF,WAAW,CAACoH,MAAM,CAAC,IACpBjP,UAAU,CAAC6C,WAAS,EAAEoM,MAAM,CAAC,EAC7B,CAED;iBAAM,IAAInH,eAAe,IAAI9H,UAAU,CAAC8C,WAAS,EAAEmM,MAAM,CAAC,EAAE,CAG5D;iBAAM,IAAI,CAAC/H,YAAY,CAAC+H,MAAM,CAAC,IAAIpH,WAAW,CAACoH,MAAM,CAAC,EAAE;gBACvD,IACE,kGAAA;gBACA,qGAAA;gBACA,sHAAA;gBACCP,uBAAuB,CAACM,KAAK,CAAC,IAAA,CAC3B3H,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAE0H,KAAK,CAAC,IACtD3H,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAAC0H,KAAK,CAAE,CAAC,IAAA,CAC/C3H,uBAAuB,CAACK,kBAAkB,YAAYzH,MAAM,IAC5DD,UAAU,CAACqH,uBAAuB,CAACK,kBAAkB,EAAEuH,MAAM,CAAC,IAC7D5H,uBAAuB,CAACK,kBAAkB,YAAY0C,QAAQ,IAC7D/C,uBAAuB,CAACK,kBAAkB,CAACuH,MAAM,CAAE,CAAC,IAC1D,sEAAA;gBACA,6FAAA;gBACCA,MAAM,KAAK,IAAI,IACd5H,uBAAuB,CAACM,8BAA8B,IAAA,CACpDN,uBAAuB,CAACC,YAAY,YAAYrH,MAAM,IACtDD,UAAU,CAACqH,uBAAuB,CAACC,YAAY,EAAExF,KAAK,CAAC,IACtDuF,uBAAuB,CAACC,YAAY,YAAY8C,QAAQ,IACvD/C,uBAAuB,CAACC,YAAY,CAACxF,KAAK,CAAE,CAAE,EACpD,CAGD;qBAAM;oBACL,OAAO,KAAK,CAAA;gBACd,CAAA;YACA,6DAAA,GACF,CAAC,MAAM,IAAIsH,mBAAmB,CAAC6F,MAAM,CAAC,EAAE,CAIvC;iBAAM,IACLjP,UAAU,CAAC+C,gBAAc,EAAErD,aAAa,CAACoC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACrE,CAID;iBAAM,IACL,CAACgM,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,YAAY,IAAIA,MAAM,KAAK,MAAM,KACjED,KAAK,KAAK,QAAQ,IAClBpP,aAAa,CAACkC,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IACnCoH,aAAa,CAAC8F,KAAK,CAAC,EACpB,CAKD;iBAAM,IACLhH,uBAAuB,IACvB,CAAChI,UAAU,CAACgD,mBAAiB,EAAEtD,aAAa,CAACoC,KAAK,EAAEmB,iBAAe,EAAE,EAAE,CAAC,CAAC,EACzE,CAGD;iBAAM,IAAInB,KAAK,EAAE;gBAChB,OAAO,KAAK,CAAA;YACd,CAAC,MAAM,CAEL;YAGF,OAAO,IAAI,CAAA;SACZ,CAAA;QAED;;;;;KAKF,GACE,IAAM4M,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAarD,OAAO,EAAE;YACjD,OAAOA,OAAO,KAAK,gBAAgB,IAAI7L,WAAW,CAAC6L,OAAO,EAAElI,gBAAc,CAAC,CAAA;SAC5E,CAAA;QAED;;;;;;;;;KASF,GACE,IAAM+L,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAad,WAAW,EAAE;YACjD,IAAIe,IAAI,CAAA;YACR,IAAIrN,KAAK,CAAA;YACT,IAAImN,MAAM,CAAA;YACV,IAAI9N,CAAC,CAAA;YACL,6BAAA,GACA+M,YAAY,CAAC,0BAA0B,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;YAE3D,IAAQL,UAAU,GAAKK,WAAW,CAA1BL,UAAU,CAAA;YAElB,iEAAA,GACA,IAAI,CAACA,UAAU,IAAIJ,YAAY,CAACS,WAAW,CAAC,EAAE;gBAC5C,OAAA;YACF,CAAA;YAEA,IAAMgB,SAAS,GAAG;gBAChBC,QAAQ,EAAE,EAAE;gBACZC,SAAS,EAAE,EAAE;gBACbC,QAAQ,EAAE,IAAI;gBACdC,iBAAiB,EAAEtI,YAAAA;aACpB,CAAA;YACD/F,CAAC,GAAG4M,UAAU,CAACrN,MAAM,CAAA;YAErB,4DAAA,GACA,MAAOS,CAAC,EAAE,CAAE;gBACVgO,IAAI,GAAGpB,UAAU,CAAC5M,CAAC,CAAC,CAAA;gBACpB,IAAAsO,KAAA,GAA+BN,IAAI,EAA3BnD,IAAI,GAAAyD,KAAA,CAAJzD,IAAI,EAAEV,YAAY,GAAAmE,KAAA,CAAZnE,YAAY,CAAA;gBAC1BxJ,KAAK,GAAGkK,IAAI,KAAK,OAAO,GAAGmD,IAAI,CAACrN,KAAK,GAAGhC,UAAU,CAACqP,IAAI,CAACrN,KAAK,CAAC,CAAA;gBAC9DmN,MAAM,GAAGhO,iBAAiB,CAAC+K,IAAI,CAAC,CAAA;gBAEhC,6BAAA,GACAoD,SAAS,CAACC,QAAQ,GAAGJ,MAAM,CAAA;gBAC3BG,SAAS,CAACE,SAAS,GAAGxN,KAAK,CAAA;gBAC3BsN,SAAS,CAACG,QAAQ,GAAG,IAAI,CAAA;gBACzBH,SAAS,CAACM,aAAa,GAAGpL,SAAS,CAAC,CAAA,2DAAA;gBACpC4J,YAAY,CAAC,uBAAuB,EAAEE,WAAW,EAAEgB,SAAS,CAAC,CAAA;gBAC7DtN,KAAK,GAAGsN,SAAS,CAACE,SAAS,CAAA;gBAE3B,2CAAA,GACA,IAAIF,SAAS,CAACM,aAAa,EAAE;oBAC3B,SAAA;gBACF,CAAA;gBAEA,oBAAA,GACA3D,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;gBAEnC,2CAAA,GACA,IAAI,CAACgB,SAAS,CAACG,QAAQ,EAAE;oBACvB,SAAA;gBACF,CAAA;gBAEA,8CAAA,GACA,IAAI,CAACtH,wBAAwB,IAAIjI,UAAU,CAAC,MAAM,EAAE8B,KAAK,CAAC,EAAE;oBAC1DiK,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;oBACnC,SAAA;gBACF,CAAA;gBAEA,kDAAA,GACA,IAAIlG,kBAAkB,EAAE;oBACtBpG,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEY,eAAa,EAAE,GAAG,CAAC,CAAA;oBAChDZ,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEa,UAAQ,EAAE,GAAG,CAAC,CAAA;oBAC3Cb,KAAK,GAAGpC,aAAa,CAACoC,KAAK,EAAEc,aAAW,EAAE,GAAG,CAAC,CAAA;gBAChD,CAAA;gBAEA,wCAAA,GACA,IAAMoM,KAAK,GAAG/N,iBAAiB,CAACmN,WAAW,CAACP,QAAQ,CAAC,CAAA;gBACrD,IAAI,CAACkB,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,CAAC,EAAE;oBAC5C,SAAA;gBACF,CAAA;gBAEA;;SAEN,GACM,IAAI6G,oBAAoB,IAAA,CAAKsG,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,CAAC,EAAE;oBAClE,uCAAA;oBACAlD,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;oBAEnC,8EAAA;oBACAtM,KAAK,GAAG8G,2BAA2B,GAAG9G,KAAK,CAAA;gBAC7C,CAAA;gBAEA,gEAAA,GACA,IAAIqG,YAAY,IAAInI,UAAU,CAAC,+BAA+B,EAAE8B,KAAK,CAAC,EAAE;oBACtEiK,gBAAgB,CAACC,IAAI,EAAEoC,WAAW,CAAC,CAAA;oBACnC,SAAA;gBACF,CAAA;gBAEA,gDAAA,GACA,IACElI,kBAAkB,IAClBzC,OAAA,CAAOF,YAAY,CAAK,KAAA,QAAQ,IAChC,OAAOA,YAAY,CAACoM,gBAAgB,KAAK,UAAU,EACnD;oBACA,IAAIrE,YAAY,EAAE,CAEjB;yBAAM;wBACL,OAAQ/H,YAAY,CAACoM,gBAAgB,CAACX,KAAK,EAAEC,MAAM,CAAC;4BAClD,KAAK,aAAa;gCAAE;oCAClBnN,KAAK,GAAGoE,kBAAkB,CAACjC,UAAU,CAACnC,KAAK,CAAC,CAAA;oCAC5C,MAAA;gCACF,CAAA;4BAEA,KAAK,kBAAkB;gCAAE;oCACvBA,KAAK,GAAGoE,kBAAkB,CAAChC,eAAe,CAACpC,KAAK,CAAC,CAAA;oCACjD,MAAA;gCACF,CAAA;wBAKF,CAAA;oBACF,CAAA;gBACF,CAAA;gBAEA,0DAAA,GACA,IAAI;oBACF,IAAIwJ,YAAY,EAAE;wBAChB8C,WAAW,CAACwB,cAAc,CAACtE,YAAY,EAAEU,IAAI,EAAElK,KAAK,CAAC,CAAA;oBACvD,CAAC,MAAM;wBACL,mFAAA,GACAsM,WAAW,CAAC/B,YAAY,CAACL,IAAI,EAAElK,KAAK,CAAC,CAAA;oBACvC,CAAA;oBAEA,IAAI6L,YAAY,CAACS,WAAW,CAAC,EAAE;wBAC7B3C,YAAY,CAAC2C,WAAW,CAAC,CAAA;oBAC3B,CAAC,MAAM;wBACLrP,QAAQ,CAACwF,SAAS,CAACI,OAAO,CAAC,CAAA;oBAC7B,CAAA;gBACF,CAAC,CAAC,OAAOP,CAAC,EAAE,CAAA,CAAC;YACf,CAAA;YAEA,6BAAA,GACA8J,YAAY,CAAC,yBAAyB,EAAEE,WAAW,EAAE,IAAI,CAAC,CAAA;SAC3D,CAAA;QAED;;;;KAIF,GACE,IAAMyB,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAaC,QAAQ,EAAE;YAC7C,IAAIC,UAAU,CAAA;YACd,IAAMC,cAAc,GAAG3C,eAAe,CAACyC,QAAQ,CAAC,CAAA;YAEhD,6BAAA,GACA5B,YAAY,CAAC,yBAAyB,EAAE4B,QAAQ,EAAE,IAAI,CAAC,CAAA;YAEvD,MAAQC,UAAU,GAAGC,cAAc,CAACC,QAAQ,EAAE,CAAG;gBAC/C,6BAAA,GACA/B,YAAY,CAAC,wBAAwB,EAAE6B,UAAU,EAAE,IAAI,CAAC,CAAA;gBACxD,8BAAA,GACAxB,iBAAiB,CAACwB,UAAU,CAAC,CAAA;gBAE7B,yBAAA,GACAb,mBAAmB,CAACa,UAAU,CAAC,CAAA;gBAE/B,4BAAA,GACA,IAAIA,UAAU,CAAC/J,OAAO,YAAYjB,gBAAgB,EAAE;oBAClD8K,kBAAkB,CAACE,UAAU,CAAC/J,OAAO,CAAC,CAAA;gBACxC,CAAA;YACF,CAAA;YAEA,6BAAA,GACAkI,YAAY,CAAC,wBAAwB,EAAE4B,QAAQ,EAAE,IAAI,CAAC,CAAA;SACvD,CAAA;QAED;;;;;;KAMF,GACE,sCAAA;QACAvL,SAAS,CAAC2L,QAAQ,GAAG,SAAU3D,KAAK,EAAY;YAAA,IAAVjC,GAAG,GAAA7J,SAAA,CAAAC,MAAA,GAAA,CAAA,IAAAD,SAAA,CAAA,CAAA,CAAA,KAAA6D,SAAA,GAAA7D,SAAA,CAAA,CAAA,CAAA,GAAG,CAAA,CAAE,CAAA;YAC5C,IAAIuM,IAAI,CAAA;YACR,IAAImD,YAAY,CAAA;YAChB,IAAI/B,WAAW,CAAA;YACf,IAAIgC,OAAO,CAAA;YACX,IAAIC,UAAU,CAAA;YACd;;iEAEJ,GACI3G,cAAc,GAAG,CAAC6C,KAAK,CAAA;YACvB,IAAI7C,cAAc,EAAE;gBAClB6C,KAAK,GAAG,OAAO,CAAA;YACjB,CAAA;YAEA,yCAAA,GACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC0B,OAAO,CAAC1B,KAAK,CAAC,EAAE;gBAChD,IAAI,OAAOA,KAAK,CAAChN,QAAQ,KAAK,UAAU,EAAE;oBACxCgN,KAAK,GAAGA,KAAK,CAAChN,QAAQ,EAAE,CAAA;oBACxB,IAAI,OAAOgN,KAAK,KAAK,QAAQ,EAAE;wBAC7B,MAAMpM,eAAe,CAAC,iCAAiC,CAAC,CAAA;oBAC1D,CAAA;gBACF,CAAC,MAAM;oBACL,MAAMA,eAAe,CAAC,4BAA4B,CAAC,CAAA;gBACrD,CAAA;YACF,CAAA;YAEA,mDAAA,GACA,IAAI,CAACoE,SAAS,CAACM,WAAW,EAAE;gBAC1B,IACEpB,OAAA,CAAOJ,MAAM,EAACiN,YAAY,CAAK,KAAA,QAAQ,IACvC,OAAOjN,MAAM,EAACiN,YAAY,KAAK,UAAU,EACzC;oBACA,IAAI,OAAO/D,KAAK,KAAK,QAAQ,EAAE;wBAC7B,OAAOlJ,MAAM,EAACiN,YAAY,CAAC/D,KAAK,CAAC,CAAA;oBACnC,CAAA;oBAEA,IAAI0B,OAAO,CAAC1B,KAAK,CAAC,EAAE;wBAClB,OAAOlJ,MAAM,EAACiN,YAAY,CAAC/D,KAAK,CAACV,SAAS,CAAC,CAAA;oBAC7C,CAAA;gBACF,CAAA;gBAEA,OAAOU,KAAK,CAAA;YACd,CAAA;YAEA,sBAAA,GACA,IAAI,CAAClE,UAAU,EAAE;gBACfgC,YAAY,CAACC,GAAG,CAAC,CAAA;YACnB,CAAA;YAEA,6BAAA,GACA/F,SAAS,CAACI,OAAO,GAAG,EAAE,CAAA;YAEtB,kDAAA,GACA,IAAI,OAAO4H,KAAK,KAAK,QAAQ,EAAE;gBAC7BzD,QAAQ,GAAG,KAAK,CAAA;YAClB,CAAA;YAEA,IAAIA,QAAQ,EAAE;gBACZ,6DAAA,GACA,IAAIyD,KAAK,CAACsB,QAAQ,EAAE;oBAClB,IAAMxC,OAAO,GAAGpK,iBAAiB,CAACsL,KAAK,CAACsB,QAAQ,CAAC,CAAA;oBACjD,IAAI,CAAC/G,YAAY,CAACuE,OAAO,CAAC,IAAIzD,WAAW,CAACyD,OAAO,CAAC,EAAE;wBAClD,MAAMlL,eAAe,CACnB,yDACF,CAAC,CAAA;oBACH,CAAA;gBACF,CAAA;YACF,CAAC,MAAM,IAAIoM,KAAK,YAAYtH,IAAI,EAAE;gBAChC;iDACN,GACM+H,IAAI,GAAGV,aAAa,CAAC,SAAS,CAAC,CAAA;gBAC/B6D,YAAY,GAAGnD,IAAI,CAAC/G,aAAa,CAACQ,UAAU,CAAC8F,KAAK,EAAE,IAAI,CAAC,CAAA;gBACzD,IAAI4D,YAAY,CAACvL,QAAQ,KAAK,CAAC,IAAIuL,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAAE;oBACnE,qCAAA,GACAb,IAAI,GAAGmD,YAAY,CAAA;gBACrB,CAAC,MAAM,IAAIA,YAAY,CAACtC,QAAQ,KAAK,MAAM,EAAE;oBAC3Cb,IAAI,GAAGmD,YAAY,CAAA;gBACrB,CAAC,MAAM;oBACL,0DAAA;oBACAnD,IAAI,CAACuD,WAAW,CAACJ,YAAY,CAAC,CAAA;gBAChC,CAAA;YACF,CAAC,MAAM;gBACL,0CAAA,GACA,IACE,CAAC5H,UAAU,IACX,CAACL,kBAAkB,IACnB,CAACE,cAAc,IACf,mDAAA;gBACAmE,KAAK,CAAC1M,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EACzB;oBACA,OAAOqG,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAACsI,KAAK,CAAC,GACpCA,KAAK,CAAA;gBACX,CAAA;gBAEA,sCAAA,GACAS,IAAI,GAAGV,aAAa,CAACC,KAAK,CAAC,CAAA;gBAE3B,0CAAA,GACA,IAAI,CAACS,IAAI,EAAE;oBACT,OAAOzE,UAAU,GAAG,IAAI,GAAGE,mBAAmB,GAAGtC,SAAS,GAAG,EAAE,CAAA;gBACjE,CAAA;YACF,CAAA;YAEA,yDAAA,GACA,IAAI6G,IAAI,IAAI1E,UAAU,EAAE;gBACtBmD,YAAY,CAACuB,IAAI,CAACwD,UAAU,CAAC,CAAA;YAC/B,CAAA;YAEA,qBAAA,GACA,IAAMC,YAAY,GAAGpD,eAAe,CAACvE,QAAQ,GAAGyD,KAAK,GAAGS,IAAI,CAAC,CAAA;YAE7D,iDAAA,GACA,MAAQoB,WAAW,GAAGqC,YAAY,CAACR,QAAQ,EAAE,CAAG;gBAC9C,4DAAA,GACA,IAAI7B,WAAW,CAACxJ,QAAQ,KAAK,CAAC,IAAIwJ,WAAW,KAAKgC,OAAO,EAAE;oBACzD,SAAA;gBACF,CAAA;gBAEA,8BAAA,GACA7B,iBAAiB,CAACH,WAAW,CAAC,CAAA;gBAE9B,yBAAA,GACAc,mBAAmB,CAACd,WAAW,CAAC,CAAA;gBAEhC,oCAAA,GACA,IAAIA,WAAW,CAACpI,OAAO,YAAYjB,gBAAgB,EAAE;oBACnD8K,kBAAkB,CAACzB,WAAW,CAACpI,OAAO,CAAC,CAAA;gBACzC,CAAA;gBAEAoK,OAAO,GAAGhC,WAAW,CAAA;YACvB,CAAA;YAEAgC,OAAO,GAAG,IAAI,CAAA;YAEd,gDAAA,GACA,IAAItH,QAAQ,EAAE;gBACZ,OAAOyD,KAAK,CAAA;YACd,CAAA;YAEA,kCAAA,GACA,IAAIhE,UAAU,EAAE;gBACd,IAAIC,mBAAmB,EAAE;oBACvB6H,UAAU,GAAG9J,sBAAsB,CAAC6G,IAAI,CAACJ,IAAI,CAAC/G,aAAa,CAAC,CAAA;oBAE5D,MAAO+G,IAAI,CAACwD,UAAU,CAAE;wBACtB,0DAAA;wBACAH,UAAU,CAACE,WAAW,CAACvD,IAAI,CAACwD,UAAU,CAAC,CAAA;oBACzC,CAAA;gBACF,CAAC,MAAM;oBACLH,UAAU,GAAGrD,IAAI,CAAA;gBACnB,CAAA;gBAEA,IAAI9F,YAAY,CAACwJ,UAAU,IAAIxJ,YAAY,CAACyJ,aAAa,EAAE;oBACzD;;;;;;UAMR,GACQN,UAAU,GAAG5J,UAAU,CAAC2G,IAAI,CAACtI,gBAAgB,EAAEuL,UAAU,EAAE,IAAI,CAAC,CAAA;gBAClE,CAAA;gBAEA,OAAOA,UAAU,CAAA;YACnB,CAAA;YAEA,IAAIO,cAAc,GAAGxI,cAAc,GAAG4E,IAAI,CAACnB,SAAS,GAAGmB,IAAI,CAACD,SAAS,CAAA;YAErE,gCAAA,GACA,IACE3E,cAAc,IACdtB,YAAY,CAAC,UAAU,CAAC,IACxBkG,IAAI,CAAC/G,aAAa,IAClB+G,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,IAC1B7D,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,IAC/BhM,UAAU,CAAC6G,YAAwB,EAAEmG,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,CAAC,EACrE;gBACA4E,cAAc,GACZ,YAAY,GAAG5D,IAAI,CAAC/G,aAAa,CAAC4K,OAAO,CAAC7E,IAAI,GAAG,KAAK,GAAG4E,cAAc,CAAA;YAC3E,CAAA;YAEA,uCAAA,GACA,IAAI1I,kBAAkB,EAAE;gBACtB0I,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAElO,eAAa,EAAE,GAAG,CAAC,CAAA;gBAClEkO,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAEjO,UAAQ,EAAE,GAAG,CAAC,CAAA;gBAC7DiO,cAAc,GAAGlR,aAAa,CAACkR,cAAc,EAAEhO,aAAW,EAAE,GAAG,CAAC,CAAA;YAClE,CAAA;YAEA,OAAOsD,kBAAkB,IAAIuC,mBAAmB,GAC5CvC,kBAAkB,CAACjC,UAAU,CAAC2M,cAAc,CAAC,GAC7CA,cAAc,CAAA;SACnB,CAAA;QAED;;;;;KAKF,GACErM,SAAS,CAACuM,SAAS,GAAG,SAAUxG,GAAG,EAAE;YACnCD,YAAY,CAACC,GAAG,CAAC,CAAA;YACjBjC,UAAU,GAAG,IAAI,CAAA;SAClB,CAAA;QAED;;;;KAIF,GACE9D,SAAS,CAACwM,WAAW,GAAG,YAAY;YAClC/G,MAAM,GAAG,IAAI,CAAA;YACb3B,UAAU,GAAG,KAAK,CAAA;SACnB,CAAA;QAED;;;;;;;;;KASF,GACE9D,SAAS,CAACyM,gBAAgB,GAAG,SAAUC,GAAG,EAAE9B,IAAI,EAAErN,KAAK,EAAE;YACvD,+CAAA,GACA,IAAI,CAACkI,MAAM,EAAE;gBACXK,YAAY,CAAC,CAAA,CAAE,CAAC,CAAA;YAClB,CAAA;YAEA,IAAM2E,KAAK,GAAG/N,iBAAiB,CAACgQ,GAAG,CAAC,CAAA;YACpC,IAAMhC,MAAM,GAAGhO,iBAAiB,CAACkO,IAAI,CAAC,CAAA;YACtC,OAAOJ,iBAAiB,CAACC,KAAK,EAAEC,MAAM,EAAEnN,KAAK,CAAC,CAAA;SAC/C,CAAA;QAED;;;;;;KAMF,GACEyC,SAAS,CAAC2M,OAAO,GAAG,SAAU/C,UAAU,EAAEgD,YAAY,EAAE;YACtD,IAAI,OAAOA,YAAY,KAAK,UAAU,EAAE;gBACtC,OAAA;YACF,CAAA;YAEAxK,KAAK,CAACwH,UAAU,CAAC,GAAGxH,KAAK,CAACwH,UAAU,CAAC,IAAI,EAAE,CAAA;YAC3ClP,SAAS,CAAC0H,KAAK,CAACwH,UAAU,CAAC,EAAEgD,YAAY,CAAC,CAAA;SAC3C,CAAA;QAED;;;;;;;KAOF,GACE5M,SAAS,CAAC6M,UAAU,GAAG,SAAUjD,UAAU,EAAE;YAC3C,IAAIxH,KAAK,CAACwH,UAAU,CAAC,EAAE;gBACrB,OAAOpP,QAAQ,CAAC4H,KAAK,CAACwH,UAAU,CAAC,CAAC,CAAA;YACpC,CAAA;SACD,CAAA;QAED;;;;;KAKF,GACE5J,SAAS,CAAC8M,WAAW,GAAG,SAAUlD,UAAU,EAAE;YAC5C,IAAIxH,KAAK,CAACwH,UAAU,CAAC,EAAE;gBACrBxH,KAAK,CAACwH,UAAU,CAAC,GAAG,EAAE,CAAA;YACxB,CAAA;SACD,CAAA;QAED;;;;KAIF,GACE5J,SAAS,CAAC+M,cAAc,GAAG,YAAY;YACrC3K,KAAK,GAAG,CAAA,CAAE,CAAA;SACX,CAAA;QAED,OAAOpC,SAAS,CAAA;IAClB,CAAA;IAEA,IAAA,SAAeF,eAAe,EAAE", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}]}