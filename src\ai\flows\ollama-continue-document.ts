import { ollamaService } from '../ollama-service';

export interface ContinueDocumentInput {
  topic: string;
  writingStyle: string;
  context: string;
  pagesToGenerate: number;
  culturalInflections: {
    american: number;
    russian: number;
    german: number;
    japanese: number;
    french: number;
  };
}

export interface ContinueDocumentOutput {
  content: string;
}

const masterPrompt = `Master System Prompt: V2 with Modular Cultural Inflection
[Instruction to LLM]: This is the updated protocol for generating text. The system has been upgraded to separate the core WRITING_STYLE from a new, independent CULTURAL_INFLECTION layer. You must now process requests by first applying the rules for the chosen WRITING_STYLE, and then filtering that output through the specified CULTURAL_INFLECTION lens(es).

Part 1: Writing Style Definitions (The "What")
This section defines the fundamental structure and purpose of the text. Adhere to the selected style's CORE_OBJECTIVE, TONE, STRUCTURE, and FOCUS.

[1] Analytical
CORE_OBJECTIVE: To dissect a topic and explain its internal logic.
TONE: Formal, objective, intellectual, detached.
STRUCTURE: Introduction → Analysis → Conclusion. Heavy use of subheadings and logical divisions.
FOCUS: Breaking down complex ideas into component parts and examining relationships.

[2] Instructional
CORE_OBJECTIVE: To teach the reader how to do something or understand a process.
TONE: Clear, direct, helpful, authoritative but approachable.
STRUCTURE: Step-by-step progression. Use of numbered lists, bullet points, and clear transitions.
FOCUS: Practical application and comprehension. Anticipating reader questions and obstacles.

[3] Reporting
CORE_OBJECTIVE: To present factual information in an organized, neutral manner.
TONE: Objective, professional, informative, unbiased.
STRUCTURE: Who/What/When/Where/Why/How format. Chronological or thematic organization.
FOCUS: Accuracy, completeness, and clarity of information presentation.

[4] Argumentative / Persuasive
CORE_OBJECTIVE: To convince the reader of a particular viewpoint or course of action.
TONE: Confident, logical, passionate but controlled, evidence-based.
STRUCTURE: Thesis → Evidence → Counter-argument acknowledgment → Refutation → Conclusion.
FOCUS: Building a compelling case through logic, evidence, and emotional appeal.

[5] Narrative
CORE_OBJECTIVE: To tell a story or recount events in an engaging manner.
TONE: Engaging, descriptive, personal, emotionally resonant.
STRUCTURE: Beginning → Middle → End. Character development and plot progression.
FOCUS: Creating vivid scenes, developing characters, and maintaining reader engagement.

[6] Exploratory / Reflective
CORE_OBJECTIVE: To examine ideas, experiences, or concepts through personal reflection.
TONE: Thoughtful, introspective, questioning, open-minded.
STRUCTURE: Question → Exploration → Insight. Non-linear, associative thinking patterns.
FOCUS: Personal growth, understanding, and meaning-making through reflection.

[7] Comparative
CORE_OBJECTIVE: To examine similarities and differences between two or more subjects.
TONE: Balanced, analytical, fair, systematic.
STRUCTURE: Point-by-point or subject-by-subject comparison. Clear criteria for comparison.
FOCUS: Highlighting relationships, contrasts, and relative merits of subjects.

[8] Schematic / Referential
CORE_OBJECTIVE: To organize and present information in a systematic, reference-friendly format.
TONE: Precise, organized, comprehensive, authoritative.
STRUCTURE: Hierarchical organization. Heavy use of headings, subheadings, and cross-references.
FOCUS: Creating a comprehensive resource that can be easily navigated and referenced.

Part 2: Cultural Inflection Definitions (The "How")
This section defines the communication style and cultural lens through which the writing style is expressed.

[A] American
COMMUNICATION_STYLE: Direct, optimistic, pragmatic, results-oriented.
CULTURAL_LENS: Emphasis on individual achievement, innovation, and forward progress.
LINGUISTIC_MARKERS: Active voice, confident assertions, solution-focused language.

[B] Russian
COMMUNICATION_STYLE: Philosophical, thorough, historically-conscious, intellectually rigorous.
CULTURAL_LENS: Deep consideration of context, precedent, and long-term implications.
LINGUISTIC_MARKERS: Complex sentence structures, historical references, theoretical frameworks.

[C] German
COMMUNICATION_STYLE: Systematic, precise, methodical, technically detailed.
CULTURAL_LENS: Process-oriented, risk-aware, quality-focused, engineering mindset.
LINGUISTIC_MARKERS: Detailed explanations, systematic organization, technical precision.

[D] Japanese
COMMUNICATION_STYLE: Respectful, consensus-building, harmony-seeking, indirect.
CULTURAL_LENS: Group consideration, long-term thinking, respect for tradition and hierarchy.
LINGUISTIC_MARKERS: Polite language, consideration of multiple perspectives, gradual revelation.

[E] French
COMMUNICATION_STYLE: Elegant, intellectually sophisticated, artistically aware, debate-oriented.
CULTURAL_LENS: Appreciation for nuance, style, and intellectual discourse.
LINGUISTIC_MARKERS: Sophisticated vocabulary, rhetorical flourishes, cultural references.

Part 3: Integration Protocol
When a user makes a selection in the app, you will receive a prompt structured like this. Your task is to synthesize all parts into a single, coherent output.

--- USER REQUEST ---
WRITING_STYLE: [4] Argumentative / Persuasive
CULTURAL_INFLECTION_1: [A] American, 50%
CULTURAL_INFLECTION_2: [C] German, 50%
CULTURAL_INFLECTION_3: [B] Russian, Off
CULTURAL_INFLECTION_4: [D] Japanese, Off
CULTURAL_INFLECTION_5: [E] French, Off
TOPIC: The necessity of adopting nuclear fusion for energy independence.

--- YOUR REQUIRED BEHAVIOR ---
Adopt the Core Style: Argumentative. Start with a thesis, use evidence, refute counter-arguments.
Blend the Cultures:
Use the German focus on technical precision, risk analysis, and process to build the core evidence.
Use the American tone of optimism, pragmatism, and a strong, confident call-to-action to frame the argument and conclusion.
Produce the Text: The resulting text should be a logically rigorous and exhaustive argument (German) presented in a confident, forward-looking, and persuasive manner (American).`;

export async function continueDocument(input: ContinueDocumentInput): Promise<ContinueDocumentOutput> {
  const styleMapping: {[key: string]: string} = {
    "Argumentative": "Argumentative / Persuasive",
    "Exploratory": "Exploratory / Reflective",
    "Schematic": "Schematic / Referential"
  };
  
  const mappedStyle = styleMapping[input.writingStyle] || input.writingStyle;
  
  // Build cultural inflections string
  const culturalInflections = Object.entries(input.culturalInflections)
    .filter(([_, value]) => value > 0)
    .map(([key, value]) => {
      const culturalMap: {[key: string]: string} = {
        american: '[A] American',
        russian: '[B] Russian', 
        german: '[C] German',
        japanese: '[D] Japanese',
        french: '[E] French'
      };
      return `${culturalMap[key]}, ${value}%`;
    })
    .join('\n');

  const prompt = `${masterPrompt}

---

Now, continue generating the document based on the following request.

EXECUTION INSTRUCTIONS
* You are continuing a document that is already in progress. Use the following text as the context from the previous page to ensure a seamless transition: ${input.context}
* Generate content for ${input.pagesToGenerate} additional pages, with each page being approximately 240 words.
* Separate each new page of content with the marker "[PAGEBREAK]".
* Use four spaces for the first-line indentation of each new paragraph.
* DO NOT include a title, author information, or page numbers in your output.
* Your response must be only the body of the paper.
* Maintain the established tone, style, and cultural inflections.

--- USER REQUEST ---
WRITING_STYLE: ${mappedStyle}
${culturalInflections}
TOPIC: ${input.topic}

Generate the content now:`;

  try {
    const content = await ollamaService.generateText(prompt);
    return { content };
  } catch (error) {
    console.error('Error in continueDocument:', error);
    throw error;
  }
}
