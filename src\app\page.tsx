
"use client";

import React, { useState, useTransition, useRef, useEffect, useCallback } from "react";
import {
  Feather,
  FilePlus2,
  History,
  Settings2,
  Download,
  Sparkles,
  LoaderCircle,
  User,
  Bot,
  ArrowUp,
  Wand2,
  Save,
  FileDown,
} from "lucide-react";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/scroll-area";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import type { ComposeDocumentInput } from "@/ai/flows/ollama-compose-document";
import { composeDocument } from "@/ai/flows/ollama-compose-document";
import type { ContinueDocumentInput } from "@/ai/flows/ollama-continue-document";
import { continueDocument } from "@/ai/flows/ollama-continue-document";
import { ollamaService } from "@/ai/ollama-service";
import { EnhancedSidebar, type UploadedDocument } from "@/components/enhanced-sidebar";
import { documentUploadService } from "@/lib/document-upload";
import { documentExportService } from "@/lib/document-export";
import { format as formatDate } from 'date-fns';
import { Slider } from "@/components/ui/slider";

type Message = {
  role: "user" | "assistant";
  content: string;
};

type Settings = {
  writingStyle: string;
  pageCount: number;
  humanization: boolean;
  studentName: string;
  professorName: string;
  courseDetails: string;
  citationStyle: "MLA" | "APA";
  culturalInflections: {
    american: number;
    russian: number;
    german: number;
    japanese: number;
    french: number;
  };
};

type DocumentRecord = {
  id: string;
  topic: string;
  content: string;
  settings: Settings;
  timestamp: Date;
};

const writingStyles = [
  "Analytical", "Instructional", "Reporting", "Argumentative", "Exploratory", "Descriptive", "Narrative", "Schematic",
];

const cultures: Array<keyof Settings['culturalInflections']> = [
  'american',
  'russian',
  'german',
  'japanese',
  'french',
];


// Old LeftSidebar component removed - replaced with EnhancedSidebar

// ExportDropdown removed - export functionality moved to EnhancedSidebar

const SettingsPanel = ({ settings, setSettings, disabled, onSave }: { settings: Settings, setSettings: React.Dispatch<React.SetStateAction<Settings>>, disabled: boolean, onSave: () => void }) => (
  <Card className="h-full">
    <CardHeader>
      <CardTitle className="flex items-center gap-2 font-headline">
        <Settings2 /> Settings
      </CardTitle>
    </CardHeader>
    <CardContent className="grid grid-cols-2 gap-x-4 gap-y-6">
      <div className="space-y-2 col-span-2 sm:col-span-1">
        <Label htmlFor="writing-style">Writing Style</Label>
        <Select value={settings.writingStyle} onValueChange={(value) => setSettings(s => ({ ...s, writingStyle: value }))} disabled={disabled}>
          <SelectTrigger id="writing-style"><SelectValue placeholder="Select style" /></SelectTrigger>
          <SelectContent>{writingStyles.map(style => <SelectItem key={style} value={style}>{style}</SelectItem>)}</SelectContent>
        </Select>
      </div>
      <div className="space-y-2 col-span-2 sm:col-span-1">
        <Label htmlFor="pages">Pages</Label>
        <Input id="pages" type="number" min="1" max="1500" value={settings.pageCount} onChange={(e) => setSettings(s => ({ ...s, pageCount: Math.min(1500, Math.max(1, parseInt(e.target.value, 10) || 1)) }))} disabled={disabled}/>
        <p className="text-sm text-muted-foreground">Approx. {settings.pageCount * 250} words</p>
      </div>
      <div className="space-y-2 col-span-2">
        <Label htmlFor="humanization" className="flex items-center gap-2">
          Humanization <Sparkles className="text-primary" />
        </Label>
        <div className="flex items-center space-x-2 h-10">
          <Switch id="humanization" checked={settings.humanization} onCheckedChange={(checked) => setSettings(s => ({ ...s, humanization: checked }))} disabled={disabled} />
          <Label htmlFor="humanization" className="text-sm text-muted-foreground">Improve realism</Label>
        </div>
      </div>

      <div className="col-span-2 border-t pt-4 mt-2">
        <h3 className="font-semibold text-lg mb-2 font-headline">Cultural Inflection</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Blend cultural communication styles. Set to 0% to turn off.
        </p>
      </div>
      
      {cultures.map((key) => (
        <div key={key} className="col-span-2 sm:col-span-1 space-y-2">
          <div className="flex justify-between items-center">
              <Label htmlFor={`inflection-${key}`} className="capitalize">{key}</Label>
              <span className="text-sm text-muted-foreground">{settings.culturalInflections[key]}%</span>
          </div>
          <Slider
              id={`inflection-${key}`}
              min={0}
              max={100}
              step={10}
              value={[settings.culturalInflections[key]]}
              onValueChange={([value]) => setSettings(s => ({
                  ...s,
                  culturalInflections: { ...s.culturalInflections, [key]: value }
              }))}
              disabled={disabled}
          />
        </div>
      ))}
      
      <div className="col-span-2 border-t pt-4 mt-2">
        <h3 className="font-semibold text-lg mb-2 font-headline">Academic Formatting</h3>
      </div>
       <div className="space-y-2">
        <Label htmlFor="student-name">Student Name</Label>
        <Input id="student-name" value={settings.studentName} onChange={(e) => setSettings(s => ({...s, studentName: e.target.value}))} disabled={disabled} placeholder="John Doe" />
      </div>
       <div className="space-y-2">
        <Label htmlFor="professor-name">Professor Name</Label>
        <Input id="professor-name" value={settings.professorName} onChange={(e) => setSettings(s => ({...s, professorName: e.target.value}))} disabled={disabled} placeholder="Dr. Smith" />
      </div>
       <div className="space-y-2">
        <Label htmlFor="course-details">Course Details</Label>
        <Input id="course-details" value={settings.courseDetails} onChange={(e) => setSettings(s => ({...s, courseDetails: e.target.value}))} disabled={disabled} placeholder="ENGL 101" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="citation-style">Citation Style</Label>
        <Select value={settings.citationStyle} onValueChange={(value) => setSettings(s => ({ ...s, citationStyle: value as Settings['citationStyle'] }))} disabled={disabled}>
          <SelectTrigger id="citation-style"><SelectValue /></SelectTrigger>
          <SelectContent>
            <SelectItem value="MLA">MLA</SelectItem>
            <SelectItem value="APA">APA</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="col-span-2 flex justify-end">
        <Button onClick={onSave} disabled={disabled}><Save className="mr-2" /> Save Settings</Button>
      </div>
    </CardContent>
  </Card>
);

const DocumentPreview = ({ content, topic, settings }: { content: string, topic: string, settings: Settings }) => {
  const pages = content ? content.split('[PAGEBREAK]') : [];
  
  if (!content) {
    return (
      <div className="text-center text-muted-foreground h-full flex flex-col justify-center items-center py-20">
        <Feather size={48} className="mb-4 text-primary/50" />
        <h3 className="font-semibold text-lg text-foreground">No Document Generated</h3>
        <p>Use the chat to generate your document.</p>
      </div>
    );
  }

  const studentLastName = settings.studentName.split(' ').pop() || '';
  const shortTitle = topic.split(' ').slice(0, 5).join(' ').toUpperCase();
  
  return (
    <div className="p-4 sm:p-8">
      {pages.map((page, index) => (
        <div key={index} className="relative bg-white text-black shadow-lg p-12 mb-8 font-serif leading-relaxed text-[12pt]" style={{width: '8.5in', minHeight: '11in'}}>
          <div className="absolute top-[0.5in] right-[1in] text-[12pt]">
            {settings.citationStyle === 'MLA' ? `${studentLastName} ${index + 1}` : `${shortTitle} ${index + 1}`}
          </div>

          {index === 0 && (
            <>
              <pre className="whitespace-pre-wrap font-inherit">{settings.studentName}{"\n"}{settings.professorName}{"\n"}{settings.courseDetails}{"\n"}{formatDate(new Date(), 'dd MMMM yyyy')}</pre>
              <h1 className="text-center font-bold my-6 text-[14pt]">{topic}</h1>
            </>
          )}
          <pre className="whitespace-pre-wrap font-inherit">{page.trim()}</pre>
        </div>
      ))}
    </div>
  );
};

const ChatView = ({ messages, chatInput, setChatInput, handleSend, isLoading }: { messages: Message[], chatInput: string, setChatInput: (val: string) => void, handleSend: () => void, isLoading: boolean }) => {
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (scrollAreaRef.current) {
      const viewport = scrollAreaRef.current.querySelector('div[data-radix-scroll-area-viewport]');
      if (viewport) viewport.scrollTop = viewport.scrollHeight;
    }
  }, [messages]);

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (!isLoading) handleSend();
    }
  };

  return (
    <main className="flex flex-col h-full bg-background/80 p-4">
      <ScrollArea className="flex-1 -mx-4" ref={scrollAreaRef}>
        <div className="px-4 pb-4">
          {messages.length === 0 ? (
            <div className="text-center text-muted-foreground h-full flex flex-col justify-center items-center pt-20">
               <h2 className="text-3xl font-headline text-foreground">Welcome to ASCAES</h2>
               <p className="mt-2 max-w-md">Describe the topic for your document below to get started.</p>
            </div>
          ) : (
            <div className="space-y-6">
              {messages.map((msg, idx) => (
                <div key={idx} className={`flex items-start gap-4 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                  {msg.role === 'assistant' && <div className="p-2 rounded-full bg-primary/20 text-primary"><Bot /></div>}
                  <div className={`rounded-lg p-4 max-w-xl ${msg.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-card'}`}>
                    <p className="whitespace-pre-wrap">{msg.content}</p>
                  </div>
                  {msg.role === 'user' && <div className="p-2 rounded-full bg-accent/20 text-accent-foreground"><User /></div>}
                </div>
              ))}
              {isLoading && messages.length > 0 && messages[messages.length-1].role === 'user' && (
                <div className="flex items-start gap-4">
                  <div className="p-2 rounded-full bg-primary/20 text-primary"><Bot /></div>
                  <div className="rounded-lg p-4 bg-card flex items-center space-x-2">
                    <LoaderCircle className="animate-spin" />
                    <span>Generating...</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </ScrollArea>
      <div className="mt-4 relative">
        <Textarea value={chatInput} onChange={(e) => setChatInput(e.target.value)} onKeyDown={handleKeyDown} placeholder="e.g., 'A research paper on the impact of climate change on marine biodiversity...'" className="pr-20 py-3" rows={2} disabled={isLoading}/>
        <Button size="icon" className="absolute right-3 top-1/2 -translate-y-1/2" onClick={handleSend} disabled={isLoading || !chatInput.trim()}><ArrowUp /></Button>
      </div>
    </main>
  );
};

export function useExportHandler() {
    const { toast } = useToast();

    const handleExport = useCallback(async (exportFormat: 'PDF' | 'LaTeX' | 'RTF' | 'TXT', doc: DocumentRecord) => {
        const { content, topic, settings } = doc;
        toast({ title: `Exporting as ${exportFormat}...`, description: "Please wait a moment." });

        if (exportFormat === 'PDF') {
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.width = '8.5in';
            document.body.appendChild(tempContainer);

            const pdf = new jsPDF('p', 'in', 'letter');
            const pages = content.split('[PAGEBREAK]');
            const studentLastName = settings.studentName.split(' ').pop() || '';
            const shortTitle = topic.split(' ').slice(0, 5).join(' ').toUpperCase();

            for (let i = 0; i < pages.length; i++) {
                const pageElement = document.createElement('div');
                pageElement.className = "relative bg-white text-black font-serif leading-relaxed text-[12pt]";
                pageElement.style.width = '8.5in';
                pageElement.style.minHeight = '11in';
                pageElement.style.padding = '1in';
                pageElement.style.boxSizing = 'border-box';

                let pageHTML = `
                    <div style="position: absolute; top: 0.5in; right: 1in; font-size: 12pt;">
                        ${settings.citationStyle === 'MLA' ? `${studentLastName} ${i + 1}` : `${shortTitle} ${i + 1}`}
                    </div>
                `;
                if (i === 0) {
                    pageHTML += `
                        <pre style="white-space: pre-wrap; font-family: inherit;">${settings.studentName}\n${settings.professorName}\n${settings.courseDetails}\n${formatDate(new Date(doc.timestamp), 'dd MMMM yyyy')}</pre>
                        <h1 style="text-align: center; font-weight: bold; margin: 24px 0; font-size: 14pt;">${topic}</h1>
                    `;
                }
                pageHTML += `<pre style="white-space: pre-wrap; font-family: inherit;">${pages[i].trim()}</pre>`;
                pageElement.innerHTML = pageHTML;
                tempContainer.appendChild(pageElement);

                try {
                    const canvas = await html2canvas(pageElement, { scale: 2 });
                    const imgData = canvas.toDataURL('image/png');

                    if (i > 0) pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, 0, 8.5, 11);
                } catch (error) {
                    console.error("Error generating canvas for page", i, error);
                    toast({ variant: "destructive", title: "PDF Export Failed", description: "Could not render a page." });
                    document.body.removeChild(tempContainer);
                    return;
                }
                
                tempContainer.removeChild(pageElement);
            }

            document.body.removeChild(tempContainer);
            pdf.save(`${topic.slice(0, 20)}.pdf`);
            toast({ title: "Export Successful", description: `Downloaded ${topic.slice(0, 20)}.pdf` });
        } else {
            const fileExtension = { 'LaTeX': 'tex', 'RTF': 'rtf', 'TXT': 'txt' }[exportFormat];
            const blob = new Blob([content.replace(/\[PAGEBREAK\]/g, '\n\n---\n\n')], { type: 'text/plain;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `ascaes-document.${fileExtension}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
    }, [toast]);
    
    return handleExport;
}


export default function ASCAESPage() {
  const [settings, setSettings] = useState<Settings>({
    writingStyle: "Analytical",
    pageCount: 10, // Changed to 10 pages per batch
    humanization: false,
    studentName: "John Doe",
    professorName: "Professor Smith",
    courseDetails: "ENGL 101",
    citationStyle: "MLA",
    culturalInflections: {
      american: 100,
      russian: 0,
      german: 0,
      japanese: 0,
      french: 0,
    },
  });
  const [chatInput, setChatInput] = useState("");
  const [chatHistory, setChatHistory] = useState<Message[]>([]);
  const [generatedContent, setGeneratedContent] = useState("");
  const [isGenerating, startTransition] = useTransition();
  const { toast } = useToast();
  const [currentTopic, setCurrentTopic] = useState("");
  const [documentHistory, setDocumentHistory] = useState<DocumentRecord[]>([]);
  const [uploadedDocuments, setUploadedDocuments] = useState<UploadedDocument[]>([]);
  const [currentModel, setCurrentModel] = useState("llama3.2:3b");

  // Initialize document upload service and load uploaded documents
  useEffect(() => {
    documentUploadService.init();
    setUploadedDocuments(documentUploadService.getUploadedDocuments());
  }, []);

  const handleGenerate = useCallback(() => {
    if (!chatInput.trim()) return;

    const topic = chatInput.trim().replace(/^paper on /i, '').replace(/ in /i, ' ').split(' ').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    setCurrentTopic(topic);
    const userMessage: Message = { role: "user", content: chatInput };
    setChatHistory([userMessage]);
    setGeneratedContent("");
    setChatInput("");

    startTransition(async () => {
      let fullContent = "";
      try {
        // Check if Ollama is connected
        const isConnected = await ollamaService.checkConnection();
        if (!isConnected) {
          throw new Error("Cannot connect to Ollama. Please make sure Ollama is running on localhost:11434");
        }

        // Set the current model
        ollamaService.setCurrentModel(currentModel);

        setChatHistory(prev => [...prev, { role: 'assistant', content: `Generating page 1 of ${settings.pageCount} using ${currentModel}...` }]);
        const composeInput: ComposeDocumentInput = { topic, writingStyle: settings.writingStyle, culturalInflections: settings.culturalInflections };
        const result = await composeDocument(composeInput);
        if (!result.content || result.content.trim() === '') throw new Error("The AI returned an empty response for the first page.");
        
        fullContent = result.content;
        setGeneratedContent(fullContent);

        let pagesGenerated = 1;
        while (pagesGenerated < settings.pageCount) {
          // Wait 6 seconds before the next batch request.
          await new Promise(resolve => setTimeout(resolve, 6000));

          const remainingPages = settings.pageCount - pagesGenerated;
          const pagesToGenerateInBatch = Math.min(10, remainingPages);
          const batchEndPage = pagesGenerated + pagesToGenerateInBatch;
          
          setChatHistory(prev => {
              const newHistory = [...prev];
              const lastMessage = newHistory[newHistory.length - 1];
              const progressMessage = `Generating pages ${pagesGenerated + 1} to ${batchEndPage} of ${settings.pageCount}...`;
              if (lastMessage.role === 'assistant' && lastMessage.content.startsWith('Generating page')) {
                  newHistory[newHistory.length - 1] = { role: 'assistant', content: progressMessage };
              } else {
                  newHistory.push({ role: 'assistant', content: progressMessage });
              }
              return newHistory;
          });

          const pages = fullContent.split('[PAGEBREAK]');
          const context = pages[pages.length - 1].slice(-1000);

          const continueInput: ContinueDocumentInput = { 
              topic, 
              writingStyle: settings.writingStyle, 
              context,
              pagesToGenerate: pagesToGenerateInBatch,
              culturalInflections: settings.culturalInflections,
          };
          const continueResult = await continueDocument(continueInput);

          if (!continueResult.content || continueResult.content.trim() === '') {
              toast({ variant: "destructive", title: "Generation Stopped", description: `The AI failed to generate pages starting from page ${pagesGenerated + 1}.` });
              break;
          }

          fullContent += "\n\n[PAGEBREAK]\n\n" + continueResult.content;
          setGeneratedContent(fullContent);
          // This is more robust in case the AI doesn't return exactly what was requested.
          pagesGenerated = fullContent.split('[PAGEBREAK]').length;
        }
        
        const finalMessage = { role: "assistant", content: `I have finished generating your document on "${topic}" using ${currentModel}. You can see the preview on the right.` };
        setChatHistory(prev => [...prev.slice(0, prev.findIndex(m => m.content.startsWith("Generating page"))), finalMessage]);

        const newRecord: DocumentRecord = { id: new Date().toISOString(), topic, content: fullContent, settings, createdAt: new Date() };
        setDocumentHistory(prev => [newRecord, ...prev]);

      } catch (error) {
        console.error(error);
        const errorMessage = error instanceof Error ? error.message : "An unknown error occurred.";
        toast({ variant: "destructive", title: "Generation Failed", description: errorMessage });
        setChatHistory(prev => [...prev.slice(0, 1), { role: "assistant", content: `I'm sorry, I couldn't generate the document. ${errorMessage}` }]);
      }
    });
  }, [chatInput, settings, toast, currentModel]);
  
  const handleSaveSettings = () => toast({ title: "Settings Saved", description: "Your new settings will be applied to the next document generation." });
  const handleSelectDocument = (doc: DocumentRecord) => {
    setGeneratedContent(doc.content);
    setCurrentTopic(doc.topic);
    setSettings(doc.settings);
    setChatHistory([]);
  };
  const handleNewDocument = () => {
    setGeneratedContent("");
    setCurrentTopic("");
    setChatHistory([]);
    setChatInput("");
  };

  const handleNewChat = () => {
    setChatHistory([]);
    setChatInput("");
  };

  const handleUploadDocument = async (file: File) => {
    try {
      const uploadedDoc = await documentUploadService.uploadDocument(file);
      setUploadedDocuments(prev => [uploadedDoc, ...prev]);
      toast({ title: "File Uploaded", description: `${file.name} has been uploaded successfully.` });
    } catch (error) {
      console.error('Error uploading document:', error);
      toast({ variant: "destructive", title: "Upload Failed", description: "Failed to upload the document." });
    }
  };

  const handleExportDocument = async (doc: DocumentRecord, format: 'pdf' | 'rtf' | 'latex' | 'txt') => {
    try {
      toast({ title: "Export", description: `Exporting document as ${format.toUpperCase()}...` });

      switch (format) {
        case 'pdf':
          await documentExportService.exportAsPDF(doc);
          break;
        case 'rtf':
          documentExportService.exportAsRTF(doc);
          break;
        case 'latex':
          documentExportService.exportAsLaTeX(doc);
          break;
        case 'txt':
          documentExportService.exportAsText(doc);
          break;
      }

      toast({ title: "Export Complete", description: `Document exported as ${format.toUpperCase()} successfully.` });
    } catch (error) {
      console.error('Export error:', error);
      toast({ variant: "destructive", title: "Export Failed", description: `Failed to export document as ${format.toUpperCase()}.` });
    }
  };

  const handleModelChange = (model: string) => {
    setCurrentModel(model);
    ollamaService.setCurrentModel(model);
    toast({ title: "Model Changed", description: `Switched to ${model}` });
  };

  const isPreviewLoading = isGenerating && !generatedContent;

  return (
    <div className="grid grid-cols-[320px_1fr] md:grid-cols-[320px_1fr_1.2fr] h-screen font-body text-foreground overflow-hidden">
      <EnhancedSidebar
        documentHistory={documentHistory}
        uploadedDocuments={uploadedDocuments}
        onSelectDocument={handleSelectDocument}
        onNewDocument={handleNewDocument}
        onNewChat={handleNewChat}
        onUploadDocument={handleUploadDocument}
        onExportDocument={handleExportDocument}
        currentModel={currentModel}
        onModelChange={handleModelChange}
      />
      <ChatView messages={chatHistory} chatInput={chatInput} setChatInput={setChatInput} handleSend={handleGenerate} isLoading={isGenerating} />
      <aside className="hidden md:flex flex-col h-full bg-card p-4 border-l min-w-0">
        <Tabs defaultValue="document" className="flex flex-col flex-1 min-h-0">
            <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="document">Document</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>
            <TabsContent value="document" className="flex-1 min-h-0 mt-4 overflow-y-auto">
                 <Card className="flex-1 flex flex-col min-h-0">
                    <CardHeader>
                        <div className="flex justify-between items-start">
                        <div>
                            <CardTitle className="font-headline">Document Preview</CardTitle>
                            <CardDescription>Generated content will appear here.</CardDescription>
                        </div>
                        </div>
                    </CardHeader>
                    <CardContent className="flex-1 min-h-0 bg-muted/30">
                        <ScrollArea className="h-full w-full">
                            {isPreviewLoading ? (
                                <div className="space-y-4 p-8">
                                    <Skeleton className="h-4 w-full" />
                                    <Skeleton className="h-4 w-full" />
                                    <Skeleton className="h-4 w-[80%]" />
                                </div>
                            ) : (
                                <DocumentPreview content={generatedContent} topic={currentTopic} settings={settings} />
                            )}
                        </ScrollArea>
                    </CardContent>
                </Card>
            </TabsContent>
            <TabsContent value="settings" className="flex-1 min-h-0 mt-4 overflow-y-auto">
                <SettingsPanel settings={settings} setSettings={setSettings} disabled={isGenerating} onSave={handleSaveSettings} />
            </TabsContent>
        </Tabs>
      </aside>
    </div>
  );
}
